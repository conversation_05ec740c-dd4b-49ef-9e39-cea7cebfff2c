import { supabaseHelpers } from '@/config/supabase';
import { kitchenService } from '@/services/kitchenService';
import { AdapterFactory } from '@/services/adapters/AdapterFactory';

export interface DebugInfo {
  environment: {
    adapterType: string;
    supabaseUrl: string;
    hasSupabaseKey: boolean;
  };
  supabaseConnection: {
    isInitialized: boolean;
    hasClient: boolean;
    connectionTest: any;
  };
  dataLocation: {
    hasLocalStorageData: boolean;
    hasSupabaseData: boolean;
    localStorageKeys: string[];
  };
  currentAdapter: string;
}

export const debugConnection = async (): Promise<DebugInfo> => {
  console.log('🔍 Starting connection debug...');

  // 1. Check environment variables
  const environment = {
    adapterType: import.meta.env.VITE_DATABASE_ADAPTER || 'not set',
    supabaseUrl: import.meta.env.VITE_SUPABASE_URL || 'not set',
    hasSupabaseKey: !!(import.meta.env.VITE_SUPABASE_ANON_KEY),
  };

  console.log('📋 Environment:', environment);

  // 2. Check Supabase connection
  const supabaseStatus = supabaseHelpers.getStatus();
  let connectionTest;
  
  try {
    connectionTest = await supabaseHelpers.testConnection();
  } catch (error) {
    connectionTest = { success: false, error: error.message };
  }

  const supabaseConnection = {
    isInitialized: supabaseStatus.isInitialized,
    hasClient: supabaseStatus.hasClient,
    connectionTest,
  };

  console.log('🔗 Supabase Connection:', supabaseConnection);

  // 3. Check data location
  const localStorageKeys = Object.keys(localStorage).filter(key => 
    key.startsWith('angiday_')
  );
  
  const hasLocalStorageData = localStorageKeys.length > 0;
  
  let hasSupabaseData = false;
  if (connectionTest.success) {
    try {
      const recipes = await kitchenService.getRecipes();
      hasSupabaseData = recipes.length > 0;
    } catch (error) {
      console.log('Could not check Supabase data:', error);
    }
  }

  const dataLocation = {
    hasLocalStorageData,
    hasSupabaseData,
    localStorageKeys,
  };

  console.log('💾 Data Location:', dataLocation);

  // 4. Determine current adapter
  let currentAdapter = 'unknown';
  try {
    const adapter = AdapterFactory.getAdapterFromEnv();
    currentAdapter = adapter.constructor.name;
  } catch (error) {
    console.error('Error determining adapter:', error);
  }

  console.log('🔧 Current Adapter:', currentAdapter);

  const debugInfo: DebugInfo = {
    environment,
    supabaseConnection,
    dataLocation,
    currentAdapter,
  };

  return debugInfo;
};

export const printDebugReport = async (): Promise<void> => {
  const info = await debugConnection();
  
  console.log('\n🔍 === CONNECTION DEBUG REPORT ===');
  console.log('\n📋 Environment Configuration:');
  console.log(`   Database Adapter: ${info.environment.adapterType}`);
  console.log(`   Supabase URL: ${info.environment.supabaseUrl}`);
  console.log(`   Has Supabase Key: ${info.environment.hasSupabaseKey}`);
  
  console.log('\n🔗 Supabase Connection Status:');
  console.log(`   Client Initialized: ${info.supabaseConnection.isInitialized}`);
  console.log(`   Has Client: ${info.supabaseConnection.hasClient}`);
  console.log(`   Connection Test: ${info.supabaseConnection.connectionTest.success ? '✅ SUCCESS' : '❌ FAILED'}`);
  
  if (!info.supabaseConnection.connectionTest.success) {
    console.log(`   Error Details:`, info.supabaseConnection.connectionTest.details || info.supabaseConnection.connectionTest.error);
  }
  
  console.log('\n💾 Data Location Analysis:');
  console.log(`   Has Local Storage Data: ${info.dataLocation.hasLocalStorageData ? '✅ YES' : '❌ NO'}`);
  console.log(`   Has Supabase Data: ${info.dataLocation.hasSupabaseData ? '✅ YES' : '❌ NO'}`);
  console.log(`   Local Storage Keys: ${info.dataLocation.localStorageKeys.join(', ') || 'none'}`);
  
  console.log('\n🔧 Current Adapter:');
  console.log(`   Active Adapter: ${info.currentAdapter}`);
  
  console.log('\n💡 Recommendations:');
  
  if (info.environment.adapterType === 'supabase' && !info.supabaseConnection.connectionTest.success) {
    console.log('   ⚠️ Supabase is configured but connection failed');
    console.log('   📝 Check if database tables are created');
    console.log('   🔧 Run: supabaseHelpers.setupDatabase()');
  }
  
  if (info.dataLocation.hasLocalStorageData && !info.dataLocation.hasSupabaseData) {
    console.log('   📦 Data is currently stored in localStorage');
    console.log('   🚀 Consider migrating to Supabase once connection is fixed');
  }
  
  if (info.currentAdapter === 'LocalStorageAdapter' && info.environment.adapterType === 'supabase') {
    console.log('   🔄 System has fallen back to localStorage due to Supabase issues');
  }
  
  console.log('\n=== END DEBUG REPORT ===\n');
};

// Helper function to setup Supabase database
export const setupSupabaseDatabase = async (): Promise<void> => {
  console.log('🔧 Setting up Supabase database...');
  
  try {
    const result = await supabaseHelpers.setupDatabase();
    console.log('Setup result:', result);
    
    if (result.success) {
      console.log('✅ Database setup completed successfully!');
      
      // Test connection again
      const testResult = await supabaseHelpers.testConnection();
      console.log('Post-setup connection test:', testResult);
    } else {
      console.log('❌ Database setup failed:', result.message);
    }
  } catch (error) {
    console.error('❌ Database setup error:', error);
  }
};

// Helper function to migrate data from localStorage to Supabase
export const migrateLocalDataToSupabase = async (): Promise<void> => {
  console.log('🚀 Starting data migration from localStorage to Supabase...');
  
  // This would need to be implemented based on your specific needs
  console.log('⚠️ Migration function not implemented yet');
  console.log('💡 You would need to:');
  console.log('   1. Read data from localStorage');
  console.log('   2. Transform it to Supabase format');
  console.log('   3. Insert into Supabase tables');
  console.log('   4. Verify migration success');
  console.log('   5. Optionally clear localStorage');
};

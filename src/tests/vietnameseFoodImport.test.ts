/**
 * Test suite cho Vietnamese Food Import System
 * Kiểm tra tính đúng đắn của dữ liệu và quá trình import
 */

import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import { vietnameseFoodImportService } from '../services/VietnameseFoodImportService';
import { vietnameseFoodScrapingService } from '../services/VietnameseFoodScrapingService';
import { allVietnameseDishes, getBreakfastDishes, getLunchDinnerDishes } from '../data/vietnameseFoodData';
import { vietnameseFoodCategories } from '../data/vietnameseFoodCategories';

describe('Vietnamese Food Data Validation', () => {
  describe('Data Structure Validation', () => {
    it('should have valid food categories', () => {
      expect(vietnameseFoodCategories).toBeDefined();
      expect(vietnameseFoodCategories.length).toBeGreaterThan(0);

      vietnameseFoodCategories.forEach(category => {
        expect(category.id).toBeDefined();
        expect(category.name).toBeDefined();
        expect(category.description).toBeDefined();
        expect(Array.isArray(category.mealTypes)).toBe(true);
        expect(typeof category.isStreetFood).toBe('boolean');
        expect(typeof category.isHomeCooking).toBe('boolean');
        expect(Array.isArray(category.commonTime)).toBe(true);
      });
    });

    it('should have valid Vietnamese dishes', () => {
      expect(allVietnameseDishes).toBeDefined();
      expect(allVietnameseDishes.length).toBeGreaterThan(0);

      allVietnameseDishes.forEach(dish => {
        // Required fields
        expect(dish.id).toBeDefined();
        expect(dish.name).toBeDefined();
        expect(dish.description).toBeDefined();
        expect(dish.categoryId).toBeDefined();
        expect(dish.region).toBeDefined();
        expect(dish.difficulty).toBeDefined();
        expect(dish.cookingTime).toBeDefined();
        expect(dish.servings).toBeGreaterThan(0);
        expect(Array.isArray(dish.ingredients)).toBe(true);
        expect(Array.isArray(dish.instructions)).toBe(true);
        expect(dish.nutrition).toBeDefined();
        expect(Array.isArray(dish.tags)).toBe(true);
        expect(dish.cost).toBeGreaterThan(0);
        expect(typeof dish.isPopular).toBe('boolean');
        expect(dish.mealType).toBeDefined();
        expect(dish.preparationComplexity).toBeDefined();

        // Validate enums
        expect(['north', 'central', 'south', 'nationwide']).toContain(dish.region);
        expect(['easy', 'medium', 'hard']).toContain(dish.difficulty);
        expect(['breakfast', 'lunch', 'dinner', 'snack', 'anytime']).toContain(dish.mealType);
        expect(['simple', 'moderate', 'complex']).toContain(dish.preparationComplexity);

        // Validate nutrition
        expect(dish.nutrition.calories).toBeGreaterThan(0);
        expect(dish.nutrition.protein).toBeGreaterThan(0);
        expect(dish.nutrition.carbs).toBeGreaterThan(0);
        expect(dish.nutrition.fat).toBeGreaterThan(0);
        expect(dish.nutrition.fiber).toBeGreaterThanOrEqual(0);

        // Validate ingredients and instructions
        expect(dish.ingredients.length).toBeGreaterThan(0);
        expect(dish.instructions.length).toBeGreaterThan(0);

        dish.ingredients.forEach(ingredient => {
          expect(typeof ingredient).toBe('string');
          expect(ingredient.trim().length).toBeGreaterThan(0);
        });

        dish.instructions.forEach(instruction => {
          expect(typeof instruction).toBe('string');
          expect(instruction.trim().length).toBeGreaterThan(0);
        });
      });
    });

    it('should properly categorize breakfast vs home cooking dishes', () => {
      const breakfastDishes = getBreakfastDishes();
      const homeCookingDishes = getLunchDinnerDishes();

      expect(breakfastDishes.length).toBeGreaterThan(0);
      expect(homeCookingDishes.length).toBeGreaterThan(0);

      // Check breakfast dishes are properly categorized
      breakfastDishes.forEach(dish => {
        expect(dish.mealType).toBe('breakfast');
        expect(['pho', 'bun', 'hu_tieu', 'mi', 'banh_mi', 'chao', 'xoi']).toContain(dish.categoryId);
      });

      // Check home cooking dishes are properly categorized
      homeCookingDishes.forEach(dish => {
        expect(['lunch', 'dinner']).toContain(dish.mealType);
        expect(['mon_chinh_thit', 'mon_ca', 'mon_tom_cua', 'canh', 'rau_cu', 'mon_kho', 'mon_rim']).toContain(dish.categoryId);
      });
    });

    it('should have unique dish IDs', () => {
      const ids = allVietnameseDishes.map(dish => dish.id);
      const uniqueIds = new Set(ids);
      expect(uniqueIds.size).toBe(ids.length);
    });

    it('should have valid category references', () => {
      const categoryIds = vietnameseFoodCategories.map(cat => cat.id);

      allVietnameseDishes.forEach(dish => {
        expect(categoryIds).toContain(dish.categoryId);
      });
    });
  });

  describe('Regional Distribution', () => {
    it('should have dishes from all regions', () => {
      const regions = ['north', 'central', 'south', 'nationwide'];
      const dishRegions = allVietnameseDishes.map(dish => dish.region);

      regions.forEach(region => {
        expect(dishRegions).toContain(region);
      });
    });

    it('should have proper regional specialties', () => {
      const northernDishes = allVietnameseDishes.filter(dish => dish.region === 'north');
      const centralDishes = allVietnameseDishes.filter(dish => dish.region === 'central');
      const southernDishes = allVietnameseDishes.filter(dish => dish.region === 'south');

      // Check for typical northern dishes
      expect(northernDishes.some(dish => dish.name.toLowerCase().includes('phở'))).toBe(true);
      expect(northernDishes.some(dish => dish.name.toLowerCase().includes('bún chả'))).toBe(true);

      // Check for typical central dishes
      expect(centralDishes.some(dish => dish.name.toLowerCase().includes('huế'))).toBe(true);

      // Check for typical southern dishes
      expect(southernDishes.some(dish => dish.name.toLowerCase().includes('hủ tiếu'))).toBe(true);
    });
  });

  describe('Nutritional Data Validation', () => {
    it('should have reasonable nutritional values', () => {
      allVietnameseDishes.forEach(dish => {
        // Calories should be reasonable for a meal
        expect(dish.nutrition.calories).toBeGreaterThan(50);
        expect(dish.nutrition.calories).toBeLessThan(1000);

        // Protein should be reasonable
        expect(dish.nutrition.protein).toBeGreaterThan(0);
        expect(dish.nutrition.protein).toBeLessThan(100);

        // Carbs should be reasonable
        expect(dish.nutrition.carbs).toBeGreaterThan(0);
        expect(dish.nutrition.carbs).toBeLessThan(150);

        // Fat should be reasonable
        expect(dish.nutrition.fat).toBeGreaterThan(0);
        expect(dish.nutrition.fat).toBeLessThan(50);

        // Fiber should be reasonable
        expect(dish.nutrition.fiber).toBeGreaterThanOrEqual(0);
        expect(dish.nutrition.fiber).toBeLessThan(20);
      });
    });

    it('should have balanced macronutrients', () => {
      allVietnameseDishes.forEach(dish => {
        const { protein, carbs, fat } = dish.nutrition;
        const totalMacros = protein + carbs + fat;

        // Each macronutrient should be at least 5% of total
        expect(protein / totalMacros).toBeGreaterThan(0.05);
        expect(carbs / totalMacros).toBeGreaterThan(0.05);
        expect(fat / totalMacros).toBeGreaterThan(0.05);
      });
    });
  });
});

describe('Vietnamese Food Import Service', () => {
  beforeEach(async () => {
    // Clear any existing test data
    await vietnameseFoodImportService.clearImportedData();
  });

  afterEach(async () => {
    // Clean up after tests
    await vietnameseFoodImportService.clearImportedData();
  });

  describe('Local Data Import', () => {
    it('should import local data successfully', async () => {
      const result = await vietnameseFoodImportService.importLocalData({
        batchSize: 5,
        validateData: true,
        skipDuplicates: true
      });

      expect(result.success).toBe(true);
      expect(result.totalProcessed).toBeGreaterThan(0);
      expect(result.totalImported).toBeGreaterThan(0);
      expect(result.totalErrors).toBe(0);
      expect(result.duration).toBeGreaterThan(0);
    }, 30000); // 30 second timeout

    it('should handle validation errors gracefully', async () => {
      const result = await vietnameseFoodImportService.importLocalData({
        validateData: true,
        skipDuplicates: false
      });

      // Even with validation, should not fail completely
      expect(result.totalProcessed).toBeGreaterThan(0);
      expect(Array.isArray(result.errors)).toBe(true);
    });

    it('should skip duplicates when enabled', async () => {
      // First import
      const firstResult = await vietnameseFoodImportService.importLocalData({
        skipDuplicates: false
      });

      // Second import with skip duplicates
      const secondResult = await vietnameseFoodImportService.importLocalData({
        skipDuplicates: true
      });

      expect(secondResult.totalSkipped).toBeGreaterThan(0);
    }, 60000);
  });

  describe('Scraping Service', () => {
    it('should have supported sources', () => {
      const sources = vietnameseFoodScrapingService.getSupportedSources();
      expect(Array.isArray(sources)).toBe(true);
      expect(sources.length).toBeGreaterThan(0);
      expect(sources).toContain('Cooky.vn');
      expect(sources).toContain('Foody.vn');
      expect(sources).toContain('MonNgon.vn');
    });

    it('should validate source support', () => {
      expect(vietnameseFoodScrapingService.isSourceSupported('Cooky.vn')).toBe(true);
      expect(vietnameseFoodScrapingService.isSourceSupported('InvalidSource')).toBe(false);
    });

    it('should get source info', () => {
      const sourceInfo = vietnameseFoodScrapingService.getSourceInfo('Cooky.vn');
      expect(sourceInfo).toBeDefined();
      expect(sourceInfo?.name).toBe('Cooky.vn');
      expect(sourceInfo?.baseUrl).toBeDefined();
      expect(sourceInfo?.selectors).toBeDefined();
      expect(sourceInfo?.rateLimit).toBeGreaterThan(0);
    });

    it('should scrape mock data from supported source', async () => {
      const result = await vietnameseFoodScrapingService.scrapeFromSource('Cooky.vn', {
        maxPages: 1
      });

      expect(result.success).toBe(true);
      expect(result.source).toBe('Cooky.vn');
      expect(result.data).toBeDefined();
      expect(Array.isArray(result.data)).toBe(true);
      expect(result.scrapedAt).toBeInstanceOf(Date);
    }, 10000);

    it('should handle unsupported source', async () => {
      const result = await vietnameseFoodScrapingService.scrapeFromSource('InvalidSource');

      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
      expect(result.source).toBe('InvalidSource');
    });
  });

  describe('Import Statistics', () => {
    it('should provide accurate statistics', async () => {
      // Import some data first
      await vietnameseFoodImportService.importLocalData({
        batchSize: 10
      });

      const stats = await vietnameseFoodImportService.getImportStats();

      expect(stats.totalRecipes).toBeGreaterThan(0);
      expect(typeof stats.byCategory).toBe('object');
      expect(typeof stats.byRegion).toBe('object');
      expect(typeof stats.byMealType).toBe('object');
    }, 30000);

    it('should track categories correctly', async () => {
      await vietnameseFoodImportService.importLocalData();
      const stats = await vietnameseFoodImportService.getImportStats();

      // Should have breakfast dishes
      const breakfastCount = stats.byMealType['breakfast'] || 0;
      expect(breakfastCount).toBeGreaterThan(0);

      // Should have lunch/dinner dishes
      const lunchCount = stats.byMealType['lunch'] || 0;
      expect(lunchCount).toBeGreaterThan(0);
    }, 30000);
  });

  describe('Data Cleanup', () => {
    it('should clear imported data successfully', async () => {
      // Import some data first
      await vietnameseFoodImportService.importLocalData({
        batchSize: 5
      });

      // Clear the data
      const clearResult = await vietnameseFoodImportService.clearImportedData();

      expect(clearResult.deleted).toBeGreaterThan(0);
      expect(Array.isArray(clearResult.errors)).toBe(true);

      // Verify data is cleared
      const stats = await vietnameseFoodImportService.getImportStats();
      expect(stats.totalRecipes).toBe(0);
    }, 30000);
  });
});

describe('Integration Tests', () => {
  it('should handle complete import workflow', async () => {
    // Clear existing data
    await vietnameseFoodImportService.clearImportedData();

    // Import local data
    const localResult = await vietnameseFoodImportService.importLocalData({
      batchSize: 5,
      validateData: true,
      skipDuplicates: true
    });

    expect(localResult.success).toBe(true);
    expect(localResult.totalImported).toBeGreaterThan(0);

    // Get stats
    const stats = await vietnameseFoodImportService.getImportStats();
    expect(stats.totalRecipes).toBe(localResult.totalImported);

    // Try to import again (should skip duplicates)
    const duplicateResult = await vietnameseFoodImportService.importLocalData({
      skipDuplicates: true
    });

    expect(duplicateResult.totalSkipped).toBeGreaterThan(0);

    // Clear data
    const clearResult = await vietnameseFoodImportService.clearImportedData();
    expect(clearResult.deleted).toBeGreaterThan(0);
  }, 60000);

  it('should maintain data integrity across operations', async () => {
    // Import data
    const importResult = await vietnameseFoodImportService.importLocalData({
      validateData: true
    });

    expect(importResult.success).toBe(true);

    // Verify all imported dishes maintain their properties
    const stats = await vietnameseFoodImportService.getImportStats();
    expect(stats.totalRecipes).toBe(importResult.totalImported);

    // Check that we have both breakfast and home cooking dishes
    expect(stats.byMealType['breakfast']).toBeGreaterThan(0);
    expect(stats.byMealType['lunch']).toBeGreaterThan(0);

    // Check regional distribution
    expect(Object.keys(stats.byRegion).length).toBeGreaterThan(1);
  }, 45000);
});
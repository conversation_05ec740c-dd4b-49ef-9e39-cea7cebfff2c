import { VietnameseDish } from './vietnameseFoodCategories';

/**
 * <PERSON><PERSON> liệu mẫu các món ăn Việt Nam được phân loại
 * Phân biệt rõ ràng giữa món ăn sáng/ngoài hàng và món ăn cơm hàng ngày
 */

// NHÓM 1: MÓN ĂN SÁNG / NGOÀI HÀNG
export const breakfastStreetFoods: VietnameseDish[] = [
  // PHỞ
  {
    id: 'pho_bo_ha_noi',
    name: 'Phở bò Hà Nội',
    description: 'Phở bò truyền thống Hà Nội với nước dùng trong vắt, thơm ngon',
    categoryId: 'pho',
    region: 'north',
    difficulty: 'hard',
    cookingTime: '3 giờ',
    servings: 6,
    ingredients: [
      '1kg xương ống bò',
      '500g thịt bò',
      '300g bánh phở',
      '2 củ hành tây',
      '1 củ gừng',
      '<PERSON>u<PERSON>, hồi, đinh hương',
      'Nước mắm, muối, đường',
      '<PERSON><PERSON><PERSON> lá, ngò gai, gi<PERSON> đỗ'
    ],
    instructions: [
      'Ninh xương bò 3 tiếng để có nước dùng trong',
      'Nướng hành tây, gừng thơm',
      'Cho gia vị vào túi vải, thả vào nồi',
      'Thái thịt bò mỏng',
      'Trần bánh phở qua nước sôi',
      'Múc phở vào tô, chan nước dùng nóng'
    ],
    nutrition: { calories: 350, protein: 25, carbs: 40, fat: 8, fiber: 3 },
    tags: ['Phở', 'Truyền thống', 'Hà Nội', 'Nước dùng'],
    cost: 60000,
    isPopular: true,
    mealType: 'breakfast',
    preparationComplexity: 'complex'
  },
  {
    id: 'pho_ga',
    name: 'Phở gà',
    description: 'Phở gà thanh đạm với nước dùng ngọt từ xương gà',
    categoryId: 'pho',
    region: 'nationwide',
    difficulty: 'medium',
    cookingTime: '2 giờ',
    servings: 4,
    ingredients: [
      '1 con gà ta',
      '300g bánh phở',
      '1 củ hành tây',
      '1 củ gừng',
      'Quế, hồi',
      'Nước mắm, muối',
      'Hành lá, ngò rí'
    ],
    instructions: [
      'Luộc gà lấy nước dùng',
      'Xé thịt gà thành sợi',
      'Nướng hành tây, gừng',
      'Nấu nước dùng với gia vị',
      'Trần bánh phở',
      'Bày phở ra tô, chan nước dùng'
    ],
    nutrition: { calories: 320, protein: 22, carbs: 38, fat: 6, fiber: 2 },
    tags: ['Phở', 'Gà', 'Thanh đạm', 'Dễ tiêu'],
    cost: 50000,
    isPopular: true,
    mealType: 'breakfast',
    preparationComplexity: 'moderate'
  },

  // BÚN
  {
    id: 'bun_cha_ha_noi',
    name: 'Bún chả Hà Nội',
    description: 'Bún chả truyền thống với chả nướng thơm và nước mắm chua ngọt',
    categoryId: 'bun',
    region: 'north',
    difficulty: 'medium',
    cookingTime: '40 phút',
    servings: 4,
    ingredients: [
      '400g thịt heo xay',
      '200g thịt ba chỉ',
      '300g bún tươi',
      'Nước mắm, đường, giấm',
      'Tỏi, ớt',
      'Rau thơm, dưa chua'
    ],
    instructions: [
      'Nặn thịt xay thành viên tròn',
      'Thái thịt ba chỉ miếng dài',
      'Nướng chả trên bếp than',
      'Pha nước mắm chua ngọt',
      'Trần bún qua nước sôi',
      'Ăn kèm với rau thơm và dưa chua'
    ],
    nutrition: { calories: 380, protein: 22, carbs: 35, fat: 18, fiber: 4 },
    tags: ['Bún', 'Nướng', 'Hà Nội', 'Chua ngọt'],
    cost: 55000,
    isPopular: true,
    mealType: 'breakfast',
    preparationComplexity: 'moderate'
  },
  {
    id: 'bun_bo_hue',
    name: 'Bún bò Huế',
    description: 'Bún bò Huế cay nồng với hương vị đặc trưng xứ Huế',
    categoryId: 'bun',
    region: 'central',
    difficulty: 'hard',
    cookingTime: '2 giờ',
    servings: 6,
    ingredients: [
      '500g xương heo',
      '300g thịt bò',
      '200g chả cua',
      '300g bún bò',
      'Sả, gừng, hành tím',
      'Mắm ruốc, tôm khô',
      'Ớt, tiêu, muối'
    ],
    instructions: [
      'Ninh xương heo 2 tiếng',
      'Phi thơm sả, gừng, hành tím',
      'Cho mắm ruốc, tôm khô vào xào',
      'Đổ nước dùng vào nấu',
      'Luộc thịt bò, chả cua',
      'Trần bún, múc vào tô'
    ],
    nutrition: { calories: 360, protein: 24, carbs: 38, fat: 12, fiber: 4 },
    tags: ['Bún', 'Cay', 'Huế', 'Đặc sản'],
    cost: 70000,
    isPopular: true,
    mealType: 'breakfast',
    preparationComplexity: 'complex'
  },
  {
    id: 'bun_rieu_cua',
    name: 'Bún riêu cua',
    description: 'Bún riêu cua chua ngọt với cua đồng và cà chua',
    categoryId: 'bun',
    region: 'nationwide',
    difficulty: 'medium',
    cookingTime: '1 giờ',
    servings: 4,
    ingredients: [
      '300g bún tươi',
      '300g cua đồng',
      '3 quả cà chua',
      '200g thịt heo xay',
      'Mắm ruốc, giấm',
      'Rau thơm, bạc hà'
    ],
    instructions: [
      'Cua giã lấy nước',
      'Cà chua xào thơm',
      'Nấu nước dùng từ cua',
      'Cho thịt viên vào nấu',
      'Trần bún ra tô',
      'Chan nước dùng chua ngọt'
    ],
    nutrition: { calories: 280, protein: 20, carbs: 32, fat: 8, fiber: 3 },
    tags: ['Bún riêu', 'Cua', 'Chua ngọt', 'Thanh mát'],
    cost: 55000,
    isPopular: true,
    mealType: 'breakfast',
    preparationComplexity: 'moderate'
  },

  // HỦ TIẾU
  {
    id: 'hu_tieu_nam_vang',
    name: 'Hủ tiếu Nam Vang',
    description: 'Hủ tiếu Nam Vang trong vắt với tôm thịt và gan heo',
    categoryId: 'hu_tieu',
    region: 'south',
    difficulty: 'medium',
    cookingTime: '1 giờ',
    servings: 4,
    ingredients: [
      '300g hủ tiếu',
      '200g tôm',
      '200g thịt heo',
      '100g gan heo',
      'Xương heo, cua đồng',
      'Hành lá, ngò gai'
    ],
    instructions: [
      'Ninh xương heo và cua',
      'Luộc tôm, thịt, gan',
      'Trần hủ tiếu mềm',
      'Múc hủ tiếu vào tô',
      'Xếp tôm thịt lên trên',
      'Chan nước dùng trong'
    ],
    nutrition: { calories: 320, protein: 24, carbs: 35, fat: 10, fiber: 2 },
    tags: ['Hủ tiếu', 'Nam Vang', 'Trong vắt', 'Tôm thịt'],
    cost: 65000,
    isPopular: true,
    mealType: 'breakfast',
    preparationComplexity: 'moderate'
  }
];

// NHÓM 2: MÓN ĂN CƠM HÀNG NGÀY
export const homeCookingMeals: VietnameseDish[] = [
  // MÓN CHÍNH THỊT
  {
    id: 'ga_rang_gung',
    name: 'Gà rang gừng',
    description: 'Món gà rang gừng thơm ngon, đậm đà hương vị truyền thống',
    categoryId: 'mon_chinh_thit',
    region: 'nationwide',
    difficulty: 'easy',
    cookingTime: '30 phút',
    servings: 4,
    ingredients: [
      '1 con gà ta (khoảng 1.2kg)',
      '50g gừng tươi',
      '3 củ hành tím',
      '2 thìa canh nước mắm',
      '1 thìa canh đường',
      '1 thìa cà phê tiêu',
      '2 thìa canh dầu ăn',
      'Rau thơm: ngò, hành lá'
    ],
    instructions: [
      'Gà rửa sạch, chặt miếng vừa ăn',
      'Gừng cạo vỏ, thái lát mỏng',
      'Hành tím bóc vỏ, thái lát',
      'Ướp gà với nước mắm, đường, tiêu trong 15 phút',
      'Đun chảo với dầu, cho gừng và hành tím vào phi thơm',
      'Cho gà vào rang đều tay đến khi chín vàng',
      'Nêm nếm lại gia vị, rắc rau thơm lên trên'
    ],
    nutrition: { calories: 280, protein: 25, carbs: 8, fat: 15, fiber: 2 },
    tags: ['Món chính', 'Gia đình', 'Dễ làm', 'Truyền thống'],
    cost: 65000,
    isPopular: true,
    mealType: 'lunch',
    preparationComplexity: 'simple'
  },
  {
    id: 'thit_kho_trung_cut',
    name: 'Thịt kho trứng cút',
    description: 'Thịt kho trứng cút đậm đà, thấm vị, món ngon truyền thống',
    categoryId: 'mon_kho',
    region: 'nationwide',
    difficulty: 'medium',
    cookingTime: '40 phút',
    servings: 4,
    ingredients: [
      '500g thịt ba chỉ',
      '20 quả trứng cút',
      '3 thìa canh nước mắm',
      '2 thìa canh đường',
      '1 thìa cà phê tiêu',
      '3 củ hành tím',
      'Nước dừa tươi'
    ],
    instructions: [
      'Thịt thái miếng vuông vừa ăn',
      'Trứng cút luộc chín, bóc vỏ',
      'Ướp thịt với gia vị 20 phút',
      'Rang thịt đến khi săn lại',
      'Cho nước dừa vào kho 25 phút',
      'Cho trứng cút vào kho thêm 10 phút'
    ],
    nutrition: { calories: 320, protein: 22, carbs: 10, fat: 20, fiber: 1 },
    tags: ['Món chính', 'Truyền thống', 'Đậm đà', 'Kho'],
    cost: 70000,
    isPopular: true,
    mealType: 'lunch',
    preparationComplexity: 'moderate'
  },
  {
    id: 'dau_phu_nhoi_thit',
    name: 'Đậu phụ nhồi thịt',
    description: 'Đậu phụ nhồi thịt mềm ngon, giàu protein và tiết kiệm',
    categoryId: 'mon_chinh_thit',
    region: 'nationwide',
    difficulty: 'easy',
    cookingTime: '25 phút',
    servings: 4,
    ingredients: [
      '4 miếng đậu phụ',
      '200g thịt heo xay',
      '2 củ hành tím',
      '1 thìa canh nước mắm',
      '1/2 thìa cà phê đường',
      '1/4 thìa cà phê tiêu',
      '1 quả trứng gà',
      'Hành lá, ngò rí'
    ],
    instructions: [
      'Đậu phụ rửa sạch, cắt đôi, khoét ruột tạo hốc',
      'Thịt xay trộn với hành tím băm, nước mắm, đường, tiêu',
      'Nhồi thịt vào hốc đậu phụ',
      'Chiên đậu phụ nhồi thịt đến khi vàng đều',
      'Đun nước dùng, cho đậu phụ vào om 10 phút',
      'Nêm nếm gia vị, rắc hành lá'
    ],
    nutrition: { calories: 220, protein: 18, carbs: 12, fat: 12, fiber: 4 },
    tags: ['Món chính', 'Tiết kiệm', 'Bổ dưỡng', 'Protein'],
    cost: 45000,
    isPopular: true,
    mealType: 'dinner',
    preparationComplexity: 'simple'
  },

  // MÓN CÁ
  {
    id: 'ca_thu_nuong_la_chuoi',
    name: 'Cá thu nướng lá chuối',
    description: 'Cá thu nướng lá chuối thơm lừng, bổ dưỡng và hấp dẫn',
    categoryId: 'mon_ca',
    region: 'nationwide',
    difficulty: 'medium',
    cookingTime: '35 phút',
    servings: 4,
    ingredients: [
      '1 con cá thu (800g)',
      'Lá chuối tươi',
      '3 thìa canh nước mắm',
      '1 thìa canh dầu điều',
      '2 thìa cà phê đường',
      'Sả, gừng, tỏi',
      'Ớt, tiêu'
    ],
    instructions: [
      'Cá thu làm sạch, rạch dao',
      'Ướp cá với gia vị 30 phút',
      'Lá chuối rửa sạch, lau khô',
      'Gói cá trong lá chuối',
      'Nướng trên than hồng 30 phút',
      'Lật đều để cá chín đều'
    ],
    nutrition: { calories: 250, protein: 28, carbs: 5, fat: 12, fiber: 1 },
    tags: ['Cá', 'Nướng', 'Thơm ngon', 'Omega-3'],
    cost: 80000,
    isPopular: true,
    mealType: 'dinner',
    preparationComplexity: 'moderate'
  },

  // CANH
  {
    id: 'canh_suon_non_ham_rau_cu',
    name: 'Canh sườn non hầm rau củ',
    description: 'Canh sườn non ngọt thanh với nhiều rau củ bổ dưỡng',
    categoryId: 'canh',
    region: 'nationwide',
    difficulty: 'easy',
    cookingTime: '45 phút',
    servings: 4,
    ingredients: [
      '500g sườn non',
      '1 củ cà rót',
      '1 củ su hào',
      '2 quả cà chua',
      '1 củ hành tây',
      'Ngò rí, hành lá',
      'Muối, tiêu, nước mắm'
    ],
    instructions: [
      'Sườn non rửa sạch, chặt khúc',
      'Luộc sơ sườn để loại bỏ tạp chất',
      'Rau củ gọt vỏ, thái miếng to',
      'Hầm sườn với nước trong 30 phút',
      'Cho rau củ vào hầm thêm 15 phút',
      'Nêm gia vị, rắc rau thơm'
    ],
    nutrition: { calories: 180, protein: 15, carbs: 18, fat: 6, fiber: 5 },
    tags: ['Canh', 'Bổ dưỡng', 'Rau củ', 'Thanh mát'],
    cost: 55000,
    isPopular: true,
    mealType: 'lunch',
    preparationComplexity: 'simple'
  },

  // RAU CỦ
  {
    id: 'rau_muong_xao_toi',
    name: 'Rau muống xào tỏi',
    description: 'Rau muống xào tỏi giòn ngon, nhanh gọn và bổ dưỡng',
    categoryId: 'rau_cu',
    region: 'nationwide',
    difficulty: 'easy',
    cookingTime: '10 phút',
    servings: 4,
    ingredients: [
      '500g rau muống',
      '4 tép tỏi',
      '2 thìa canh dầu ăn',
      '1 thìa cà phê muối',
      '1/2 thìa cà phê đường',
      'Nước mắm'
    ],
    instructions: [
      'Rau muống nhặt sạch, rửa nhiều lần',
      'Tỏi băm nhuyễn',
      'Đun chảo với dầu, phi tỏi thơm',
      'Cho rau muống vào xào nhanh tay',
      'Nêm gia vị, xào đều trong 3 phút',
      'Tắt bếp, múc ra đĩa'
    ],
    nutrition: { calories: 80, protein: 3, carbs: 8, fat: 4, fiber: 3 },
    tags: ['Rau', 'Nhanh', 'Tiết kiệm', 'Vitamin'],
    cost: 15000,
    isPopular: true,
    mealType: 'lunch',
    preparationComplexity: 'simple'
  },

  // MÓN ĂN TỐI
  {
    id: 'ca_kho_to',
    name: 'Cá kho tộ',
    description: 'Cá kho tộ đậm đà, thơm ngon với nước dừa và nước mắm',
    categoryId: 'mon_ca',
    region: 'south',
    difficulty: 'medium',
    cookingTime: '45 phút',
    servings: 4,
    ingredients: [
      '1 con cá lóc (800g)',
      '3 thìa canh nước mắm',
      '2 thìa canh đường',
      '200ml nước dừa',
      '3 củ hành tím',
      '2 tép tỏi',
      'Tiêu, ớt',
      'Hành lá'
    ],
    instructions: [
      'Cá lóc cắt khúc, ướp gia vị 30 phút',
      'Phi thơm hành tím, tỏi',
      'Cho cá vào kho với nước dừa',
      'Kho lửa nhỏ 30 phút đến khi thấm vị',
      'Nêm nếm lại gia vị',
      'Rắc hành lá lên trên'
    ],
    nutrition: { calories: 280, protein: 26, carbs: 8, fat: 16, fiber: 1 },
    tags: ['Món chính', 'Kho', 'Miền Nam', 'Đậm đà'],
    cost: 75000,
    isPopular: true,
    mealType: 'dinner',
    preparationComplexity: 'moderate'
  },
  {
    id: 'suon_xao_chua_ngot',
    name: 'Sườn xào chua ngọt',
    description: 'Sườn heo xào chua ngọt với dứa và cà chua',
    categoryId: 'mon_chinh_thit',
    region: 'nationwide',
    difficulty: 'medium',
    cookingTime: '35 phút',
    servings: 4,
    ingredients: [
      '500g sườn heo',
      '1/2 quả dứa',
      '2 quả cà chua',
      '1 quả ớt chuông',
      'Tỏi, hành tây',
      'Nước mắm, đường, giấm',
      'Bột năng'
    ],
    instructions: [
      'Sườn thái miếng, ướp gia vị',
      'Dứa, cà chua thái miếng',
      'Chiên sườn đến khi vàng',
      'Xào thơm hành tây, tỏi',
      'Cho sườn và trái cây vào xào',
      'Nêm chua ngọt vừa miệng'
    ],
    nutrition: { calories: 320, protein: 24, carbs: 18, fat: 18, fiber: 3 },
    tags: ['Món chính', 'Chua ngọt', 'Hấp dẫn', 'Gia đình'],
    cost: 68000,
    isPopular: true,
    mealType: 'dinner',
    preparationComplexity: 'moderate'
  },
  {
    id: 'canh_chua_ca_bong_lau',
    name: 'Canh chua cá bông lau',
    description: 'Canh chua miền Tây với cá bông lau và rau thơm',
    categoryId: 'canh',
    region: 'south',
    difficulty: 'easy',
    cookingTime: '30 phút',
    servings: 4,
    ingredients: [
      '500g cá bông lau',
      '2 quả cà chua',
      '100g dứa',
      '100g giá đỗ',
      'Me, đường, nước mắm',
      'Ngò gai, rau răm',
      'Ớt'
    ],
    instructions: [
      'Cá rửa sạch, cắt khúc',
      'Nấu nước dùng từ xương cá',
      'Cho me, cà chua vào nấu',
      'Thêm cá và dứa',
      'Nêm chua ngọt vừa miệng',
      'Cho rau thơm vào cuối'
    ],
    nutrition: { calories: 150, protein: 18, carbs: 12, fat: 4, fiber: 3 },
    tags: ['Canh', 'Chua', 'Miền Tây', 'Thanh mát'],
    cost: 45000,
    isPopular: true,
    mealType: 'dinner',
    preparationComplexity: 'simple'
  },
  {
    id: 'thit_nuong_la_lot',
    name: 'Thịt nướng lá lốt',
    description: 'Thịt heo cuốn lá lốt nướng thơm lừng',
    categoryId: 'mon_chinh_thit',
    region: 'south',
    difficulty: 'medium',
    cookingTime: '40 phút',
    servings: 4,
    ingredients: [
      '400g thịt heo xay',
      '30 lá lốt',
      '2 thìa canh nước mắm',
      '1 thìa canh dầu điều',
      'Tỏi, hành tím',
      'Đường, tiêu',
      'Bánh tráng, rau sống'
    ],
    instructions: [
      'Thịt xay trộn với gia vị',
      'Lá lốt rửa sạch, lau khô',
      'Cuốn thịt trong lá lốt',
      'Nướng trên bếp than',
      'Nướng đều 2 mặt',
      'Ăn kèm bánh tráng và rau sống'
    ],
    nutrition: { calories: 260, protein: 22, carbs: 6, fat: 16, fiber: 2 },
    tags: ['Nướng', 'Lá lốt', 'Miền Nam', 'Thơm ngon'],
    cost: 58000,
    isPopular: true,
    mealType: 'dinner',
    preparationComplexity: 'moderate'
  }
];

// NHÓM 3: MÓN ĂN VẶT / TRÁNG MIỆNG
export const snackFoods: VietnameseDish[] = [
  {
    id: 'banh_xeo',
    name: 'Bánh xèo',
    description: 'Bánh xèo giòn rụm với nhân tôm thịt và giá đỗ',
    categoryId: 'an_vat',
    region: 'south',
    difficulty: 'medium',
    cookingTime: '35 phút',
    servings: 4,
    ingredients: [
      '300g bột bánh xèo',
      '200g tôm',
      '200g thịt heo',
      '200g giá đỗ',
      'Nước cốt dừa',
      'Nghệ, hành lá',
      'Rau sống, bánh tráng'
    ],
    instructions: [
      'Pha bột bánh xèo với nước cốt dừa',
      'Tôm bóc vỏ, thịt thái mỏng',
      'Đun chảo nóng, quết dầu',
      'Đổ bột tạo lớp mỏng',
      'Cho nhân vào, đậy nắp',
      'Gấp đôi khi bánh chín vàng'
    ],
    nutrition: { calories: 290, protein: 18, carbs: 32, fat: 12, fiber: 3 },
    tags: ['Bánh', 'Giòn', 'Miền Nam', 'Tôm thịt'],
    cost: 65000,
    isPopular: true,
    mealType: 'snack',
    preparationComplexity: 'moderate'
  },
  {
    id: 'che_ba_mau',
    name: 'Chè ba màu',
    description: 'Chè ba màu mát lạnh với đậu đỏ, đậu xanh và thạch',
    categoryId: 'che_trang_mieng',
    region: 'nationwide',
    difficulty: 'easy',
    cookingTime: '45 phút',
    servings: 4,
    ingredients: [
      '100g đậu đỏ',
      '100g đậu xanh',
      '100g thạch rau câu',
      'Nước cốt dừa',
      'Đường, muối',
      'Đá bào'
    ],
    instructions: [
      'Nấu đậu đỏ, đậu xanh mềm',
      'Làm thạch rau câu',
      'Pha nước cốt dừa ngọt',
      'Xếp lớp đậu vào ly',
      'Thêm thạch và nước cốt dừa',
      'Cho đá bào lên trên'
    ],
    nutrition: { calories: 180, protein: 6, carbs: 35, fat: 4, fiber: 5 },
    tags: ['Chè', 'Mát lạnh', 'Tráng miệng', 'Ba màu'],
    cost: 25000,
    isPopular: true,
    mealType: 'snack',
    preparationComplexity: 'simple'
  }
];

// Tổng hợp tất cả món ăn
export const allVietnameseDishes: VietnameseDish[] = [
  ...breakfastStreetFoods,
  ...homeCookingMeals,
  ...snackFoods
];

// Helper functions để lọc món ăn theo loại
export const getDishesByCategory = (categoryId: string): VietnameseDish[] => {
  return allVietnameseDishes.filter(dish => dish.categoryId === categoryId);
};

export const getDishesByMealType = (mealType: string): VietnameseDish[] => {
  return allVietnameseDishes.filter(dish => dish.mealType === mealType);
};

export const getDishesByRegion = (region: string): VietnameseDish[] => {
  return allVietnameseDishes.filter(dish => dish.region === region || dish.region === 'nationwide');
};

export const getBreakfastDishes = (): VietnameseDish[] => {
  return allVietnameseDishes.filter(dish => dish.mealType === 'breakfast');
};

export const getLunchDinnerDishes = (): VietnameseDish[] => {
  return allVietnameseDishes.filter(dish => dish.mealType === 'lunch' || dish.mealType === 'dinner');
};

export const getSnackDishes = (): VietnameseDish[] => {
  return allVietnameseDishes.filter(dish => dish.mealType === 'snack');
};

export const getPopularDishes = (): VietnameseDish[] => {
  return allVietnameseDishes.filter(dish => dish.isPopular);
};

export const getDishesByDifficulty = (difficulty: string): VietnameseDish[] => {
  return allVietnameseDishes.filter(dish => dish.difficulty === difficulty);
};

export const getDishesByCostRange = (minCost: number, maxCost: number): VietnameseDish[] => {
  return allVietnameseDishes.filter(dish => dish.cost >= minCost && dish.cost <= maxCost);
};
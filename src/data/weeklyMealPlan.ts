/**
 * Weekly Meal Plan Data Structure
 */

export interface MealSlot {
  id: string;
  dishId?: string;
  dishName?: string;
  estimatedCost?: number;
  cookingTime?: string;
  servings?: number;
  isFromTemplate?: boolean;
  templateId?: string;
}

export interface DayMealPlan {
  date: string;
  dayOfWeek: string;
  breakfast?: MealSlot;
  lunch?: MealSlot;
  dinner?: MealSlot;
  snack?: MealSlot;
}

export interface WeeklyMealPlan {
  id: string;
  name: string;
  startDate: string;
  endDate: string;
  days: DayMealPlan[];
  totalEstimatedCost: number;
  totalDishes: number;
  appliedTemplateId?: string;
  createdAt: string;
  updatedAt: string;
  notes?: string;
  tags: string[];
}

export interface WeeklyPlanSummary {
  totalCost: number;
  totalDishes: number;
  totalCookingTime: number;
  nutritionSummary: {
    calories: number;
    protein: number;
    carbs: number;
    fat: number;
    fiber: number;
  };
  shoppingList: {
    category: string;
    items: {
      name: string;
      totalQuantity: number;
      unit: string;
      estimatedPrice: number;
      usedInDishes: string[];
    }[];
  }[];
  missingSlots: {
    day: string;
    mealType: string;
  }[];
}

// Helper functions
export const createEmptyWeekPlan = (startDate: Date): WeeklyMealPlan => {
  const days: DayMealPlan[] = [];
  const dayNames = ['Chủ nhật', 'Thứ hai', 'Thứ ba', 'Thứ tư', 'Thứ năm', 'Thứ sáu', 'Thứ bảy'];
  
  for (let i = 0; i < 7; i++) {
    const currentDate = new Date(startDate);
    currentDate.setDate(startDate.getDate() + i);
    
    days.push({
      date: currentDate.toISOString().split('T')[0],
      dayOfWeek: dayNames[currentDate.getDay()],
      breakfast: { id: `breakfast-${i}` },
      lunch: { id: `lunch-${i}` },
      dinner: { id: `dinner-${i}` },
      snack: { id: `snack-${i}` }
    });
  }

  const endDate = new Date(startDate);
  endDate.setDate(startDate.getDate() + 6);

  return {
    id: `week-${Date.now()}`,
    name: `Thực đơn tuần ${startDate.toLocaleDateString('vi-VN')}`,
    startDate: startDate.toISOString().split('T')[0],
    endDate: endDate.toISOString().split('T')[0],
    days,
    totalEstimatedCost: 0,
    totalDishes: 0,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    tags: []
  };
};

export const generateMealSlotId = (day: number, mealType: string): string => {
  return `${mealType}-day-${day}-${Date.now()}`;
};

export const calculateWeekPlanSummary = (
  weekPlan: WeeklyMealPlan,
  dishesData: any[]
): WeeklyPlanSummary => {
  let totalCost = 0;
  let totalDishes = 0;
  let totalCookingTime = 0;
  const nutritionSummary = { calories: 0, protein: 0, carbs: 0, fat: 0, fiber: 0 };
  const missingSlots: { day: string; mealType: string }[] = [];
  
  // Calculate totals from assigned meals
  weekPlan.days.forEach(day => {
    ['breakfast', 'lunch', 'dinner', 'snack'].forEach(mealType => {
      const meal = day[mealType as keyof DayMealPlan] as MealSlot;
      if (meal?.dishId) {
        const dish = dishesData.find(d => d.id === meal.dishId);
        if (dish) {
          totalCost += dish.cost || 0;
          totalDishes += 1;
          
          // Parse cooking time (assume format like "30 phút")
          const timeMatch = dish.cookingTime?.match(/(\d+)/);
          if (timeMatch) {
            totalCookingTime += parseInt(timeMatch[1]);
          }
          
          // Add nutrition
          if (dish.nutrition) {
            nutritionSummary.calories += dish.nutrition.calories || 0;
            nutritionSummary.protein += dish.nutrition.protein || 0;
            nutritionSummary.carbs += dish.nutrition.carbs || 0;
            nutritionSummary.fat += dish.nutrition.fat || 0;
            nutritionSummary.fiber += dish.nutrition.fiber || 0;
          }
        }
      } else {
        missingSlots.push({
          day: day.dayOfWeek,
          mealType: mealType === 'breakfast' ? 'Sáng' :
                   mealType === 'lunch' ? 'Trưa' :
                   mealType === 'dinner' ? 'Tối' : 'Ăn vặt'
        });
      }
    });
  });

  // Generate consolidated shopping list (simplified for now)
  const shoppingList = [
    {
      category: 'Thịt cá',
      items: []
    },
    {
      category: 'Rau củ',
      items: []
    },
    {
      category: 'Gia vị',
      items: []
    }
  ];

  return {
    totalCost,
    totalDishes,
    totalCookingTime,
    nutritionSummary,
    shoppingList,
    missingSlots
  };
};

export const applyTemplateToWeekPlan = (
  weekPlan: WeeklyMealPlan,
  template: any,
  dishesData: any[]
): WeeklyMealPlan => {
  const updatedPlan = { ...weekPlan };
  updatedPlan.appliedTemplateId = template.id;
  updatedPlan.name = `${template.name} - Tuần ${new Date(weekPlan.startDate).toLocaleDateString('vi-VN')}`;
  updatedPlan.tags = [...template.tags];
  updatedPlan.updatedAt = new Date().toISOString();

  // Apply template meals to each day
  updatedPlan.days.forEach((day, dayIndex) => {
    // Breakfast
    if (template.meals.breakfast && template.meals.breakfast.length > 0) {
      const breakfastDishId = template.meals.breakfast[dayIndex % template.meals.breakfast.length];
      const dish = dishesData.find(d => d.id === breakfastDishId);
      if (dish) {
        day.breakfast = {
          id: generateMealSlotId(dayIndex, 'breakfast'),
          dishId: dish.id,
          dishName: dish.name,
          estimatedCost: dish.cost,
          cookingTime: dish.cookingTime,
          servings: dish.servings,
          isFromTemplate: true,
          templateId: template.id
        };
      }
    }

    // Lunch
    if (template.meals.lunch && template.meals.lunch.length > 0) {
      const lunchDishId = template.meals.lunch[dayIndex % template.meals.lunch.length];
      const dish = dishesData.find(d => d.id === lunchDishId);
      if (dish) {
        day.lunch = {
          id: generateMealSlotId(dayIndex, 'lunch'),
          dishId: dish.id,
          dishName: dish.name,
          estimatedCost: dish.cost,
          cookingTime: dish.cookingTime,
          servings: dish.servings,
          isFromTemplate: true,
          templateId: template.id
        };
      }
    }

    // Dinner
    if (template.meals.dinner && template.meals.dinner.length > 0) {
      const dinnerDishId = template.meals.dinner[dayIndex % template.meals.dinner.length];
      const dish = dishesData.find(d => d.id === dinnerDishId);
      if (dish) {
        day.dinner = {
          id: generateMealSlotId(dayIndex, 'dinner'),
          dishId: dish.id,
          dishName: dish.name,
          estimatedCost: dish.cost,
          cookingTime: dish.cookingTime,
          servings: dish.servings,
          isFromTemplate: true,
          templateId: template.id
        };
      }
    }

    // Snack
    if (template.meals.snack && template.meals.snack.length > 0) {
      const snackDishId = template.meals.snack[dayIndex % template.meals.snack.length];
      const dish = dishesData.find(d => d.id === snackDishId);
      if (dish) {
        day.snack = {
          id: generateMealSlotId(dayIndex, 'snack'),
          dishId: dish.id,
          dishName: dish.name,
          estimatedCost: dish.cost,
          cookingTime: dish.cookingTime,
          servings: dish.servings,
          isFromTemplate: true,
          templateId: template.id
        };
      }
    }
  });

  return updatedPlan;
};

// Default current week plan
export const getCurrentWeekPlan = (): WeeklyMealPlan => {
  const today = new Date();
  const startOfWeek = new Date(today);
  startOfWeek.setDate(today.getDate() - today.getDay()); // Start from Sunday
  
  return createEmptyWeekPlan(startOfWeek);
};

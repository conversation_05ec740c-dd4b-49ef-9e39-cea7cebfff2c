/**
 * Phân loại món ăn Việt Nam theo thói quen ăn uống
 */

export interface VietnameseFoodCategory {
  id: string;
  name: string;
  description: string;
  mealTypes: string[]; // breakfast, lunch, dinner, snack
  isStreetFood: boolean; // món ăn đường phố/ngoài hàng
  isHomeCooking: boolean; // món ăn nấu tại nhà
  commonTime: string[]; // thời gian thường ăn
}

export interface VietnameseDish {
  id: string;
  name: string;
  description: string;
  categoryId: string;
  region: 'north' | 'central' | 'south' | 'nationwide'; // miền
  difficulty: 'easy' | 'medium' | 'hard';
  cookingTime: string;
  servings: number;
  ingredients: string[];
  instructions: string[];
  nutrition: {
    calories: number;
    protein: number;
    carbs: number;
    fat: number;
    fiber: number;
  };
  tags: string[];
  cost: number; // VND
  isPopular: boolean;
  mealType: 'breakfast' | 'lunch' | 'dinner' | 'snack' | 'anytime';
  preparationComplexity: 'simple' | 'moderate' | 'complex'; // độ phức tạp chuẩn bị
}

// Phân loại chính các món ăn Việt Nam
export const vietnameseFoodCategories: VietnameseFoodCategory[] = [
  // NHÓM MÓN ĂN SÁNG / NGOÀI HÀNG
  {
    id: 'pho',
    name: 'Phở',
    description: 'Món phở truyền thống với nước dùng trong vắt',
    mealTypes: ['breakfast', 'lunch'],
    isStreetFood: true,
    isHomeCooking: false,
    commonTime: ['6:00-10:00', '11:00-14:00']
  },
  {
    id: 'bun',
    name: 'Bún',
    description: 'Các món bún như bún chả, bún bò, bún riêu',
    mealTypes: ['breakfast', 'lunch'],
    isStreetFood: true,
    isHomeCooking: false,
    commonTime: ['6:00-10:00', '11:00-14:00']
  },
  {
    id: 'hu_tieu',
    name: 'Hủ tiếu',
    description: 'Hủ tiếu Nam Vang và các biến thể',
    mealTypes: ['breakfast', 'lunch'],
    isStreetFood: true,
    isHomeCooking: false,
    commonTime: ['6:00-10:00', '11:00-14:00']
  },
  {
    id: 'mi',
    name: 'Mì',
    description: 'Mì Quảng, mì gà, mì vịt tiềm',
    mealTypes: ['breakfast', 'lunch'],
    isStreetFood: true,
    isHomeCooking: false,
    commonTime: ['6:00-10:00', '11:00-14:00']
  },
  {
    id: 'banh_mi',
    name: 'Bánh mì',
    description: 'Bánh mì Việt Nam với các loại nhân khác nhau',
    mealTypes: ['breakfast', 'snack'],
    isStreetFood: true,
    isHomeCooking: false,
    commonTime: ['6:00-10:00', '15:00-18:00']
  },
  {
    id: 'chao',
    name: 'Cháo',
    description: 'Cháo lòng, cháo gà, cháo cá',
    mealTypes: ['breakfast', 'dinner'],
    isStreetFood: true,
    isHomeCooking: true,
    commonTime: ['6:00-10:00', '18:00-21:00']
  },
  {
    id: 'xoi',
    name: 'Xôi',
    description: 'Xôi mặn, xôi ngọt các loại',
    mealTypes: ['breakfast', 'snack'],
    isStreetFood: true,
    isHomeCooking: true,
    commonTime: ['6:00-10:00', '15:00-18:00']
  },

  // NHÓM MÓN ĂN CƠM HÀNG NGÀY
  {
    id: 'mon_chinh_thit',
    name: 'Món chính thịt',
    description: 'Các món thịt heo, bò, gà nấu với cơm',
    mealTypes: ['lunch', 'dinner'],
    isStreetFood: false,
    isHomeCooking: true,
    commonTime: ['11:00-14:00', '17:00-20:00']
  },
  {
    id: 'mon_ca',
    name: 'Món cá',
    description: 'Các món cá kho, nướng, chiên ăn với cơm',
    mealTypes: ['lunch', 'dinner'],
    isStreetFood: false,
    isHomeCooking: true,
    commonTime: ['11:00-14:00', '17:00-20:00']
  },
  {
    id: 'mon_tom_cua',
    name: 'Món tôm cua',
    description: 'Các món hải sản ăn với cơm',
    mealTypes: ['lunch', 'dinner'],
    isStreetFood: false,
    isHomeCooking: true,
    commonTime: ['11:00-14:00', '17:00-20:00']
  },
  {
    id: 'canh',
    name: 'Canh',
    description: 'Các món canh ăn kèm cơm',
    mealTypes: ['lunch', 'dinner'],
    isStreetFood: false,
    isHomeCooking: true,
    commonTime: ['11:00-14:00', '17:00-20:00']
  },
  {
    id: 'rau_cu',
    name: 'Rau củ',
    description: 'Các món rau xào, luộc, nộm',
    mealTypes: ['lunch', 'dinner'],
    isStreetFood: false,
    isHomeCooking: true,
    commonTime: ['11:00-14:00', '17:00-20:00']
  },
  {
    id: 'mon_kho',
    name: 'Món kho',
    description: 'Thịt kho, cá kho, đậu kho',
    mealTypes: ['lunch', 'dinner'],
    isStreetFood: false,
    isHomeCooking: true,
    commonTime: ['11:00-14:00', '17:00-20:00']
  },
  {
    id: 'mon_rim',
    name: 'Món rim',
    description: 'Các món rim ngọt đậm đà',
    mealTypes: ['lunch', 'dinner'],
    isStreetFood: false,
    isHomeCooking: true,
    commonTime: ['11:00-14:00', '17:00-20:00']
  },

  // NHÓM MÓN ĂN VẶT / TRÁNG MIỆNG
  {
    id: 'an_vat',
    name: 'Ăn vặt',
    description: 'Bánh xèo, bánh khọt, nem nướng',
    mealTypes: ['snack'],
    isStreetFood: true,
    isHomeCooking: false,
    commonTime: ['15:00-18:00', '19:00-22:00']
  },
  {
    id: 'che_trang_mieng',
    name: 'Chè tráng miệng',
    description: 'Các loại chè, bánh ngọt',
    mealTypes: ['snack'],
    isStreetFood: true,
    isHomeCooking: true,
    commonTime: ['14:00-17:00', '19:00-22:00']
  }
];

// Helper functions
export const getBreakfastStreetFoods = () => {
  return vietnameseFoodCategories.filter(cat =>
    cat.isStreetFood && cat.mealTypes.includes('breakfast')
  );
};

export const getHomeCookingMeals = () => {
  return vietnameseFoodCategories.filter(cat =>
    cat.isHomeCooking && (cat.mealTypes.includes('lunch') || cat.mealTypes.includes('dinner'))
  );
};

export const getStreetFoods = () => {
  return vietnameseFoodCategories.filter(cat => cat.isStreetFood);
};

export const getHomeCookingDishes = () => {
  return vietnameseFoodCategories.filter(cat => cat.isHomeCooking);
};
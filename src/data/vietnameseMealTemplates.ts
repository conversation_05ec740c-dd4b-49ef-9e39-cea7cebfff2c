/**
 * Thực đơn mẫu "<PERSON>âm <PERSON>" theo mục tiêu khác nhau
 */

export interface MealTemplate {
  id: string;
  name: string;
  description: string;
  category: 'quick' | 'budget' | 'healthy' | 'family' | 'special' | 'vegetarian';
  difficulty: 'easy' | 'medium' | 'hard';
  totalTime: string;
  servings: number;
  estimatedCost: number;
  tags: string[];
  meals: {
    breakfast?: string[];
    lunch: string[];
    dinner: string[];
    snack?: string[];
  };
  nutritionSummary: {
    calories: number;
    protein: number;
    carbs: number;
    fat: number;
    fiber: number;
  };
  shoppingList: {
    category: string;
    items: {
      name: string;
      quantity: string;
      unit: string;
      estimatedPrice: number;
    }[];
  }[];
  tips: string[];
  occasion?: string;
  region?: 'north' | 'central' | 'south' | 'nationwide';
}

export const vietnameseMealTemplates: MealTemplate[] = [
  // THỰC ĐƠN NHANH GỌN
  {
    id: 'quick_weekday_meals',
    name: '<PERSON><PERSON><PERSON>hường - <PERSON><PERSON><PERSON>',
    description: 'Thực đơn cho người bận rộn, nấu nhanh trong 30 phút',
    category: 'quick',
    difficulty: 'easy',
    totalTime: '30 phút',
    servings: 4,
    estimatedCost: 180000,
    tags: ['Nhanh gọn', 'Ngày thường', 'Dễ làm'],
    meals: {
      breakfast: ['pho_ga'],
      lunch: ['ga_rang_gung', 'canh_suon_non_ham_rau_cu', 'rau_muong_xao_toi'],
      dinner: ['dau_phu_nhoi_thit', 'canh_chua_ca_bong_lau', 'rau_muong_xao_toi']
    },
    nutritionSummary: {
      calories: 1850,
      protein: 95,
      carbs: 180,
      fat: 65,
      fiber: 25
    },
    shoppingList: [
      {
        category: 'Thịt cá',
        items: [
          { name: 'Gà ta', quantity: '1', unit: 'con', estimatedPrice: 80000 },
          { name: 'Sườn non', quantity: '500', unit: 'g', estimatedPrice: 60000 },
          { name: 'Thịt heo xay', quantity: '200', unit: 'g', estimatedPrice: 25000 },
          { name: 'Cá bông lau', quantity: '500', unit: 'g', estimatedPrice: 40000 }
        ]
      },
      {
        category: 'Rau củ',
        items: [
          { name: 'Rau muống', quantity: '2', unit: 'bó', estimatedPrice: 10000 },
          { name: 'Cà rót', quantity: '3', unit: 'quả', estimatedPrice: 15000 },
          { name: 'Củ cải trắng', quantity: '1', unit: 'củ', estimatedPrice: 8000 },
          { name: 'Cà chua', quantity: '3', unit: 'quả', estimatedPrice: 12000 }
        ]
      },
      {
        category: 'Gia vị',
        items: [
          { name: 'Gừng tươi', quantity: '50', unit: 'g', estimatedPrice: 5000 },
          { name: 'Tỏi', quantity: '1', unit: 'củ', estimatedPrice: 8000 },
          { name: 'Hành tím', quantity: '100', unit: 'g', estimatedPrice: 6000 }
        ]
      }
    ],
    tips: [
      'Ướp gà từ tối hôm trước để tiết kiệm thời gian',
      'Nấu canh sườn bằng nồi áp suất sẽ nhanh hơn',
      'Có thể thay rau muống bằng rau cải ngọt'
    ],
    region: 'nationwide'
  },

  // THỰC ĐƠN TIẾT KIỆM
  {
    id: 'budget_family_meals',
    name: 'Mâm Cơm Gia Đình - Tiết Kiệm',
    description: 'Thực đơn tiết kiệm nhưng đầy đủ dinh dưỡng cho cả gia đình',
    category: 'budget',
    difficulty: 'easy',
    totalTime: '45 phút',
    servings: 6,
    estimatedCost: 150000,
    tags: ['Tiết kiệm', 'Gia đình', 'Bổ dưỡng'],
    meals: {
      breakfast: ['bun_cha_ha_noi'],
      lunch: ['thit_kho_trung_cut', 'canh_suon_non_ham_rau_cu', 'rau_muong_xao_toi'],
      dinner: ['dau_phu_nhoi_thit', 'canh_chua_ca_bong_lau', 'rau_muong_xao_toi']
    },
    nutritionSummary: {
      calories: 1650,
      protein: 85,
      carbs: 165,
      fat: 55,
      fiber: 20
    },
    shoppingList: [
      {
        category: 'Thịt cá',
        items: [
          { name: 'Thịt ba chỉ', quantity: '800', unit: 'g', estimatedPrice: 70000 },
          { name: 'Sườn non', quantity: '400', unit: 'g', estimatedPrice: 48000 },
          { name: 'Cá bông lau', quantity: '400', unit: 'g', estimatedPrice: 32000 }
        ]
      },
      {
        category: 'Rau củ',
        items: [
          { name: 'Rau muống', quantity: '2', unit: 'bó', estimatedPrice: 10000 },
          { name: 'Củ cải trắng', quantity: '1', unit: 'củ', estimatedPrice: 8000 },
          { name: 'Cà chua', quantity: '2', unit: 'quả', estimatedPrice: 8000 }
        ]
      },
      {
        category: 'Khác',
        items: [
          { name: 'Trứng cút', quantity: '20', unit: 'quả', estimatedPrice: 15000 },
          { name: 'Đậu phụ', quantity: '4', unit: 'miếng', estimatedPrice: 12000 }
        ]
      }
    ],
    tips: [
      'Mua thịt ba chỉ có da sẽ ngon và rẻ hơn',
      'Trứng cút có thể thay bằng trứng gà để tiết kiệm',
      'Nấu nhiều canh để ăn 2 bữa'
    ],
    region: 'nationwide'
  },

  // THỰC ĐƠN HEALTHY
  {
    id: 'healthy_clean_eating',
    name: 'Mâm Cơm Healthy - Ăn Sạch',
    description: 'Thực đơn ít dầu mỡ, nhiều rau xanh, phù hợp eat clean',
    category: 'healthy',
    difficulty: 'medium',
    totalTime: '40 phút',
    servings: 4,
    estimatedCost: 220000,
    tags: ['Healthy', 'Ít dầu mỡ', 'Nhiều rau', 'Clean eating'],
    meals: {
      breakfast: ['pho_ga'],
      lunch: ['ca_thu_nuong_la_chuoi', 'canh_suon_non_ham_rau_cu', 'rau_muong_xao_toi'],
      dinner: ['dau_phu_nhoi_thit', 'canh_chua_ca_bong_lau', 'rau_muong_xao_toi']
    },
    nutritionSummary: {
      calories: 1580,
      protein: 105,
      carbs: 140,
      fat: 45,
      fiber: 30
    },
    shoppingList: [
      {
        category: 'Thịt cá',
        items: [
          { name: 'Cá thu tươi', quantity: '800', unit: 'g', estimatedPrice: 80000 },
          { name: 'Gà ta', quantity: '500', unit: 'g', estimatedPrice: 60000 },
          { name: 'Cá bông lau', quantity: '400', unit: 'g', estimatedPrice: 32000 }
        ]
      },
      {
        category: 'Rau củ',
        items: [
          { name: 'Rau muống', quantity: '3', unit: 'bó', estimatedPrice: 15000 },
          { name: 'Rau cải ngọt', quantity: '2', unit: 'bó', estimatedPrice: 12000 },
          { name: 'Cà rót', quantity: '4', unit: 'quả', estimatedPrice: 20000 },
          { name: 'Lá chuối', quantity: '5', unit: 'lá', estimatedPrice: 5000 }
        ]
      }
    ],
    tips: [
      'Nướng cá bằng lò thay vì chiên để giảm dầu mỡ',
      'Tăng lượng rau xanh trong mỗi bữa ăn',
      'Sử dụng dầu oliva thay vì dầu ăn thường'
    ],
    region: 'nationwide'
  },

  // THỰC ĐƠN MIỀN NAM
  {
    id: 'southern_specialties',
    name: 'Mâm Cơm Miền Nam - Đặc Sản',
    description: 'Hương vị đậm đà miền Nam với cá kho tộ và canh chua',
    category: 'special',
    difficulty: 'medium',
    totalTime: '60 phút',
    servings: 5,
    estimatedCost: 280000,
    tags: ['Miền Nam', 'Đặc sản', 'Đậm đà', 'Truyền thống'],
    meals: {
      breakfast: ['hu_tieu_nam_vang'],
      lunch: ['ca_kho_to', 'canh_chua_ca_bong_lau', 'rau_muong_xao_toi'],
      dinner: ['thit_nuong_la_lot', 'suon_xao_chua_ngot', 'canh_chua_ca_bong_lau'],
      snack: ['banh_xeo']
    },
    nutritionSummary: {
      calories: 2150,
      protein: 125,
      carbs: 195,
      fat: 85,
      fiber: 28
    },
    shoppingList: [
      {
        category: 'Thịt cá',
        items: [
          { name: 'Cá lóc', quantity: '1', unit: 'con', estimatedPrice: 90000 },
          { name: 'Thịt heo xay', quantity: '600', unit: 'g', estimatedPrice: 75000 },
          { name: 'Sườn heo', quantity: '500', unit: 'g', estimatedPrice: 68000 }
        ]
      },
      {
        category: 'Rau củ & lá',
        items: [
          { name: 'Lá lốt', quantity: '30', unit: 'lá', estimatedPrice: 15000 },
          { name: 'Dứa', quantity: '1', unit: 'quả', estimatedPrice: 25000 },
          { name: 'Giá đỗ', quantity: '200', unit: 'g', estimatedPrice: 8000 }
        ]
      },
      {
        category: 'Gia vị đặc biệt',
        items: [
          { name: 'Nước dừa', quantity: '400', unit: 'ml', estimatedPrice: 20000 },
          { name: 'Me chua', quantity: '50', unit: 'g', estimatedPrice: 8000 }
        ]
      }
    ],
    tips: [
      'Cá kho tộ ngon nhất khi kho bằng niêu đất',
      'Lá lốt nên chọn loại non, xanh đậm',
      'Canh chua nên nấu trong nồi đất để giữ vị chua tự nhiên'
    ],
    occasion: 'Cuối tuần, tiếp khách',
    region: 'south'
  }
];

// Helper functions
export const getMealTemplatesByCategory = (category: MealTemplate['category']) => {
  return vietnameseMealTemplates.filter(template => template.category === category);
};

export const getMealTemplatesByDifficulty = (difficulty: MealTemplate['difficulty']) => {
  return vietnameseMealTemplates.filter(template => template.difficulty === difficulty);
};

export const getMealTemplatesByBudget = (maxBudget: number) => {
  return vietnameseMealTemplates.filter(template => template.estimatedCost <= maxBudget);
};

export const getMealTemplateById = (id: string) => {
  return vietnameseMealTemplates.find(template => template.id === id);
};

export const getRandomMealTemplates = (count: number = 3) => {
  const shuffled = [...vietnameseMealTemplates].sort(() => 0.5 - Math.random());
  return shuffled.slice(0, count);
};

/**
 * Service cào dữ liệu món ăn Việt Nam từ các nguồn trực tuyến
 * Hỗ trợ phân loại tự động món ăn sáng/ngoài hàng vs món ăn cơm hàng ngày
 */

import { VietnameseDish } from '../data/vietnameseFoodCategories';

export interface ScrapingSource {
  name: string;
  baseUrl: string;
  selectors: {
    title: string;
    description: string;
    ingredients: string;
    instructions: string;
    cookTime: string;
    servings: string;
    image: string;
    category: string;
  };
  rateLimit: number; // milliseconds between requests
}

export interface ScrapingResult {
  success: boolean;
  data?: VietnameseDish[];
  error?: string;
  source: string;
  scrapedAt: Date;
}

export interface ScrapingOptions {
  maxPages?: number;
  delay?: number;
  categories?: string[];
  regions?: string[];
}

class VietnameseFoodScrapingService {
  private readonly sources: ScrapingSource[] = [
    {
      name: 'Cooky.vn',
      baseUrl: 'https://cooky.vn',
      selectors: {
        title: '.recipe-title, h1.title',
        description: '.recipe-description, .description',
        ingredients: '.ingredient-item, .ingredients li',
        instructions: '.instruction-step, .instructions li',
        cookTime: '.cook-time, .time',
        servings: '.servings, .portion',
        image: '.recipe-image img, .main-image img',
        category: '.category, .recipe-category'
      },
      rateLimit: 2000
    },
    {
      name: 'Foody.vn',
      baseUrl: 'https://foody.vn',
      selectors: {
        title: '.recipe-name, h1',
        description: '.recipe-desc, .description',
        ingredients: '.ingredient-list li, .ingredients li',
        instructions: '.cooking-step, .steps li',
        cookTime: '.cooking-time, .time-info',
        servings: '.serving-size, .serves',
        image: '.recipe-photo img, .main-photo img',
        category: '.recipe-category, .category-tag'
      },
      rateLimit: 1500
    },
    {
      name: 'MonNgon.vn',
      baseUrl: 'https://monngon.vn',
      selectors: {
        title: '.recipe-title, .post-title h1',
        description: '.recipe-summary, .excerpt',
        ingredients: '.recipe-ingredients li, .ingredients-list li',
        instructions: '.recipe-method li, .cooking-steps li',
        cookTime: '.prep-time, .cooking-duration',
        servings: '.recipe-serves, .portions',
        image: '.featured-image img, .recipe-image img',
        category: '.recipe-category, .post-category'
      },
      rateLimit: 2500
    }
  ];

  private readonly vietnameseKeywords = {
    breakfast: ['phở', 'bún', 'hủ tiếu', 'mì', 'bánh mì', 'cháo', 'xôi', 'chè'],
    streetFood: ['phở', 'bún', 'hủ tiếu', 'mì quảng', 'bánh mì', 'bánh xèo', 'bánh khọt', 'nem nướng'],
    homeCooking: ['cơm', 'canh', 'thịt kho', 'cá kho', 'gà rang', 'rau xào', 'đậu phụ'],
    regions: {
      north: ['hà nội', 'bắc', 'phở bò', 'bún chả', 'chả cá'],
      central: ['huế', 'miền trung', 'bún bò huế', 'mì quảng', 'cao lầu'],
      south: ['sài gòn', 'miền nam', 'hủ tiếu', 'bánh xèo', 'cơm tấm']
    }
  };

  /**
   * Cào dữ liệu từ một nguồn cụ thể
   */
  async scrapeFromSource(sourceName: string, options: ScrapingOptions = {}): Promise<ScrapingResult> {
    const source = this.sources.find(s => s.name === sourceName);
    if (!source) {
      return {
        success: false,
        error: `Nguồn ${sourceName} không được hỗ trợ`,
        source: sourceName,
        scrapedAt: new Date()
      };
    }

    try {
      console.log(`🔄 Bắt đầu cào dữ liệu từ ${sourceName}...`);

      // Trong thực tế, đây sẽ là API call đến backend service
      // Backend sẽ thực hiện việc cào dữ liệu thực sự
      const mockData = await this.mockScrapeData(source, options);

      return {
        success: true,
        data: mockData,
        source: sourceName,
        scrapedAt: new Date()
      };
    } catch (error) {
      return {
        success: false,
        error: `Lỗi khi cào dữ liệu từ ${sourceName}: ${error}`,
        source: sourceName,
        scrapedAt: new Date()
      };
    }
  }

  /**
   * Cào dữ liệu từ tất cả các nguồn
   */
  async scrapeFromAllSources(options: ScrapingOptions = {}): Promise<ScrapingResult[]> {
    const results: ScrapingResult[] = [];

    for (const source of this.sources) {
      const result = await this.scrapeFromSource(source.name, options);
      results.push(result);

      // Delay giữa các nguồn để tránh bị block
      if (options.delay) {
        await this.delay(options.delay);
      }
    }

    return results;
  }

  /**
   * Phân loại món ăn dựa trên tên và mô tả
   */
  private categorizeVietnameseDish(title: string, description: string): {
    categoryId: string;
    mealType: 'breakfast' | 'lunch' | 'dinner' | 'snack';
    isStreetFood: boolean;
    region: 'north' | 'central' | 'south' | 'nationwide';
  } {
    const titleLower = title.toLowerCase();
    const descLower = description.toLowerCase();
    const combined = `${titleLower} ${descLower}`;

    // Phân loại theo món ăn sáng/ngoài hàng
    if (this.vietnameseKeywords.breakfast.some(keyword => combined.includes(keyword))) {
      return {
        categoryId: this.getCategoryByKeyword(combined),
        mealType: 'breakfast',
        isStreetFood: true,
        region: this.getRegionByKeyword(combined)
      };
    }

    // Phân loại theo món ăn cơm hàng ngày
    if (this.vietnameseKeywords.homeCooking.some(keyword => combined.includes(keyword))) {
      return {
        categoryId: this.getHomeCookingCategory(combined),
        mealType: 'lunch',
        isStreetFood: false,
        region: this.getRegionByKeyword(combined)
      };
    }

    // Mặc định
    return {
      categoryId: 'mon_chinh_thit',
      mealType: 'lunch',
      isStreetFood: false,
      region: 'nationwide'
    };
  }

  private getCategoryByKeyword(text: string): string {
    if (text.includes('phở')) return 'pho';
    if (text.includes('bún')) return 'bun';
    if (text.includes('hủ tiếu')) return 'hu_tieu';
    if (text.includes('mì')) return 'mi';
    if (text.includes('bánh mì')) return 'banh_mi';
    if (text.includes('cháo')) return 'chao';
    if (text.includes('xôi')) return 'xoi';
    if (text.includes('chè')) return 'che_trang_mieng';
    return 'mon_chinh_thit';
  }

  private getHomeCookingCategory(text: string): string {
    if (text.includes('canh')) return 'canh';
    if (text.includes('kho')) return 'mon_kho';
    if (text.includes('rim')) return 'mon_rim';
    if (text.includes('cá')) return 'mon_ca';
    if (text.includes('tôm') || text.includes('cua')) return 'mon_tom_cua';
    if (text.includes('rau')) return 'rau_cu';
    return 'mon_chinh_thit';
  }

  private getRegionByKeyword(text: string): 'north' | 'central' | 'south' | 'nationwide' {
    if (this.vietnameseKeywords.regions.north.some(keyword => text.includes(keyword))) {
      return 'north';
    }
    if (this.vietnameseKeywords.regions.central.some(keyword => text.includes(keyword))) {
      return 'central';
    }
    if (this.vietnameseKeywords.regions.south.some(keyword => text.includes(keyword))) {
      return 'south';
    }
    return 'nationwide';
  }

  /**
   * Mock data cho demo - trong thực tế sẽ thay bằng API call
   */
  private async mockScrapeData(source: ScrapingSource, options: ScrapingOptions): Promise<VietnameseDish[]> {
    // Simulate network delay
    await this.delay(source.rateLimit);

    const mockDishes: VietnameseDish[] = [];
    const maxItems = options.maxPages ? options.maxPages * 10 : 20;

    // Generate mock data based on source
    for (let i = 0; i < maxItems; i++) {
      const dishData = this.generateMockDish(source.name, i);
      if (dishData) {
        mockDishes.push(dishData);
      }
    }

    return mockDishes;
  }

  private generateMockDish(sourceName: string, index: number): VietnameseDish | null {
    const mockTitles = {
      'Cooky.vn': [
        'Phở bò truyền thống Hà Nội',
        'Bún chả nướng than hồng',
        'Gà rang gừng đậm đà',
        'Canh chua cá lóc miền Tây',
        'Thịt kho tàu đậm đà',
        'Rau muống xào tỏi giòn ngon'
      ],
      'Foody.vn': [
        'Hủ tiếu Nam Vang đặc biệt',
        'Mì Quảng tôm thịt',
        'Cá thu nướng lá chuối',
        'Canh sườn hầm rau củ',
        'Đậu phụ nhồi thịt',
        'Bánh xèo miền Nam'
      ],
      'MonNgon.vn': [
        'Bún bò Huế cay nồng',
        'Bánh mì thịt nướng',
        'Cơm tấm sườn nướng',
        'Chè ba màu mát lạnh',
        'Nem nướng Nha Trang',
        'Cháo lòng đậm đà'
      ]
    };

    const titles = mockTitles[sourceName as keyof typeof mockTitles] || mockTitles['Cooky.vn'];
    const title = titles[index % titles.length];

    if (!title) return null;

    const category = this.categorizeVietnameseDish(title, '');

    return {
      id: `scraped_${sourceName.toLowerCase().replace('.', '_')}_${index}`,
      name: title,
      description: `${title} được cào từ ${sourceName}`,
      categoryId: category.categoryId,
      region: category.region,
      difficulty: ['easy', 'medium', 'hard'][Math.floor(Math.random() * 3)] as 'easy' | 'medium' | 'hard',
      cookingTime: ['30 phút', '45 phút', '1 giờ', '2 giờ'][Math.floor(Math.random() * 4)],
      servings: Math.floor(Math.random() * 4) + 2,
      ingredients: this.generateMockIngredients(title),
      instructions: this.generateMockInstructions(title),
      nutrition: {
        calories: Math.floor(Math.random() * 200) + 200,
        protein: Math.floor(Math.random() * 20) + 10,
        carbs: Math.floor(Math.random() * 30) + 20,
        fat: Math.floor(Math.random() * 15) + 5,
        fiber: Math.floor(Math.random() * 5) + 2
      },
      tags: this.generateMockTags(title),
      cost: Math.floor(Math.random() * 50000) + 30000,
      isPopular: Math.random() > 0.7,
      mealType: category.mealType,
      preparationComplexity: ['simple', 'moderate', 'complex'][Math.floor(Math.random() * 3)] as 'simple' | 'moderate' | 'complex'
    };
  }

  private generateMockIngredients(title: string): string[] {
    const baseIngredients = ['Nước mắm', 'Muối', 'Đường', 'Tiêu', 'Dầu ăn'];
    const specificIngredients: { [key: string]: string[] } = {
      'phở': ['Xương bò', 'Thịt bò', 'Bánh phở', 'Hành tây', 'Gừng', 'Quế', 'Hồi'],
      'bún': ['Bún tươi', 'Thịt heo', 'Rau thơm', 'Dưa chua'],
      'gà': ['Thịt gà', 'Gừng', 'Hành tím', 'Rau thơm'],
      'cá': ['Cá tươi', 'Lá chuối', 'Sả', 'Tỏi'],
      'canh': ['Xương heo', 'Rau củ', 'Cà chua', 'Hành lá']
    };

    let ingredients = [...baseIngredients];

    for (const [key, values] of Object.entries(specificIngredients)) {
      if (title.toLowerCase().includes(key)) {
        ingredients = [...ingredients, ...values];
        break;
      }
    }

    return ingredients.slice(0, 8);
  }

  private generateMockInstructions(title: string): string[] {
    const baseInstructions = [
      'Chuẩn bị nguyên liệu sạch sẽ',
      'Ướp gia vị theo khẩu vị',
      'Nấu theo phương pháp truyền thống',
      'Nêm nếm lại gia vị',
      'Trang trí và thưởng thức'
    ];

    return baseInstructions;
  }

  private generateMockTags(title: string): string[] {
    const allTags = ['Truyền thống', 'Ngon', 'Dễ làm', 'Bổ dưỡng', 'Gia đình', 'Tiết kiệm'];
    return allTags.slice(0, Math.floor(Math.random() * 4) + 2);
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Lấy danh sách các nguồn hỗ trợ
   */
  getSupportedSources(): string[] {
    return this.sources.map(source => source.name);
  }

  /**
   * Kiểm tra xem nguồn có được hỗ trợ không
   */
  isSourceSupported(sourceName: string): boolean {
    return this.sources.some(source => source.name === sourceName);
  }

  /**
   * Lấy thông tin chi tiết về một nguồn
   */
  getSourceInfo(sourceName: string): ScrapingSource | null {
    return this.sources.find(source => source.name === sourceName) || null;
  }
}

export const vietnameseFoodScrapingService = new VietnameseFoodScrapingService();
/**
 * Service chuyển đổi từ Meal Plan sang Shopping List
 * Tích hợp với hệ thống shopping cart hiện có
 */

import { WeeklyMealPlan, DayMealPlan, MealSlot } from '@/data/weeklyMealPlan';
import { ShoppingSession, ShoppingCartItem } from '@/contexts/ShoppingCartContext';
import { VietnameseDish } from '@/data/vietnameseFoodCategories';
import { UnitConversionService } from './UnitConversionService';
import { IngredientPricingService } from './IngredientPricingService';

export interface MealPlanShoppingOptions {
  consolidateIngredients: boolean;
  includeOptionalItems: boolean;
  adjustForServings: boolean;
  targetServings?: number;
  excludeDays?: string[]; // Exclude specific days
  onlySelectedMeals?: boolean;
}

export interface GeneratedShoppingList {
  sessions: ShoppingSession[];
  totalItems: number;
  totalEstimatedCost: number;
  summary: {
    breakfastItems: number;
    lunchItems: number;
    dinnerItems: number;
    snackItems: number;
    uniqueIngredients: number;
    estimatedShoppingTime: number; // minutes
  };
  shoppingTips: string[];
  missingIngredients: string[]; // Dishes without ingredient data
}

class MealPlanToShoppingService {
  private unitConversion = new UnitConversionService();
  private pricingService = new IngredientPricingService();

  /**
   * Chuyển đổi meal plan thành shopping list
   */
  async generateShoppingList(
    weekPlan: WeeklyMealPlan,
    dishesData: VietnameseDish[],
    options: MealPlanShoppingOptions = {
      consolidateIngredients: true,
      includeOptionalItems: false,
      adjustForServings: true,
      onlySelectedMeals: false
    }
  ): Promise<GeneratedShoppingList> {
    const sessions: ShoppingSession[] = [];
    const allItems: ShoppingCartItem[] = [];
    const missingIngredients: string[] = [];
    let totalEstimatedCost = 0;

    // Group meals by day or by meal type
    if (options.consolidateIngredients) {
      // Create consolidated sessions by meal type
      const mealTypeSessions = await this.createMealTypeSessions(weekPlan, dishesData, options);
      sessions.push(...mealTypeSessions);
    } else {
      // Create individual sessions for each day
      const dailySessions = await this.createDailySessions(weekPlan, dishesData, options);
      sessions.push(...dailySessions);
    }

    // Calculate totals
    sessions.forEach(session => {
      totalEstimatedCost += session.totalEstimatedCost;
      allItems.push(...session.items);
    });

    // Generate summary
    const summary = this.generateSummary(sessions, allItems);
    
    // Generate shopping tips
    const shoppingTips = this.generateShoppingTips(sessions, weekPlan);

    return {
      sessions,
      totalItems: allItems.length,
      totalEstimatedCost,
      summary,
      shoppingTips,
      missingIngredients
    };
  }

  /**
   * Tạo sessions theo loại bữa ăn (sáng, trưa, tối, ăn vặt)
   */
  private async createMealTypeSessions(
    weekPlan: WeeklyMealPlan,
    dishesData: VietnameseDish[],
    options: MealPlanShoppingOptions
  ): Promise<ShoppingSession[]> {
    const sessions: ShoppingSession[] = [];
    const mealTypes = ['breakfast', 'lunch', 'dinner', 'snack'];
    
    for (const mealType of mealTypes) {
      const mealItems: ShoppingCartItem[] = [];
      let sessionCost = 0;

      // Collect all dishes of this meal type from all days
      weekPlan.days.forEach((day, dayIndex) => {
        if (options.excludeDays?.includes(day.date)) return;

        const meal = (day as any)[mealType] as MealSlot;
        if (!meal?.dishId) return;

        const dish = dishesData.find(d => d.id === meal.dishId);
        if (!dish) return;

        // Convert dish ingredients to shopping items
        const dishItems = this.convertDishToShoppingItems(
          dish,
          meal,
          day,
          mealType,
          options
        );

        mealItems.push(...dishItems);
        sessionCost += meal.estimatedCost || dish.cost || 0;
      });

      if (mealItems.length > 0) {
        // Consolidate duplicate ingredients
        const consolidatedItems = this.consolidateIngredients(mealItems);

        sessions.push({
          id: `meal-type-${mealType}-${Date.now()}`,
          type: 'meal-package',
          name: this.getMealTypeDisplayName(mealType),
          description: `Nguyên liệu cho tất cả bữa ${this.getMealTypeDisplayName(mealType).toLowerCase()} trong tuần`,
          addedAt: new Date().toISOString(),
          items: consolidatedItems,
          totalEstimatedCost: sessionCost,
          isCollapsed: false,
          isSelected: true,
          color: this.getMealTypeColor(mealType),
          icon: this.getMealTypeIcon(mealType)
        });
      }
    }

    return sessions;
  }

  /**
   * Tạo sessions theo ngày
   */
  private async createDailySessions(
    weekPlan: WeeklyMealPlan,
    dishesData: VietnameseDish[],
    options: MealPlanShoppingOptions
  ): Promise<ShoppingSession[]> {
    const sessions: ShoppingSession[] = [];

    weekPlan.days.forEach((day, dayIndex) => {
      if (options.excludeDays?.includes(day.date)) return;

      const dayItems: ShoppingCartItem[] = [];
      let dayCost = 0;

      // Process all meals for this day
      ['breakfast', 'lunch', 'dinner', 'snack'].forEach(mealType => {
        const meal = (day as any)[mealType] as MealSlot;
        if (!meal?.dishId) return;

        const dish = dishesData.find(d => d.id === meal.dishId);
        if (!dish) return;

        const dishItems = this.convertDishToShoppingItems(
          dish,
          meal,
          day,
          mealType,
          options
        );

        dayItems.push(...dishItems);
        dayCost += meal.estimatedCost || dish.cost || 0;
      });

      if (dayItems.length > 0) {
        sessions.push({
          id: `day-${dayIndex}-${Date.now()}`,
          type: 'meal-package',
          name: `${day.dayOfWeek} (${new Date(day.date).toLocaleDateString('vi-VN')})`,
          description: `Nguyên liệu cho tất cả bữa ăn trong ngày`,
          addedAt: new Date().toISOString(),
          items: dayItems,
          totalEstimatedCost: dayCost,
          isCollapsed: false,
          isSelected: true,
          color: '#3B82F6',
          icon: '📅'
        });
      }
    });

    return sessions;
  }

  /**
   * Chuyển đổi món ăn thành shopping items
   */
  private convertDishToShoppingItems(
    dish: VietnameseDish,
    meal: MealSlot,
    day: DayMealPlan,
    mealType: string,
    options: MealPlanShoppingOptions
  ): ShoppingCartItem[] {
    const items: ShoppingCartItem[] = [];

    if (!dish.ingredients || dish.ingredients.length === 0) {
      return items;
    }

    dish.ingredients.forEach((ingredient, index) => {
      // Parse ingredient string (e.g., "500g thịt heo", "2 củ hành tím")
      const parsed = this.parseIngredient(ingredient);
      
      if (parsed) {
        // Adjust quantity based on servings if needed
        let adjustedQuantity = parsed.quantity;
        if (options.adjustForServings && options.targetServings && dish.servings) {
          adjustedQuantity = (parsed.quantity * options.targetServings) / dish.servings;
        }

        // Estimate price for this ingredient
        const estimatedPrice = this.estimateIngredientPrice(parsed.name, adjustedQuantity, parsed.unit);

        items.push({
          id: `${meal.id}-ingredient-${index}-${Date.now()}`,
          sessionId: '', // Will be set by session
          name: parsed.name,
          quantity: adjustedQuantity,
          unit: parsed.unit,
          category: this.categorizeIngredient(parsed.name),
          estimatedPrice,
          source: {
            type: 'meal',
            id: dish.id,
            name: `${dish.name} (${day.dayOfWeek} - ${this.getMealTypeDisplayName(mealType)})`
          },
          notes: `Cho món: ${dish.name}`,
          isChecked: false,
          addedAt: new Date().toISOString(),
          priority: this.getIngredientPriority(parsed.name)
        });
      }
    });

    return items;
  }

  /**
   * Parse ingredient string thành quantity, unit, name
   */
  private parseIngredient(ingredient: string): { quantity: number; unit: string; name: string } | null {
    // Patterns for Vietnamese ingredients
    const patterns = [
      /^(\d+(?:\.\d+)?)\s*(g|kg|ml|l|thìa|muỗng|chén|bát|củ|quả|con|miếng|lá|bó|nắm)\s+(.+)$/i,
      /^(\d+(?:\.\d+)?)\s+(.+)$/i, // Just number + name
      /^(.+)$/i // Just name (assume quantity = 1)
    ];

    for (const pattern of patterns) {
      const match = ingredient.match(pattern);
      if (match) {
        if (match.length === 4) {
          // Has quantity, unit, name
          return {
            quantity: parseFloat(match[1]),
            unit: match[2],
            name: match[3].trim()
          };
        } else if (match.length === 3) {
          // Has quantity, name (no unit)
          return {
            quantity: parseFloat(match[1]),
            unit: 'phần',
            name: match[2].trim()
          };
        } else {
          // Just name
          return {
            quantity: 1,
            unit: 'phần',
            name: match[1].trim()
          };
        }
      }
    }

    return null;
  }

  /**
   * Gộp các nguyên liệu trùng lặp
   */
  private consolidateIngredients(items: ShoppingCartItem[]): ShoppingCartItem[] {
    const consolidated = new Map<string, ShoppingCartItem>();

    items.forEach(item => {
      const key = `${item.name}-${item.unit}`;
      
      if (consolidated.has(key)) {
        const existing = consolidated.get(key)!;
        existing.quantity += item.quantity;
        existing.estimatedPrice = (existing.estimatedPrice || 0) + (item.estimatedPrice || 0);
        existing.notes = `${existing.notes}, ${item.notes}`;
      } else {
        consolidated.set(key, { ...item });
      }
    });

    return Array.from(consolidated.values());
  }

  /**
   * Ước tính giá nguyên liệu
   */
  private estimateIngredientPrice(name: string, quantity: number, unit: string): number {
    // Simplified pricing logic - in real app, use IngredientPricingService
    const basePrices: { [key: string]: number } = {
      'thịt heo': 120000, // per kg
      'thịt gà': 100000,
      'cá': 80000,
      'rau': 20000,
      'hành': 30000,
      'tỏi': 50000,
      'gừng': 40000,
      'nước mắm': 25000, // per bottle
      'dầu ăn': 35000,
      'đường': 20000,
      'muối': 10000
    };

    // Find matching ingredient
    const matchedKey = Object.keys(basePrices).find(key => 
      name.toLowerCase().includes(key)
    );

    if (matchedKey) {
      const basePrice = basePrices[matchedKey];
      // Convert to appropriate unit and calculate
      if (unit === 'kg') return basePrice * quantity;
      if (unit === 'g') return (basePrice * quantity) / 1000;
      return basePrice * 0.1 * quantity; // Default small portion
    }

    return 5000 * quantity; // Default price
  }

  /**
   * Phân loại nguyên liệu
   */
  private categorizeIngredient(name: string): string {
    const categories = {
      'Thịt cá': ['thịt', 'cá', 'tôm', 'cua', 'gà', 'heo', 'bò'],
      'Rau củ': ['rau', 'củ', 'cải', 'cà', 'hành', 'tỏi', 'gừng'],
      'Gia vị': ['nước mắm', 'dầu', 'đường', 'muối', 'tiêu', 'ớt'],
      'Khác': []
    };

    for (const [category, keywords] of Object.entries(categories)) {
      if (keywords.some(keyword => name.toLowerCase().includes(keyword))) {
        return category;
      }
    }

    return 'Khác';
  }

  /**
   * Xác định độ ưu tiên nguyên liệu
   */
  private getIngredientPriority(name: string): 'high' | 'medium' | 'low' {
    const highPriority = ['thịt', 'cá', 'gà'];
    const lowPriority = ['gia vị', 'muối', 'đường'];

    if (highPriority.some(keyword => name.toLowerCase().includes(keyword))) {
      return 'high';
    }
    if (lowPriority.some(keyword => name.toLowerCase().includes(keyword))) {
      return 'low';
    }
    return 'medium';
  }

  /**
   * Helper methods
   */
  private getMealTypeDisplayName(mealType: string): string {
    const names = {
      breakfast: 'Bữa Sáng',
      lunch: 'Bữa Trưa', 
      dinner: 'Bữa Tối',
      snack: 'Ăn Vặt'
    };
    return names[mealType as keyof typeof names] || mealType;
  }

  private getMealTypeColor(mealType: string): string {
    const colors = {
      breakfast: '#F59E0B',
      lunch: '#10B981',
      dinner: '#3B82F6',
      snack: '#8B5CF6'
    };
    return colors[mealType as keyof typeof colors] || '#6B7280';
  }

  private getMealTypeIcon(mealType: string): string {
    const icons = {
      breakfast: '☀️',
      lunch: '🍽️',
      dinner: '🌙',
      snack: '🍪'
    };
    return icons[mealType as keyof typeof icons] || '🍽️';
  }

  private generateSummary(sessions: ShoppingSession[], allItems: ShoppingCartItem[]) {
    const summary = {
      breakfastItems: 0,
      lunchItems: 0,
      dinnerItems: 0,
      snackItems: 0,
      uniqueIngredients: new Set<string>(),
      estimatedShoppingTime: 0
    };

    allItems.forEach(item => {
      summary.uniqueIngredients.add(item.name);
      
      if (item.source.name.includes('Sáng')) summary.breakfastItems++;
      else if (item.source.name.includes('Trưa')) summary.lunchItems++;
      else if (item.source.name.includes('Tối')) summary.dinnerItems++;
      else if (item.source.name.includes('Vặt')) summary.snackItems++;
    });

    // Estimate shopping time: 2 minutes per unique ingredient + 10 minutes base
    summary.estimatedShoppingTime = Math.ceil(summary.uniqueIngredients.size * 2 + 10);

    return {
      ...summary,
      uniqueIngredients: summary.uniqueIngredients.size
    };
  }

  private generateShoppingTips(sessions: ShoppingSession[], weekPlan: WeeklyMealPlan): string[] {
    const tips: string[] = [];

    tips.push('💡 Mua nguyên liệu tươi sống vào cuối để đảm bảo độ tươi');
    tips.push('🛒 Mang túi vải để bảo vệ môi trường');
    tips.push('💰 So sánh giá ở nhiều quầy trước khi mua');
    
    if (sessions.length > 3) {
      tips.push('📝 In danh sách này ra để không quên món nào');
    }

    return tips;
  }
}

export const mealPlanToShoppingService = new MealPlanToShoppingService();

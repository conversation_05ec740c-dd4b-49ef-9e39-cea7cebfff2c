/**
 * Service để chuyển đổi đơn vị đo lường
 * Hỗ trợ hệ thống đơn vị chuẩn hóa Việt Nam
 */

export interface Unit {
  id: string;
  name: string;
  displayName: string;
  type: 'weight' | 'volume' | 'count';
  baseUnitId: string | null;
  conversionFactor: number;
  isBaseUnit: boolean;
}

export interface ConversionResult {
  success: boolean;
  convertedQuantity: number;
  fromUnit: Unit;
  toUnit: Unit;
  error?: string;
}

export class UnitConversionService {
  private static instance: UnitConversionService;
  private units: Map<string, Unit> = new Map();
  private unitsByName: Map<string, Unit> = new Map();

  private constructor() {
    this.initializeUnits();
  }

  public static getInstance(): UnitConversionService {
    if (!UnitConversionService.instance) {
      UnitConversionService.instance = new UnitConversionService();
    }
    return UnitConversionService.instance;
  }

  /**
   * Khởi tạo dữ liệu đơn vị (trong thực tế sẽ load từ database)
   */
  private initializeUnits(): void {
    const unitsData: Unit[] = [
      // Weight units (base: lạng = 100g)
      { id: 'lang', name: 'lang', displayName: 'Lạng (100g)', type: 'weight', baseUnitId: null, conversionFactor: 1.0, isBaseUnit: true },
      { id: 'gram', name: 'gram', displayName: 'Gram (g)', type: 'weight', baseUnitId: 'lang', conversionFactor: 0.01, isBaseUnit: false },
      { id: 'kilogram', name: 'kilogram', displayName: 'Kilogram (kg)', type: 'weight', baseUnitId: 'lang', conversionFactor: 10.0, isBaseUnit: false },
      { id: 'yen', name: 'yen', displayName: 'Yến (37.5g)', type: 'weight', baseUnitId: 'lang', conversionFactor: 0.375, isBaseUnit: false },

      // Volume units (base: lít)
      { id: 'liter', name: 'liter', displayName: 'Lít (l)', type: 'volume', baseUnitId: null, conversionFactor: 1.0, isBaseUnit: true },
      { id: 'milliliter', name: 'milliliter', displayName: 'Mili-lít (ml)', type: 'volume', baseUnitId: 'liter', conversionFactor: 0.001, isBaseUnit: false },
      { id: 'teaspoon', name: 'teaspoon', displayName: 'Thìa (5ml)', type: 'volume', baseUnitId: 'liter', conversionFactor: 0.005, isBaseUnit: false },
      { id: 'tablespoon', name: 'tablespoon', displayName: 'Muỗng canh (15ml)', type: 'volume', baseUnitId: 'liter', conversionFactor: 0.015, isBaseUnit: false },
      { id: 'cup', name: 'cup', displayName: 'Cốc (250ml)', type: 'volume', baseUnitId: 'liter', conversionFactor: 0.25, isBaseUnit: false },

      // Count units (base: cái)
      { id: 'piece', name: 'piece', displayName: 'Cái', type: 'count', baseUnitId: null, conversionFactor: 1.0, isBaseUnit: true },
      { id: 'bulb', name: 'bulb', displayName: 'Củ', type: 'count', baseUnitId: 'piece', conversionFactor: 1.0, isBaseUnit: false },
      { id: 'fruit', name: 'fruit', displayName: 'Quả', type: 'count', baseUnitId: 'piece', conversionFactor: 1.0, isBaseUnit: false },
      { id: 'clove', name: 'clove', displayName: 'Tép', type: 'count', baseUnitId: 'piece', conversionFactor: 1.0, isBaseUnit: false },
      { id: 'bunch', name: 'bunch', displayName: 'Bó', type: 'count', baseUnitId: 'piece', conversionFactor: 1.0, isBaseUnit: false },
      { id: 'dozen', name: 'dozen', displayName: 'Tá (12 cái)', type: 'count', baseUnitId: 'piece', conversionFactor: 12.0, isBaseUnit: false },

      // Special units
      { id: 'pinch', name: 'pinch', displayName: 'Nhúm', type: 'volume', baseUnitId: 'liter', conversionFactor: 0.001, isBaseUnit: false },
      { id: 'dash', name: 'dash', displayName: 'Ít', type: 'volume', baseUnitId: 'liter', conversionFactor: 0.002, isBaseUnit: false },
    ];

    unitsData.forEach(unit => {
      this.units.set(unit.id, unit);
      this.unitsByName.set(unit.name.toLowerCase(), unit);
    });
  }

  /**
   * Lấy thông tin đơn vị theo ID
   */
  public getUnitById(unitId: string): Unit | null {
    return this.units.get(unitId) || null;
  }

  /**
   * Lấy thông tin đơn vị theo tên
   */
  public getUnitByName(unitName: string): Unit | null {
    return this.unitsByName.get(unitName.toLowerCase()) || null;
  }

  /**
   * Lấy đơn vị cơ sở của một loại
   */
  public getBaseUnit(type: 'weight' | 'volume' | 'count'): Unit | null {
    for (const unit of this.units.values()) {
      if (unit.type === type && unit.isBaseUnit) {
        return unit;
      }
    }
    return null;
  }

  /**
   * Chuyển đổi số lượng từ đơn vị này sang đơn vị khác
   */
  public convert(quantity: number, fromUnitId: string, toUnitId: string): ConversionResult {
    const fromUnit = this.getUnitById(fromUnitId);
    const toUnit = this.getUnitById(toUnitId);

    if (!fromUnit) {
      return {
        success: false,
        convertedQuantity: 0,
        fromUnit: {} as Unit,
        toUnit: {} as Unit,
        error: `Không tìm thấy đơn vị nguồn: ${fromUnitId}`
      };
    }

    if (!toUnit) {
      return {
        success: false,
        convertedQuantity: 0,
        fromUnit,
        toUnit: {} as Unit,
        error: `Không tìm thấy đơn vị đích: ${toUnitId}`
      };
    }

    // Kiểm tra cùng loại đơn vị
    if (fromUnit.type !== toUnit.type) {
      return {
        success: false,
        convertedQuantity: 0,
        fromUnit,
        toUnit,
        error: `Không thể chuyển đổi giữa ${fromUnit.type} và ${toUnit.type}`
      };
    }

    // Chuyển đổi về đơn vị cơ sở rồi chuyển sang đơn vị đích
    const baseQuantity = quantity * fromUnit.conversionFactor;
    const convertedQuantity = baseQuantity / toUnit.conversionFactor;

    return {
      success: true,
      convertedQuantity,
      fromUnit,
      toUnit
    };
  }

  /**
   * Chuyển đổi về đơn vị cơ sở
   */
  public convertToBaseUnit(quantity: number, unitId: string): ConversionResult {
    const unit = this.getUnitById(unitId);
    if (!unit) {
      return {
        success: false,
        convertedQuantity: 0,
        fromUnit: {} as Unit,
        toUnit: {} as Unit,
        error: `Không tìm thấy đơn vị: ${unitId}`
      };
    }

    const baseUnit = this.getBaseUnit(unit.type);
    if (!baseUnit) {
      return {
        success: false,
        convertedQuantity: 0,
        fromUnit: unit,
        toUnit: {} as Unit,
        error: `Không tìm thấy đơn vị cơ sở cho loại: ${unit.type}`
      };
    }

    return this.convert(quantity, unitId, baseUnit.id);
  }

  /**
   * Parse chuỗi nguyên liệu thành quantity và unit
   */
  public parseIngredientString(ingredientStr: string): { quantity: number; unit: string; name: string } {
    // Regex để parse các pattern phổ biến
    const patterns = [
      // "200g thịt bò", "1.5kg gạo"
      /^(\d+(?:\.\d+)?)\s*([a-zA-Z]+)\s+(.+)$/,
      // "1 củ hành", "2 quả cà chua"
      /^(\d+(?:\.\d+)?)\s+(củ|quả|cây|lá|miếng|tép|bó|gói|chai|lon|hộp|cái)\s+(.+)$/,
      // "1 muỗng canh nước mắm"
      /^(\d+(?:\.\d+)?)\s+(muỗng\s+canh|muỗng|thìa|cốc|chén|bát)\s+(.+)$/,
      // "ít muối", "vài giọt dầu"
      /^(ít|vài|chút|nhúm)\s+(.+)$/,
    ];

    for (const pattern of patterns) {
      const match = ingredientStr.trim().match(pattern);
      if (match) {
        if (pattern === patterns[3]) { // Special case for "ít", "vài"
          const quantityMap: { [key: string]: number } = {
            'ít': 0.5,
            'vài': 2,
            'chút': 0.5,
            'nhúm': 1
          };
          return {
            quantity: quantityMap[match[1]] || 1,
            unit: match[1] === 'nhúm' ? 'pinch' : 'teaspoon',
            name: match[2]
          };
        } else {
          let unit = match[2].toLowerCase();
          
          // Normalize unit names
          const unitMap: { [key: string]: string } = {
            'g': 'gram',
            'kg': 'kilogram',
            'ml': 'milliliter',
            'l': 'liter',
            'lít': 'liter',
            'củ': 'bulb',
            'quả': 'fruit',
            'cây': 'stalk',
            'lá': 'leaf',
            'miếng': 'slice',
            'tép': 'clove',
            'bó': 'bunch',
            'gói': 'pack',
            'chai': 'bottle',
            'lon': 'can',
            'hộp': 'box',
            'cái': 'piece',
            'muỗng canh': 'tablespoon',
            'muỗng': 'tablespoon',
            'thìa': 'teaspoon',
            'cốc': 'cup',
            'chén': 'cup',
            'bát': 'bowl'
          };

          unit = unitMap[unit] || unit;

          return {
            quantity: parseFloat(match[1]),
            unit,
            name: match[3]
          };
        }
      }
    }

    // Fallback: treat as 1 piece
    return {
      quantity: 1,
      unit: 'piece',
      name: ingredientStr
    };
  }

  /**
   * Lấy danh sách đơn vị theo loại
   */
  public getUnitsByType(type: 'weight' | 'volume' | 'count'): Unit[] {
    return Array.from(this.units.values()).filter(unit => unit.type === type);
  }

  /**
   * Kiểm tra xem hai đơn vị có cùng loại không
   */
  public areCompatibleUnits(unitId1: string, unitId2: string): boolean {
    const unit1 = this.getUnitById(unitId1);
    const unit2 = this.getUnitById(unitId2);
    return unit1 && unit2 && unit1.type === unit2.type;
  }
}

import { SupabaseAdapter } from './adapters/SupabaseAdapter';
import { LocalStorageAdapter } from './adapters/LocalStorageAdapter';
import { supabaseHelpers } from '@/config/supabase';
import type { Recipe, MealPlan, ShoppingList } from './interfaces/DatabaseAdapter';

export interface MigrationResult {
  success: boolean;
  message: string;
  details: {
    recipesCount: number;
    mealPlansCount: number;
    shoppingListsCount: number;
    errors: string[];
  };
}

export interface MigrationProgress {
  stage: 'checking' | 'recipes' | 'mealPlans' | 'shoppingLists' | 'cleanup' | 'complete' | 'error';
  progress: number; // 0-100
  message: string;
  currentItem?: string;
}

export class DataMigrationService {
  private localAdapter: LocalStorageAdapter;
  private supabaseAdapter: SupabaseAdapter;
  private progressCallback?: (progress: MigrationProgress) => void;

  constructor(progressCallback?: (progress: MigrationProgress) => void) {
    this.localAdapter = new LocalStorageAdapter();
    this.supabaseAdapter = new SupabaseAdapter();
    this.progressCallback = progressCallback;
  }

  private updateProgress(progress: MigrationProgress): void {
    if (this.progressCallback) {
      this.progressCallback(progress);
    }
    console.log(`📊 Migration Progress: ${progress.stage} - ${progress.progress}% - ${progress.message}`);
  }

  async checkMigrationNeeded(): Promise<{
    needed: boolean;
    localDataCount: number;
    supabaseDataCount: number;
    details: {
      localRecipes: number;
      localMealPlans: number;
      localShoppingLists: number;
      supabaseRecipes: number;
      supabaseMealPlans: number;
      supabaseShoppingLists: number;
    };
  }> {
    this.updateProgress({
      stage: 'checking',
      progress: 0,
      message: 'Checking data in localStorage and Supabase...'
    });

    // Check localStorage data
    const localRecipes = await this.localAdapter.getRecipes();
    const localMealPlans = await this.localAdapter.getMealPlans('default');
    const localShoppingLists = await this.localAdapter.getShoppingLists('default');

    // Check Supabase data
    let supabaseRecipes: Recipe[] = [];
    let supabaseMealPlans: MealPlan[] = [];
    let supabaseShoppingLists: ShoppingList[] = [];

    try {
      const isConnected = await this.supabaseAdapter.initialize();
      if (isConnected) {
        supabaseRecipes = await this.supabaseAdapter.getRecipes();
        supabaseMealPlans = await this.supabaseAdapter.getMealPlans('default');
        supabaseShoppingLists = await this.supabaseAdapter.getShoppingLists('default');
      }
    } catch (error) {
      console.warn('Could not check Supabase data:', error);
    }

    const localDataCount = localRecipes.length + localMealPlans.length + localShoppingLists.length;
    const supabaseDataCount = supabaseRecipes.length + supabaseMealPlans.length + supabaseShoppingLists.length;

    const details = {
      localRecipes: localRecipes.length,
      localMealPlans: localMealPlans.length,
      localShoppingLists: localShoppingLists.length,
      supabaseRecipes: supabaseRecipes.length,
      supabaseMealPlans: supabaseMealPlans.length,
      supabaseShoppingLists: supabaseShoppingLists.length,
    };

    // Migration is needed if there's data in localStorage but not in Supabase
    const needed = localDataCount > 0 && supabaseDataCount === 0;

    this.updateProgress({
      stage: 'checking',
      progress: 100,
      message: `Found ${localDataCount} items in localStorage, ${supabaseDataCount} items in Supabase`
    });

    return {
      needed,
      localDataCount,
      supabaseDataCount,
      details
    };
  }

  async migrateData(options: {
    includeRecipes?: boolean;
    includeMealPlans?: boolean;
    includeShoppingLists?: boolean;
    clearLocalStorageAfter?: boolean;
    userId?: string;
  } = {}): Promise<MigrationResult> {
    const {
      includeRecipes = true,
      includeMealPlans = true,
      includeShoppingLists = true,
      clearLocalStorageAfter = false,
      userId = 'default'
    } = options;

    const result: MigrationResult = {
      success: false,
      message: '',
      details: {
        recipesCount: 0,
        mealPlansCount: 0,
        shoppingListsCount: 0,
        errors: []
      }
    };

    try {
      // Step 1: Ensure Supabase is connected
      this.updateProgress({
        stage: 'checking',
        progress: 5,
        message: 'Checking Supabase connection...'
      });

      const isConnected = await this.supabaseAdapter.initialize();
      if (!isConnected) {
        // Try to setup database
        const setupResult = await supabaseHelpers.setupDatabase();
        if (!setupResult.success) {
          throw new Error(`Supabase connection failed: ${setupResult.message}`);
        }
        
        // Retry connection
        const retryConnected = await this.supabaseAdapter.initialize();
        if (!retryConnected) {
          throw new Error('Supabase connection failed after database setup');
        }
      }

      // Step 2: Migrate Recipes
      if (includeRecipes) {
        this.updateProgress({
          stage: 'recipes',
          progress: 20,
          message: 'Migrating recipes...'
        });

        const localRecipes = await this.localAdapter.getRecipes();
        for (let i = 0; i < localRecipes.length; i++) {
          const recipe = localRecipes[i];
          try {
            this.updateProgress({
              stage: 'recipes',
              progress: 20 + (i / localRecipes.length) * 20,
              message: `Migrating recipe: ${recipe.name}`,
              currentItem: recipe.name
            });

            // Remove local-specific fields and create in Supabase
            const { id, createdAt, updatedAt, ...recipeData } = recipe;
            await this.supabaseAdapter.createRecipe(recipeData);
            result.details.recipesCount++;
          } catch (error) {
            const errorMsg = `Failed to migrate recipe "${recipe.name}": ${error.message}`;
            result.details.errors.push(errorMsg);
            console.error(errorMsg);
          }
        }
      }

      // Step 3: Migrate Meal Plans
      if (includeMealPlans) {
        this.updateProgress({
          stage: 'mealPlans',
          progress: 40,
          message: 'Migrating meal plans...'
        });

        const localMealPlans = await this.localAdapter.getMealPlans(userId);
        for (let i = 0; i < localMealPlans.length; i++) {
          const mealPlan = localMealPlans[i];
          try {
            this.updateProgress({
              stage: 'mealPlans',
              progress: 40 + (i / localMealPlans.length) * 20,
              message: `Migrating meal plan: ${mealPlan.name}`,
              currentItem: mealPlan.name
            });

            const { id, createdAt, updatedAt, ...mealPlanData } = mealPlan;
            await this.supabaseAdapter.createMealPlan(mealPlanData);
            result.details.mealPlansCount++;
          } catch (error) {
            const errorMsg = `Failed to migrate meal plan "${mealPlan.name}": ${error.message}`;
            result.details.errors.push(errorMsg);
            console.error(errorMsg);
          }
        }
      }

      // Step 4: Migrate Shopping Lists
      if (includeShoppingLists) {
        this.updateProgress({
          stage: 'shoppingLists',
          progress: 60,
          message: 'Migrating shopping lists...'
        });

        const localShoppingLists = await this.localAdapter.getShoppingLists(userId);
        for (let i = 0; i < localShoppingLists.length; i++) {
          const shoppingList = localShoppingLists[i];
          try {
            this.updateProgress({
              stage: 'shoppingLists',
              progress: 60 + (i / localShoppingLists.length) * 20,
              message: `Migrating shopping list: ${shoppingList.name}`,
              currentItem: shoppingList.name
            });

            const { id, createdAt, updatedAt, ...shoppingListData } = shoppingList;
            await this.supabaseAdapter.createShoppingList(shoppingListData);
            result.details.shoppingListsCount++;
          } catch (error) {
            const errorMsg = `Failed to migrate shopping list "${shoppingList.name}": ${error.message}`;
            result.details.errors.push(errorMsg);
            console.error(errorMsg);
          }
        }
      }

      // Step 5: Cleanup (optional)
      if (clearLocalStorageAfter) {
        this.updateProgress({
          stage: 'cleanup',
          progress: 90,
          message: 'Cleaning up localStorage...'
        });

        // Clear localStorage data
        const keys = Object.keys(localStorage).filter(key => key.startsWith('angiday_'));
        keys.forEach(key => localStorage.removeItem(key));
      }

      // Step 6: Complete
      this.updateProgress({
        stage: 'complete',
        progress: 100,
        message: 'Migration completed successfully!'
      });

      result.success = true;
      result.message = `Migration completed! Migrated ${result.details.recipesCount} recipes, ${result.details.mealPlansCount} meal plans, and ${result.details.shoppingListsCount} shopping lists.`;

      if (result.details.errors.length > 0) {
        result.message += ` ${result.details.errors.length} errors occurred.`;
      }

    } catch (error) {
      this.updateProgress({
        stage: 'error',
        progress: 0,
        message: `Migration failed: ${error.message}`
      });

      result.success = false;
      result.message = `Migration failed: ${error.message}`;
      result.details.errors.push(error.message);
    }

    return result;
  }

  async createBackup(): Promise<{
    success: boolean;
    backupData?: {
      recipes: Recipe[];
      mealPlans: MealPlan[];
      shoppingLists: ShoppingList[];
      timestamp: string;
    };
    error?: string;
  }> {
    try {
      const recipes = await this.localAdapter.getRecipes();
      const mealPlans = await this.localAdapter.getMealPlans('default');
      const shoppingLists = await this.localAdapter.getShoppingLists('default');

      const backupData = {
        recipes,
        mealPlans,
        shoppingLists,
        timestamp: new Date().toISOString()
      };

      // Save backup to localStorage with special key
      localStorage.setItem('angiday_backup_' + Date.now(), JSON.stringify(backupData));

      return {
        success: true,
        backupData
      };
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }
}

// Export singleton instance
export const dataMigrationService = new DataMigrationService();

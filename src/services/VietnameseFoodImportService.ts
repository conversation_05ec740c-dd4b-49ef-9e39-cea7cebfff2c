/**
 * Service import và lưu dữ liệu món ăn Việt Nam vào database
 * Hỗ trợ import từ nhiều nguồn và tự động phân loại
 */

import { VietnameseDish } from '../data/vietnameseFoodCategories';
import { allVietnameseDishes } from '../data/vietnameseFoodData';
import { vietnameseFoodScrapingService, ScrapingResult } from './VietnameseFoodScrapingService';
import { AdapterFactory } from './adapters/AdapterFactory';
import { DatabaseAdapter } from './interfaces/DatabaseAdapter';

export interface ImportOptions {
  source?: 'local' | 'scraping' | 'mixed';
  batchSize?: number;
  validateData?: boolean;
  skipDuplicates?: boolean;
  updateExisting?: boolean;
}

export interface ImportResult {
  success: boolean;
  totalProcessed: number;
  totalImported: number;
  totalSkipped: number;
  totalErrors: number;
  errors: string[];
  importedIds: string[];
  duration: number;
}

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

class VietnameseFoodImportService {
  private dbAdapter: DatabaseAdapter;

  constructor() {
    this.dbAdapter = AdapterFactory.createAdapter();
  }

  /**
   * Import tất cả món ăn từ dữ liệu local
   */
  async importLocalData(options: ImportOptions = {}): Promise<ImportResult> {
    const startTime = Date.now();
    console.log('🚀 Bắt đầu import dữ liệu món ăn Việt Nam từ local...');

    const result: ImportResult = {
      success: false,
      totalProcessed: 0,
      totalImported: 0,
      totalSkipped: 0,
      totalErrors: 0,
      errors: [],
      importedIds: [],
      duration: 0
    };

    try {
      const dishes = allVietnameseDishes;
      result.totalProcessed = dishes.length;

      console.log(`📊 Tổng số món ăn cần import: ${dishes.length}`);

      // Process in batches
      const batchSize = options.batchSize || 10;
      for (let i = 0; i < dishes.length; i += batchSize) {
        const batch = dishes.slice(i, i + batchSize);
        const batchResult = await this.processBatch(batch, options);

        result.totalImported += batchResult.imported;
        result.totalSkipped += batchResult.skipped;
        result.totalErrors += batchResult.errors.length;
        result.errors.push(...batchResult.errors);
        result.importedIds.push(...batchResult.importedIds);

        console.log(`✅ Đã xử lý batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(dishes.length / batchSize)}`);
      }

      result.success = result.totalErrors === 0 || result.totalImported > 0;
      result.duration = Date.now() - startTime;

      console.log(`🎉 Hoàn thành import! Imported: ${result.totalImported}, Skipped: ${result.totalSkipped}, Errors: ${result.totalErrors}`);

      return result;
    } catch (error) {
      result.errors.push(`Lỗi tổng quát: ${error}`);
      result.duration = Date.now() - startTime;
      console.error('❌ Lỗi khi import dữ liệu:', error);
      return result;
    }
  }

  /**
   * Import dữ liệu từ scraping
   */
  async importScrapedData(sources: string[] = [], options: ImportOptions = {}): Promise<ImportResult> {
    const startTime = Date.now();
    console.log('🕷️ Bắt đầu cào và import dữ liệu từ các nguồn trực tuyến...');

    const result: ImportResult = {
      success: false,
      totalProcessed: 0,
      totalImported: 0,
      totalSkipped: 0,
      totalErrors: 0,
      errors: [],
      importedIds: [],
      duration: 0
    };

    try {
      let scrapingResults: ScrapingResult[] = [];

      if (sources.length === 0) {
        // Scrape from all sources
        scrapingResults = await vietnameseFoodScrapingService.scrapeFromAllSources({
          maxPages: 2,
          delay: 1000
        });
      } else {
        // Scrape from specific sources
        for (const source of sources) {
          const scrapingResult = await vietnameseFoodScrapingService.scrapeFromSource(source, {
            maxPages: 2,
            delay: 1000
          });
          scrapingResults.push(scrapingResult);
        }
      }

      // Process scraped data
      for (const scrapingResult of scrapingResults) {
        if (scrapingResult.success && scrapingResult.data) {
          console.log(`📥 Xử lý ${scrapingResult.data.length} món ăn từ ${scrapingResult.source}`);

          const batchResult = await this.processBatch(scrapingResult.data, options);

          result.totalProcessed += scrapingResult.data.length;
          result.totalImported += batchResult.imported;
          result.totalSkipped += batchResult.skipped;
          result.totalErrors += batchResult.errors.length;
          result.errors.push(...batchResult.errors);
          result.importedIds.push(...batchResult.importedIds);
        } else {
          result.errors.push(`Lỗi cào dữ liệu từ ${scrapingResult.source}: ${scrapingResult.error}`);
        }
      }

      result.success = result.totalErrors === 0 || result.totalImported > 0;
      result.duration = Date.now() - startTime;

      console.log(`🎉 Hoàn thành import scraped data! Imported: ${result.totalImported}, Errors: ${result.totalErrors}`);

      return result;
    } catch (error) {
      result.errors.push(`Lỗi tổng quát khi scraping: ${error}`);
      result.duration = Date.now() - startTime;
      console.error('❌ Lỗi khi import scraped data:', error);
      return result;
    }
  }

  /**
   * Xử lý một batch món ăn
   */
  private async processBatch(dishes: VietnameseDish[], options: ImportOptions): Promise<{
    imported: number;
    skipped: number;
    errors: string[];
    importedIds: string[];
  }> {
    const result = {
      imported: 0,
      skipped: 0,
      errors: [],
      importedIds: []
    };

    for (const dish of dishes) {
      try {
        // Validate data if required
        if (options.validateData) {
          const validation = this.validateDish(dish);
          if (!validation.isValid) {
            result.errors.push(`Validation failed for ${dish.name}: ${validation.errors.join(', ')}`);
            continue;
          }
        }

        // Check for duplicates
        if (options.skipDuplicates) {
          const existing = await this.findExistingDish(dish);
          if (existing) {
            if (options.updateExisting) {
              await this.updateDish(existing.id, dish);
              result.imported++;
              result.importedIds.push(existing.id);
              console.log(`🔄 Updated: ${dish.name}`);
            } else {
              result.skipped++;
              console.log(`⏭️ Skipped duplicate: ${dish.name}`);
            }
            continue;
          }
        }

        // Convert to database format and save
        const dbRecipe = this.convertToDbFormat(dish);
        const savedRecipe = await this.dbAdapter.createRecipe(dbRecipe);

        result.imported++;
        result.importedIds.push(savedRecipe.id);
        console.log(`✅ Imported: ${dish.name}`);

      } catch (error) {
        result.errors.push(`Error importing ${dish.name}: ${error}`);
        console.error(`❌ Error importing ${dish.name}:`, error);
      }
    }

    return result;
  }

  /**
   * Validate dữ liệu món ăn
   */
  private validateDish(dish: VietnameseDish): ValidationResult {
    const result: ValidationResult = {
      isValid: true,
      errors: [],
      warnings: []
    };

    // Required fields
    if (!dish.name || dish.name.trim().length === 0) {
      result.errors.push('Tên món ăn không được để trống');
    }

    if (!dish.ingredients || dish.ingredients.length === 0) {
      result.errors.push('Danh sách nguyên liệu không được để trống');
    }

    if (!dish.instructions || dish.instructions.length === 0) {
      result.errors.push('Hướng dẫn nấu không được để trống');
    }

    // Validate nutrition data
    if (dish.nutrition) {
      if (dish.nutrition.calories < 0 || dish.nutrition.calories > 2000) {
        result.warnings.push('Calories có vẻ không hợp lý');
      }
      if (dish.nutrition.protein < 0 || dish.nutrition.protein > 100) {
        result.warnings.push('Protein có vẻ không hợp lý');
      }
    }

    // Validate cost
    if (dish.cost < 0 || dish.cost > 500000) {
      result.warnings.push('Giá tiền có vẻ không hợp lý');
    }

    // Validate servings
    if (dish.servings < 1 || dish.servings > 20) {
      result.warnings.push('Số khẩu phần có vẻ không hợp lý');
    }

    result.isValid = result.errors.length === 0;
    return result;
  }

  /**
   * Tìm món ăn đã tồn tại trong database
   */
  private async findExistingDish(dish: VietnameseDish): Promise<any | null> {
    try {
      const recipes = await this.dbAdapter.getRecipes();
      return recipes.find(recipe =>
        recipe.name.toLowerCase() === dish.name.toLowerCase() ||
        recipe.id === dish.id
      ) || null;
    } catch (error) {
      console.warn('Lỗi khi tìm kiếm món ăn trùng lặp:', error);
      return null;
    }
  }

  /**
   * Cập nhật món ăn đã tồn tại
   */
  private async updateDish(id: string, dish: VietnameseDish): Promise<void> {
    const dbRecipe = this.convertToDbFormat(dish);
    await this.dbAdapter.updateRecipe(id, dbRecipe);
  }

  /**
   * Chuyển đổi từ VietnameseDish sang format database
   */
  private convertToDbFormat(dish: VietnameseDish): any {
    return {
      name: dish.name,
      description: dish.description,
      ingredients: dish.ingredients,
      instructions: dish.instructions,
      prepTime: this.parseCookingTime(dish.cookingTime).prep,
      cookTime: this.parseCookingTime(dish.cookingTime).cook,
      servings: dish.servings,
      difficulty: this.mapDifficulty(dish.difficulty),
      nutrition: dish.nutrition,
      tags: [
        ...dish.tags,
        dish.categoryId,
        dish.region,
        dish.mealType,
        dish.preparationComplexity
      ],
      imageUrl: null, // Will be added later if available
      userId: null, // System recipe
      isPublic: true,
      source: 'imported',
      rating: 0,
      views: 0
    };
  }

  /**
   * Parse cooking time string
   */
  private parseCookingTime(timeStr: string): { prep: number; cook: number } {
    const totalMinutes = this.parseTimeToMinutes(timeStr);
    // Assume 20% prep time, 80% cook time
    return {
      prep: Math.floor(totalMinutes * 0.2),
      cook: Math.floor(totalMinutes * 0.8)
    };
  }

  private parseTimeToMinutes(timeStr: string): number {
    const hourMatch = timeStr.match(/(\d+)\s*giờ/);
    const minuteMatch = timeStr.match(/(\d+)\s*phút/);

    let totalMinutes = 0;
    if (hourMatch) {
      totalMinutes += parseInt(hourMatch[1]) * 60;
    }
    if (minuteMatch) {
      totalMinutes += parseInt(minuteMatch[1]);
    }

    return totalMinutes || 30; // Default 30 minutes
  }

  /**
   * Map difficulty levels
   */
  private mapDifficulty(difficulty: string): string {
    const mapping: { [key: string]: string } = {
      'easy': 'easy',
      'medium': 'medium',
      'hard': 'hard',
      'dễ': 'easy',
      'trung bình': 'medium',
      'khó': 'hard'
    };

    return mapping[difficulty.toLowerCase()] || 'medium';
  }

  /**
   * Lấy thống kê import
   */
  async getImportStats(): Promise<{
    totalRecipes: number;
    byCategory: { [key: string]: number };
    byRegion: { [key: string]: number };
    byMealType: { [key: string]: number };
  }> {
    try {
      const recipes = await this.dbAdapter.getRecipes();
      const stats = {
        totalRecipes: recipes.length,
        byCategory: {} as { [key: string]: number },
        byRegion: {} as { [key: string]: number },
        byMealType: {} as { [key: string]: number }
      };

      recipes.forEach(recipe => {
        // Count by tags (which include category, region, mealType)
        if (recipe.tags) {
          recipe.tags.forEach((tag: string) => {
            if (tag.includes('_')) {
              // Likely a category ID
              stats.byCategory[tag] = (stats.byCategory[tag] || 0) + 1;
            } else if (['north', 'central', 'south', 'nationwide'].includes(tag)) {
              stats.byRegion[tag] = (stats.byRegion[tag] || 0) + 1;
            } else if (['breakfast', 'lunch', 'dinner', 'snack'].includes(tag)) {
              stats.byMealType[tag] = (stats.byMealType[tag] || 0) + 1;
            }
          });
        }
      });

      return stats;
    } catch (error) {
      console.error('Lỗi khi lấy thống kê:', error);
      return {
        totalRecipes: 0,
        byCategory: {},
        byRegion: {},
        byMealType: {}
      };
    }
  }

  /**
   * Xóa tất cả dữ liệu đã import (để test)
   */
  async clearImportedData(): Promise<{ deleted: number; errors: string[] }> {
    const result = { deleted: 0, errors: [] };

    try {
      const recipes = await this.dbAdapter.getRecipes();
      const importedRecipes = recipes.filter(recipe =>
        recipe.source === 'imported' ||
        recipe.userId === null
      );

      for (const recipe of importedRecipes) {
        try {
          await this.dbAdapter.deleteRecipe(recipe.id);
          result.deleted++;
        } catch (error) {
          result.errors.push(`Lỗi xóa ${recipe.name}: ${error}`);
        }
      }

      console.log(`🗑️ Đã xóa ${result.deleted} món ăn đã import`);
    } catch (error) {
      result.errors.push(`Lỗi tổng quát: ${error}`);
    }

    return result;
  }
}

export const vietnameseFoodImportService = new VietnameseFoodImportService();
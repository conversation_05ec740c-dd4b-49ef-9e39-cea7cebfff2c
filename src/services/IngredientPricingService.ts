/**
 * Service để quản lý giá cả nguyên liệu
 * Tí<PERSON> hợp với hệ thống đơn vị chuẩn hóa
 */

import { UnitConversionService } from './UnitConversionService';

export interface Ingredient {
  id: string;
  name: string;
  displayName: string;
  categoryId: string;
  baseUnitId: string;
  basePricePerUnit: number;
  alternativeNames: string[];
}

export interface IngredientPrice {
  ingredientId: string;
  unitId: string;
  price: number;
  region: string;
  source: string;
  effectiveDate: string;
}

export interface PriceCalculationResult {
  success: boolean;
  totalPrice: number;
  pricePerUnit: number;
  ingredient: Ingredient | null;
  unitUsed: string;
  region: string;
  breakdown?: {
    originalQuantity: number;
    originalUnit: string;
    convertedQuantity: number;
    convertedUnit: string;
    pricePerConvertedUnit: number;
  };
  error?: string;
}

export class IngredientPricingService {
  private static instance: IngredientPricingService;
  private unitService: UnitConversionService;
  private ingredients: Map<string, Ingredient> = new Map();
  private ingredientsByName: Map<string, Ingredient> = new Map();
  private prices: Map<string, IngredientPrice[]> = new Map();

  private constructor() {
    this.unitService = UnitConversionService.getInstance();
    this.initializeData();
  }

  public static getInstance(): IngredientPricingService {
    if (!IngredientPricingService.instance) {
      IngredientPricingService.instance = new IngredientPricingService();
    }
    return IngredientPricingService.instance;
  }

  /**
   * Khởi tạo dữ liệu mẫu (trong thực tế sẽ load từ database)
   */
  private initializeData(): void {
    // Sample ingredients data
    const ingredientsData: Ingredient[] = [
      {
        id: 'thit_bo',
        name: 'thit_bo',
        displayName: 'Thịt bò',
        categoryId: 'meat_seafood',
        baseUnitId: 'lang',
        basePricePerUnit: 30000,
        alternativeNames: ['thịt bò', 'beef', 'thịt bò tươi']
      },
      {
        id: 'thit_heo',
        name: 'thit_heo',
        displayName: 'Thịt heo',
        categoryId: 'meat_seafood',
        baseUnitId: 'lang',
        basePricePerUnit: 16000,
        alternativeNames: ['thịt heo', 'pork', 'thịt lợn']
      },
      {
        id: 'ca_chua',
        name: 'ca_chua',
        displayName: 'Cà chua',
        categoryId: 'vegetables',
        baseUnitId: 'lang',
        basePricePerUnit: 2500,
        alternativeNames: ['cà chua', 'tomato']
      },
      {
        id: 'hanh_tay',
        name: 'hanh_tay',
        displayName: 'Hành tây',
        categoryId: 'vegetables',
        baseUnitId: 'bulb',
        basePricePerUnit: 5000,
        alternativeNames: ['hành tây', 'onion']
      },
      {
        id: 'toi',
        name: 'toi',
        displayName: 'Tỏi',
        categoryId: 'vegetables',
        baseUnitId: 'clove',
        basePricePerUnit: 2000,
        alternativeNames: ['tỏi', 'garlic']
      },
      {
        id: 'gao_te',
        name: 'gao_te',
        displayName: 'Gạo tẻ',
        categoryId: 'grains_cereals',
        baseUnitId: 'lang',
        basePricePerUnit: 2500,
        alternativeNames: ['gạo tẻ', 'jasmine rice', 'gạo']
      },
      {
        id: 'trung_ga',
        name: 'trung_ga',
        displayName: 'Trứng gà',
        categoryId: 'dairy_eggs',
        baseUnitId: 'fruit',
        basePricePerUnit: 4000,
        alternativeNames: ['trứng gà', 'chicken egg', 'trứng']
      },
      {
        id: 'nuoc_mam',
        name: 'nuoc_mam',
        displayName: 'Nước mắm',
        categoryId: 'seasonings',
        baseUnitId: 'liter',
        basePricePerUnit: 40000,
        alternativeNames: ['nước mắm', 'fish sauce']
      },
      {
        id: 'muoi',
        name: 'muoi',
        displayName: 'Muối',
        categoryId: 'seasonings',
        baseUnitId: 'lang',
        basePricePerUnit: 1000,
        alternativeNames: ['muối', 'salt']
      },
      {
        id: 'dau_an',
        name: 'dau_an',
        displayName: 'Dầu ăn',
        categoryId: 'oils_fats',
        baseUnitId: 'liter',
        basePricePerUnit: 50000,
        alternativeNames: ['dầu ăn', 'cooking oil', 'dầu']
      }
    ];

    // Initialize ingredients maps
    ingredientsData.forEach(ingredient => {
      this.ingredients.set(ingredient.id, ingredient);
      this.ingredientsByName.set(ingredient.name.toLowerCase(), ingredient);
      
      // Add alternative names
      ingredient.alternativeNames.forEach(altName => {
        this.ingredientsByName.set(altName.toLowerCase(), ingredient);
      });
    });

    // Sample prices data
    const pricesData: IngredientPrice[] = [
      // Thịt bò
      { ingredientId: 'thit_bo', unitId: 'lang', price: 30000, region: 'vietnam', source: 'market', effectiveDate: '2024-01-01' },
      { ingredientId: 'thit_bo', unitId: 'kilogram', price: 300000, region: 'vietnam', source: 'market', effectiveDate: '2024-01-01' },
      { ingredientId: 'thit_bo', unitId: 'gram', price: 300, region: 'vietnam', source: 'market', effectiveDate: '2024-01-01' },
      
      // Hành tây
      { ingredientId: 'hanh_tay', unitId: 'bulb', price: 5000, region: 'vietnam', source: 'market', effectiveDate: '2024-01-01' },
      { ingredientId: 'hanh_tay', unitId: 'lang', price: 3000, region: 'vietnam', source: 'market', effectiveDate: '2024-01-01' },
      
      // Tỏi
      { ingredientId: 'toi', unitId: 'clove', price: 2000, region: 'vietnam', source: 'market', effectiveDate: '2024-01-01' },
      { ingredientId: 'toi', unitId: 'bulb', price: 15000, region: 'vietnam', source: 'market', effectiveDate: '2024-01-01' },
      
      // Trứng gà
      { ingredientId: 'trung_ga', unitId: 'fruit', price: 4000, region: 'vietnam', source: 'market', effectiveDate: '2024-01-01' },
      { ingredientId: 'trung_ga', unitId: 'dozen', price: 48000, region: 'vietnam', source: 'market', effectiveDate: '2024-01-01' },
      
      // Nước mắm
      { ingredientId: 'nuoc_mam', unitId: 'tablespoon', price: 600, region: 'vietnam', source: 'market', effectiveDate: '2024-01-01' },
      { ingredientId: 'nuoc_mam', unitId: 'milliliter', price: 40, region: 'vietnam', source: 'market', effectiveDate: '2024-01-01' },
      { ingredientId: 'nuoc_mam', unitId: 'liter', price: 40000, region: 'vietnam', source: 'market', effectiveDate: '2024-01-01' },
    ];

    // Initialize prices map
    pricesData.forEach(price => {
      const key = price.ingredientId;
      if (!this.prices.has(key)) {
        this.prices.set(key, []);
      }
      this.prices.get(key)!.push(price);
    });
  }

  /**
   * Tìm nguyên liệu theo tên (hỗ trợ fuzzy matching)
   */
  public findIngredient(name: string): Ingredient | null {
    const normalizedName = name.toLowerCase().trim();
    
    // Exact match first
    let ingredient = this.ingredientsByName.get(normalizedName);
    if (ingredient) return ingredient;

    // Fuzzy matching - tìm nguyên liệu có chứa từ khóa
    for (const [key, ing] of this.ingredientsByName.entries()) {
      if (key.includes(normalizedName) || normalizedName.includes(key)) {
        return ing;
      }
    }

    return null;
  }

  /**
   * Lấy giá nguyên liệu theo đơn vị cụ thể
   */
  public getPrice(ingredientId: string, unitId: string, region: string = 'vietnam'): IngredientPrice | null {
    const prices = this.prices.get(ingredientId);
    if (!prices) return null;

    // Tìm giá theo đơn vị và khu vực
    return prices.find(p => p.unitId === unitId && p.region === region) || null;
  }

  /**
   * Tính giá nguyên liệu với số lượng và đơn vị
   */
  public calculatePrice(
    ingredientName: string,
    quantity: number,
    unit: string,
    region: string = 'vietnam'
  ): PriceCalculationResult {
    // Tìm nguyên liệu
    const ingredient = this.findIngredient(ingredientName);
    if (!ingredient) {
      return {
        success: false,
        totalPrice: 0,
        pricePerUnit: 0,
        ingredient: null,
        unitUsed: unit,
        region,
        error: `Không tìm thấy nguyên liệu: ${ingredientName}`
      };
    }

    // Normalize unit name
    const unitObj = this.unitService.getUnitByName(unit);
    const unitId = unitObj?.id || unit;

    // Tìm giá trực tiếp theo đơn vị yêu cầu
    let price = this.getPrice(ingredient.id, unitId, region);
    
    if (price) {
      return {
        success: true,
        totalPrice: price.price * quantity,
        pricePerUnit: price.price,
        ingredient,
        unitUsed: unitId,
        region
      };
    }

    // Nếu không có giá trực tiếp, thử chuyển đổi về đơn vị cơ sở
    const conversionResult = this.unitService.convertToBaseUnit(quantity, unitId);
    if (!conversionResult.success) {
      return {
        success: false,
        totalPrice: 0,
        pricePerUnit: 0,
        ingredient,
        unitUsed: unitId,
        region,
        error: conversionResult.error
      };
    }

    // Lấy giá theo đơn vị cơ sở
    const basePrice = this.getPrice(ingredient.id, ingredient.baseUnitId, region);
    if (!basePrice) {
      // Fallback to ingredient's base price
      return {
        success: true,
        totalPrice: ingredient.basePricePerUnit * conversionResult.convertedQuantity,
        pricePerUnit: ingredient.basePricePerUnit / conversionResult.convertedQuantity * quantity,
        ingredient,
        unitUsed: unitId,
        region,
        breakdown: {
          originalQuantity: quantity,
          originalUnit: unitId,
          convertedQuantity: conversionResult.convertedQuantity,
          convertedUnit: ingredient.baseUnitId,
          pricePerConvertedUnit: ingredient.basePricePerUnit
        }
      };
    }

    return {
      success: true,
      totalPrice: basePrice.price * conversionResult.convertedQuantity,
      pricePerUnit: basePrice.price / conversionResult.convertedQuantity * quantity,
      ingredient,
      unitUsed: unitId,
      region,
      breakdown: {
        originalQuantity: quantity,
        originalUnit: unitId,
        convertedQuantity: conversionResult.convertedQuantity,
        convertedUnit: ingredient.baseUnitId,
        pricePerConvertedUnit: basePrice.price
      }
    };
  }

  /**
   * Parse và tính giá từ chuỗi nguyên liệu
   */
  public calculatePriceFromString(
    ingredientStr: string,
    region: string = 'vietnam'
  ): PriceCalculationResult {
    const parsed = this.unitService.parseIngredientString(ingredientStr);
    return this.calculatePrice(parsed.name, parsed.quantity, parsed.unit, region);
  }

  /**
   * Lấy danh sách nguyên liệu theo danh mục
   */
  public getIngredientsByCategory(categoryId: string): Ingredient[] {
    return Array.from(this.ingredients.values()).filter(ing => ing.categoryId === categoryId);
  }

  /**
   * Lấy tất cả nguyên liệu
   */
  public getAllIngredients(): Ingredient[] {
    return Array.from(this.ingredients.values());
  }

  /**
   * Cập nhật giá nguyên liệu
   */
  public updatePrice(ingredientId: string, unitId: string, price: number, region: string = 'vietnam'): boolean {
    const prices = this.prices.get(ingredientId);
    if (!prices) {
      this.prices.set(ingredientId, []);
    }

    const existingPriceIndex = prices!.findIndex(p => p.unitId === unitId && p.region === region);
    const newPrice: IngredientPrice = {
      ingredientId,
      unitId,
      price,
      region,
      source: 'manual',
      effectiveDate: new Date().toISOString().split('T')[0]
    };

    if (existingPriceIndex >= 0) {
      prices![existingPriceIndex] = newPrice;
    } else {
      prices!.push(newPrice);
    }

    return true;
  }
}

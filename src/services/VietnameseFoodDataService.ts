/**
 * Service để load và quản lý dữ liệu món ăn Việt Nam
 */

import { VietnameseDish, VietnameseFoodCategory } from '../data/vietnameseFoodCategories';

export interface LoadedVietnameseFoodData {
  categories: VietnameseFoodCategory[];
  dishes: VietnameseDish[];
  stats: {
    totalDishes: number;
    breakfastDishes: number;
    lunchDishes: number;
    dinnerDishes: number;
    snackDishes: number;
    byCategory: { [key: string]: number };
    byRegion: { [key: string]: number };
    byDifficulty: { [key: string]: number };
    byMealType: { [key: string]: number };
    avgNutrition: {
      calories: number;
      protein: number;
      carbs: number;
      fat: number;
      fiber: number;
    };
    priceRange: {
      min: number;
      max: number;
      avg: number;
    };
  };
  loadedAt: string;
}

class VietnameseFoodDataService {
  private data: LoadedVietnameseFoodData | null = null;
  private loading = false;

  /**
   * Load dữ liệu từ file JSON
   */
  async loadData(): Promise<LoadedVietnameseFoodData> {
    if (this.data) {
      return this.data;
    }

    if (this.loading) {
      // Wait for existing load to complete
      while (this.loading) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
      return this.data!;
    }

    this.loading = true;

    try {
      const response = await fetch('/vietnamese-food-data.json');
      if (!response.ok) {
        throw new Error(`Failed to load data: ${response.statusText}`);
      }

      this.data = await response.json();
      console.log('✅ Vietnamese food data loaded successfully:', this.data?.stats);
      return this.data!;
    } catch (error) {
      console.error('❌ Error loading Vietnamese food data:', error);
      throw error;
    } finally {
      this.loading = false;
    }
  }

  /**
   * Lấy tất cả món ăn
   */
  async getAllDishes(): Promise<VietnameseDish[]> {
    const data = await this.loadData();
    return data.dishes;
  }

  /**
   * Lấy món ăn theo category
   */
  async getDishesByCategory(categoryId: string): Promise<VietnameseDish[]> {
    const data = await this.loadData();
    return data.dishes.filter(dish => dish.categoryId === categoryId);
  }

  /**
   * Lấy món ăn theo vùng miền
   */
  async getDishesByRegion(region: string): Promise<VietnameseDish[]> {
    const data = await this.loadData();
    return data.dishes.filter(dish => dish.region === region || dish.region === 'nationwide');
  }

  /**
   * Lấy món ăn theo loại bữa ăn
   */
  async getDishesByMealType(mealType: string): Promise<VietnameseDish[]> {
    const data = await this.loadData();
    return data.dishes.filter(dish => dish.mealType === mealType);
  }

  /**
   * Lấy món ăn sáng/ngoài hàng
   */
  async getBreakfastStreetFoods(): Promise<VietnameseDish[]> {
    return this.getDishesByMealType('breakfast');
  }

  /**
   * Lấy món ăn trưa
   */
  async getLunchMeals(): Promise<VietnameseDish[]> {
    return this.getDishesByMealType('lunch');
  }

  /**
   * Lấy món ăn tối
   */
  async getDinnerMeals(): Promise<VietnameseDish[]> {
    return this.getDishesByMealType('dinner');
  }

  /**
   * Lấy món ăn cơm hàng ngày (trưa + tối)
   */
  async getHomeCookingMeals(): Promise<VietnameseDish[]> {
    const data = await this.loadData();
    return data.dishes.filter(dish => dish.mealType === 'lunch' || dish.mealType === 'dinner');
  }

  /**
   * Lấy món ăn vặt
   */
  async getSnackFoods(): Promise<VietnameseDish[]> {
    return this.getDishesByMealType('snack');
  }

  /**
   * Tìm kiếm món ăn
   */
  async searchDishes(query: string): Promise<VietnameseDish[]> {
    const data = await this.loadData();
    const lowerQuery = query.toLowerCase();

    return data.dishes.filter(dish =>
      dish.name.toLowerCase().includes(lowerQuery) ||
      dish.description.toLowerCase().includes(lowerQuery) ||
      dish.ingredients.some(ingredient => ingredient.toLowerCase().includes(lowerQuery)) ||
      dish.tags.some(tag => tag.toLowerCase().includes(lowerQuery))
    );
  }

  /**
   * Lấy món ăn theo khoảng giá
   */
  async getDishesByPriceRange(minPrice: number, maxPrice: number): Promise<VietnameseDish[]> {
    const data = await this.loadData();
    return data.dishes.filter(dish => dish.cost >= minPrice && dish.cost <= maxPrice);
  }

  /**
   * Lấy món ăn phổ biến
   */
  async getPopularDishes(): Promise<VietnameseDish[]> {
    const data = await this.loadData();
    return data.dishes.filter(dish => dish.isPopular);
  }

  /**
   * Lấy thống kê
   */
  async getStats(): Promise<LoadedVietnameseFoodData['stats']> {
    const data = await this.loadData();
    return data.stats;
  }

  /**
   * Lấy tất cả categories
   */
  async getCategories(): Promise<VietnameseFoodCategory[]> {
    const data = await this.loadData();
    return data.categories;
  }

  /**
   * Lấy món ăn ngẫu nhiên
   */
  async getRandomDishes(count: number = 5): Promise<VietnameseDish[]> {
    const data = await this.loadData();
    const shuffled = [...data.dishes].sort(() => 0.5 - Math.random());
    return shuffled.slice(0, count);
  }

  /**
   * Lấy gợi ý món ăn theo preferences
   */
  async getRecommendedDishes(preferences: {
    region?: string;
    difficulty?: string;
    mealType?: string;
    maxPrice?: number;
    excludeCategories?: string[];
  }): Promise<VietnameseDish[]> {
    const data = await this.loadData();

    return data.dishes.filter(dish => {
      if (preferences.region && dish.region !== preferences.region && dish.region !== 'nationwide') {
        return false;
      }
      if (preferences.difficulty && dish.difficulty !== preferences.difficulty) {
        return false;
      }
      if (preferences.mealType && dish.mealType !== preferences.mealType) {
        return false;
      }
      if (preferences.maxPrice && dish.cost > preferences.maxPrice) {
        return false;
      }
      if (preferences.excludeCategories && preferences.excludeCategories.includes(dish.categoryId)) {
        return false;
      }
      return true;
    });
  }

  /**
   * Clear cache để reload dữ liệu
   */
  clearCache(): void {
    this.data = null;
  }

  /**
   * Kiểm tra xem dữ liệu đã được load chưa
   */
  isDataLoaded(): boolean {
    return this.data !== null;
  }

  /**
   * Lấy thời gian load dữ liệu
   */
  getLoadedAt(): string | null {
    return this.data?.loadedAt || null;
  }
}

// Export singleton instance
export const vietnameseFoodDataService = new VietnameseFoodDataService();
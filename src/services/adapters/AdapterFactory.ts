import { DatabaseAdapter } from '../interfaces/DatabaseAdapter';
import { SupabaseAdapter } from './SupabaseAdapter';
import { LocalStorageAdapter } from './LocalStorageAdapter';
import { supabaseHelpers } from '../../config/supabase';

export type AdapterType = 'localStorage' | 'supabase' | 'firebase' | 'pocketbase';

interface AdapterCreationOptions {
  retryAttempts?: number;
  retryDelay?: number;
  autoSetupDatabase?: boolean;
  fallbackToLocalStorage?: boolean;
}

export class AdapterFactory {
  private static supabaseAdapter: SupabaseAdapter | null = null;
  private static isSupabaseSetupInProgress = false;

  static async createSupabaseAdapterWithRetry(options: AdapterCreationOptions = {}): Promise<DatabaseAdapter> {
    const {
      retryAttempts = 3,
      retryDelay = 2000,
      autoSetupDatabase = true,
      fallbackToLocalStorage = false
    } = options;

    console.log('🔄 Creating Supabase adapter with enhanced retry mechanism...');

    // Return existing adapter if available and working
    if (this.supabaseAdapter) {
      try {
        const testResult = await supabaseHelpers.testConnection();
        if (testResult.success) {
          console.log('✅ Reusing existing Supabase adapter');
          return this.supabaseAdapter;
        } else {
          console.log('🔄 Existing adapter failed test, creating new one...');
          this.supabaseAdapter = null;
        }
      } catch (error) {
        console.log('🔄 Error testing existing adapter, creating new one...');
        this.supabaseAdapter = null;
      }
    }

    for (let attempt = 1; attempt <= retryAttempts; attempt++) {
      try {
        console.log(`🔄 Supabase connection attempt ${attempt}/${retryAttempts}`);

        const adapter = new SupabaseAdapter();
        const isConnected = await adapter.initialize();

        if (isConnected) {
          console.log('✅ Supabase adapter created successfully');
          this.supabaseAdapter = adapter;
          return adapter;
        } else {
          console.log(`❌ Supabase connection failed on attempt ${attempt}`);

          // Try to setup database if auto-setup is enabled
          if (autoSetupDatabase && !this.isSupabaseSetupInProgress) {
            console.log('🔧 Attempting to setup database...');
            this.isSupabaseSetupInProgress = true;

            try {
              const setupResult = await supabaseHelpers.setupDatabase();
              if (setupResult.success) {
                console.log('✅ Database setup successful, retrying connection...');
                const retestResult = await adapter.initialize();
                if (retestResult) {
                  console.log('✅ Supabase adapter working after database setup');
                  this.supabaseAdapter = adapter;
                  this.isSupabaseSetupInProgress = false;
                  return adapter;
                }
              } else {
                console.log('❌ Database setup failed:', setupResult.message);
              }
            } catch (setupError) {
              console.error('❌ Database setup error:', setupError);
            } finally {
              this.isSupabaseSetupInProgress = false;
            }
          }
        }

        // Wait before next attempt (except for last attempt)
        if (attempt < retryAttempts) {
          console.log(`⏳ Waiting ${retryDelay}ms before next attempt...`);
          await new Promise(resolve => setTimeout(resolve, retryDelay));
        }

      } catch (error) {
        console.error(`❌ Supabase adapter creation failed on attempt ${attempt}:`, error);

        if (attempt < retryAttempts) {
          await new Promise(resolve => setTimeout(resolve, retryDelay));
        }
      }
    }

    console.error('❌ All Supabase connection attempts failed');

    if (fallbackToLocalStorage) {
      console.warn('🔄 Falling back to localStorage adapter');
      return new LocalStorageAdapter();
    } else {
      throw new Error('Failed to create Supabase adapter after all retry attempts');
    }
  }

  static createAdapter(type: AdapterType, options: AdapterCreationOptions = {}): DatabaseAdapter {
    switch (type) {
      case 'localStorage':
        return new LocalStorageAdapter();

      case 'supabase':
        // For synchronous creation, return adapter immediately but setup asynchronously
        const adapter = new SupabaseAdapter();

        // Setup asynchronously with retry mechanism
        this.createSupabaseAdapterWithRetry({
          ...options,
          fallbackToLocalStorage: false
        }).then(enhancedAdapter => {
          if (enhancedAdapter instanceof SupabaseAdapter) {
            this.supabaseAdapter = enhancedAdapter;
            console.log('✅ Enhanced Supabase adapter ready');
          }
        }).catch(error => {
          console.error('❌ Enhanced Supabase setup failed:', error);
        });

        return adapter;

      case 'firebase':
        console.warn('Firebase adapter not implemented yet, falling back to localStorage');
        return new LocalStorageAdapter();

      case 'pocketbase':
        console.warn('PocketBase adapter not implemented yet, falling back to localStorage');
        return new LocalStorageAdapter();

      default:
        console.warn(`Unknown adapter type: ${type}, falling back to localStorage`);
        return new LocalStorageAdapter();
    }
  }

  static async createAdapterAsync(type: AdapterType, options: AdapterCreationOptions = {}): Promise<DatabaseAdapter> {
    switch (type) {
      case 'localStorage':
        return new LocalStorageAdapter();

      case 'supabase':
        return await this.createSupabaseAdapterWithRetry({
          retryAttempts: 3,
          retryDelay: 2000,
          autoSetupDatabase: true,
          fallbackToLocalStorage: true,
          ...options
        });

      case 'firebase':
        console.warn('Firebase adapter not implemented yet, falling back to localStorage');
        return new LocalStorageAdapter();

      case 'pocketbase':
        console.warn('PocketBase adapter not implemented yet, falling back to localStorage');
        return new LocalStorageAdapter();

      default:
        console.warn(`Unknown adapter type: ${type}, falling back to localStorage`);
        return new LocalStorageAdapter();
    }
  }

  static getAdapterFromEnv(): DatabaseAdapter {
    const adapterType = ((typeof process !== 'undefined' && process.env)
      ? process.env.VITE_DATABASE_ADAPTER || 'supabase'  // Default to supabase instead of localStorage
      : (typeof import.meta !== 'undefined' && import.meta.env)
        ? import.meta.env.VITE_DATABASE_ADAPTER || 'supabase'
        : 'supabase') as AdapterType;

    return this.createAdapter(adapterType, {
      retryAttempts: 3,
      autoSetupDatabase: true,
      fallbackToLocalStorage: true
    });
  }

  static async getAdapterFromEnvAsync(): Promise<DatabaseAdapter> {
    const adapterType = ((typeof process !== 'undefined' && process.env)
      ? process.env.VITE_DATABASE_ADAPTER || 'supabase'
      : (typeof import.meta !== 'undefined' && import.meta.env)
        ? import.meta.env.VITE_DATABASE_ADAPTER || 'supabase'
        : 'supabase') as AdapterType;

    return await this.createAdapterAsync(adapterType, {
      retryAttempts: 3,
      autoSetupDatabase: true,
      fallbackToLocalStorage: true
    });
  }

  // Method to force refresh Supabase adapter
  static resetSupabaseAdapter(): void {
    this.supabaseAdapter = null;
    this.isSupabaseSetupInProgress = false;
    console.log('🔄 Supabase adapter reset');
  }

  // Method to check if Supabase is working
  static async isSupabaseWorking(): Promise<boolean> {
    try {
      const testResult = await supabaseHelpers.testConnection();
      return testResult.success;
    } catch (error) {
      return false;
    }
  }
}

// Design System TypeScript Interfaces

export interface ColorPalette {
  50: string;
  100: string;
  200: string;
  300: string;
  400: string;
  500: string;
  600: string;
  700: string;
  800: string;
  900: string;
}

export interface TypographyScale {
  fontFamily: {
    sans: string[];
    mono: string[];
  };
  fontSize: {
    xs: [string, { lineHeight: string }];
    sm: [string, { lineHeight: string }];
    base: [string, { lineHeight: string }];
    lg: [string, { lineHeight: string }];
    xl: [string, { lineHeight: string }];
    '2xl': [string, { lineHeight: string }];
    '3xl': [string, { lineHeight: string }];
    '4xl': [string, { lineHeight: string }];
    '5xl': [string, { lineHeight: string }];
    '6xl': [string, { lineHeight: string }];
  };
  fontWeight: {
    thin: string;
    extralight: string;
    light: string;
    normal: string;
    medium: string;
    semibold: string;
    bold: string;
    extrabold: string;
    black: string;
  };
  lineHeight: {
    none: string;
    tight: string;
    snug: string;
    normal: string;
    relaxed: string;
    loose: string;
  };
  letterSpacing: {
    tighter: string;
    tight: string;
    normal: string;
    wide: string;
    wider: string;
    widest: string;
  };
}

export interface SpacingScale {
  0: string;
  px: string;
  0.5: string;
  1: string;
  1.5: string;
  2: string;
  2.5: string;
  3: string;
  3.5: string;
  4: string;
  5: string;
  6: string;
  7: string;
  8: string;
  9: string;
  10: string;
  11: string;
  12: string;
  14: string;
  16: string;
  20: string;
  24: string;
  28: string;
  32: string;
  36: string;
  40: string;
  44: string;
  48: string;
  52: string;
  56: string;
  60: string;
  64: string;
  72: string;
  80: string;
  96: string;
}

export interface AnimationTokens {
  duration: {
    instant: string;
    fast: string;
    normal: string;
    slow: string;
    slower: string;
    slowest: string;
  };
  easing: {
    linear: string;
    ease: string;
    easeIn: string;
    easeOut: string;
    easeInOut: string;
    bounce: string;
    elastic: string;
  };
  microInteractions: {
    buttonHover: MicroInteraction;
    cardHover: MicroInteraction;
    modalEnter: MicroInteraction;
    slideIn: MicroInteraction;
  };
}

export interface MicroInteraction {
  duration: string;
  easing: string;
  properties: string[];
}

export interface ComponentVariant {
  background?: string;
  color?: string;
  border?: string;
  borderColor?: string;
  borderRadius?: string;
  padding?: string;
  fontSize?: string;
  boxShadow?: string;
  cursor?: string;
  transform?: string;
  outline?: string;
  hover?: Partial<ComponentVariant>;
  active?: Partial<ComponentVariant>;
  focus?: Partial<ComponentVariant>;
  disabled?: Partial<ComponentVariant>;
  error?: Partial<ComponentVariant>;
}

export interface ComponentSize {
  padding: string;
  fontSize: string;
  borderRadius: string;
}

export interface ButtonVariants {
  primary: ComponentVariant;
  secondary: ComponentVariant;
  ghost: ComponentVariant;
  danger: ComponentVariant;
}

export interface ButtonSizes {
  xs: ComponentSize;
  sm: ComponentSize;
  md: ComponentSize;
  lg: ComponentSize;
  xl: ComponentSize;
}

export interface CardVariants {
  default: ComponentVariant;
  elevated: ComponentVariant;
  outlined: ComponentVariant;
  ghost: ComponentVariant;
}

export interface InputVariants {
  default: ComponentVariant;
  filled: ComponentVariant;
}

export interface ModalVariants {
  default: ComponentVariant;
  large: ComponentVariant;
  fullscreen: ComponentVariant;
}

export interface ModalOverlay {
  background: string;
  backdropFilter: string;
}

export interface ComponentThemes {
  button: {
    variants: ButtonVariants;
    sizes: ButtonSizes;
  };
  card: {
    variants: CardVariants;
  };
  input: {
    variants: InputVariants;
  };
  modal: {
    variants: ModalVariants;
    overlay: ModalOverlay;
  };
}

export interface ShadowScale {
  none: string;
  xs: string;
  sm: string;
  md: string;
  lg: string;
  xl: string;
  '2xl': string;
  inner: string;
  elevation: {
    0: string;
    1: string;
    2: string;
    3: string;
    4: string;
    5: string;
  };
  focus: {
    primary: string;
    secondary: string;
    success: string;
    warning: string;
    danger: string;
  };
}

export interface BorderRadiusScale {
  none: string;
  xs: string;
  sm: string;
  md: string;
  lg: string;
  xl: string;
  '2xl': string;
  '3xl': string;
  full: string;
  button: string;
  card: string;
  input: string;
  modal: string;
  avatar: string;
  badge: string;
}

export interface ZIndexScale {
  hide: number;
  auto: string;
  base: number;
  docked: number;
  dropdown: number;
  sticky: number;
  banner: number;
  overlay: number;
  modal: number;
  popover: number;
  skipLink: number;
  toast: number;
  tooltip: number;
}

export interface BreakpointScale {
  sm: string;
  md: string;
  lg: string;
  xl: string;
  '2xl': string;
}

export interface DesignTokens {
  colors: {
    primary: ColorPalette;
    secondary: ColorPalette;
    success: ColorPalette;
    warning: ColorPalette;
    danger: ColorPalette;
    gray: ColorPalette;
    white: string;
    black: string;
    transparent: string;
  };
  typography: TypographyScale;
  spacing: SpacingScale;
  animation: AnimationTokens;
  components: ComponentThemes;
  shadows: ShadowScale;
  borderRadius: BorderRadiusScale;
  zIndex: ZIndexScale;
  screens: BreakpointScale;
}

// Component Prop Types
export type ButtonVariant = keyof ButtonVariants;
export type ButtonSize = keyof ButtonSizes;
export type CardVariant = keyof CardVariants;
export type InputVariant = keyof InputVariants;
export type ModalVariant = keyof ModalVariants;

// Theme Types
export type ThemeMode = 'light' | 'dark' | 'auto';

export interface ThemeConfig {
  mode: ThemeMode;
  tokens: DesignTokens;
  components: ComponentThemes;
}

// Utility Types
export type ResponsiveValue<T> = T | {
  sm?: T;
  md?: T;
  lg?: T;
  xl?: T;
  '2xl'?: T;
};

export type ColorToken = keyof DesignTokens['colors'] | `${keyof DesignTokens['colors']}.${keyof ColorPalette}`;
export type SpacingToken = keyof SpacingScale;
export type ShadowToken = keyof ShadowScale | `elevation.${keyof ShadowScale['elevation']}` | `focus.${keyof ShadowScale['focus']}`;
export type BorderRadiusToken = keyof BorderRadiusScale;

// Animation Types
export interface AnimationConfig {
  duration?: keyof AnimationTokens['duration'];
  easing?: keyof AnimationTokens['easing'];
  delay?: string;
  fillMode?: 'none' | 'forwards' | 'backwards' | 'both';
  iterationCount?: number | 'infinite';
  direction?: 'normal' | 'reverse' | 'alternate' | 'alternate-reverse';
}

// Accessibility Types
export interface AccessibilitySettings {
  reducedMotion: boolean;
  highContrast: boolean;
  focusVisible: boolean;
  screenReaderOnly: boolean;
}

// Component Base Props
export interface BaseComponentProps {
  className?: string;
  children?: React.ReactNode;
  'data-testid'?: string;
}

// Polymorphic Component Types
export type PolymorphicRef<C extends React.ElementType> = React.ComponentPropsWithRef<C>['ref'];

export type PolymorphicComponentProp<C extends React.ElementType, Props = {}> = React.PropsWithChildren<Props & {
  as?: C;
}> & Omit<React.ComponentPropsWithoutRef<C>, keyof Props>;

export type PolymorphicComponentPropWithRef<C extends React.ElementType, Props = {}> = PolymorphicComponentProp<C, Props> & {
  ref?: PolymorphicRef<C>;
};

import React, { useState } from 'react';
import type { <PERSON>a, StoryObj } from '@storybook/react';
import { fn } from '@storybook/test';
import { Search, Mail, Lock, User, Phone, Calendar } from 'lucide-react';
import { 
  Input, 
  PasswordInput, 
  SearchInput, 
  Textarea, 
  FormField 
} from '@/components/design-system/atoms/Input';

const meta = {
  title: 'Design System/Atoms/Input',
  component: Input,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: 'Enhanced Input components with multiple variants, states, and accessibility features.',
      },
    },
  },
  tags: ['autodocs'],
  argTypes: {
    variant: {
      control: 'select',
      options: ['default', 'filled', 'ghost'],
      description: 'Visual style variant of the input',
    },
    size: {
      control: 'select',
      options: ['sm', 'md', 'lg'],
      description: 'Size of the input',
    },
    state: {
      control: 'select',
      options: ['default', 'error', 'success', 'warning'],
      description: 'State of the input',
    },
    disabled: {
      control: 'boolean',
      description: 'Disabled state',
    },
    clearable: {
      control: 'boolean',
      description: 'Show clear button when input has value',
    },
  },
  args: {
    onChange: fn(),
    onClear: fn(),
  },
} satisfies Meta<typeof Input>;

export default meta;
type Story = StoryObj<typeof meta>;

// Basic Input Stories
export const Default: Story = {
  args: {
    placeholder: 'Enter text...',
  },
};

export const Filled: Story = {
  args: {
    variant: 'filled',
    placeholder: 'Filled input...',
  },
};

export const Ghost: Story = {
  args: {
    variant: 'ghost',
    placeholder: 'Ghost input...',
  },
};

// Size Variants
export const Sizes: Story = {
  render: () => (
    <div className="space-y-4 w-80">
      <Input size="sm" placeholder="Small input" />
      <Input size="md" placeholder="Medium input" />
      <Input size="lg" placeholder="Large input" />
    </div>
  ),
};

// States
export const States: Story = {
  render: () => (
    <div className="space-y-4 w-80">
      <Input state="default" placeholder="Default state" />
      <Input state="error" placeholder="Error state" />
      <Input state="success" placeholder="Success state" />
      <Input state="warning" placeholder="Warning state" />
    </div>
  ),
};

// With Icons
export const WithIcons: Story = {
  render: () => (
    <div className="space-y-4 w-80">
      <Input
        leftIcon={<Mail className="w-4 h-4" />}
        placeholder="Email address"
      />
      <Input
        rightIcon={<Search className="w-4 h-4" />}
        placeholder="Search..."
      />
      <Input
        leftIcon={<User className="w-4 h-4" />}
        rightIcon={<Phone className="w-4 h-4" />}
        placeholder="Contact info"
      />
    </div>
  ),
};

// Clearable Input
export const Clearable: Story = {
  render: () => {
    const [value, setValue] = useState('Clear me!');
    
    return (
      <div className="w-80">
        <Input
          value={value}
          onChange={(e) => setValue(e.target.value)}
          onClear={() => setValue('')}
          clearable
          placeholder="Type something..."
        />
      </div>
    );
  },
};

// Password Input
export const PasswordInputStory: Story = {
  render: () => (
    <div className="space-y-4 w-80">
      <PasswordInput placeholder="Enter password" />
      <PasswordInput 
        placeholder="Password without toggle" 
        showPasswordToggle={false} 
      />
    </div>
  ),
};

// Search Input
export const SearchInputStory: Story = {
  render: () => {
    const [searchValue, setSearchValue] = useState('');
    
    return (
      <div className="w-80">
        <SearchInput
          value={searchValue}
          onChange={(e) => setSearchValue(e.target.value)}
          onSearch={(value) => alert(`Searching for: ${value}`)}
          placeholder="Search recipes..."
        />
      </div>
    );
  },
};

// Textarea
export const TextareaStory: Story = {
  render: () => (
    <div className="space-y-4 w-80">
      <Textarea placeholder="Default textarea" />
      <Textarea 
        variant="filled" 
        placeholder="Filled textarea" 
        resize="none" 
      />
      <Textarea 
        state="error" 
        placeholder="Error state textarea" 
        size="lg" 
      />
    </div>
  ),
};

// Form Fields
export const FormFields: Story = {
  render: () => {
    const [formData, setFormData] = useState({
      email: '',
      password: '',
      confirmPassword: '',
      bio: '',
    });
    
    const [errors, setErrors] = useState<Record<string, string>>({});
    
    const validateEmail = (email: string) => {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      return emailRegex.test(email);
    };
    
    const handleEmailChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const value = e.target.value;
      setFormData(prev => ({ ...prev, email: value }));
      
      if (value && !validateEmail(value)) {
        setErrors(prev => ({ ...prev, email: 'Please enter a valid email address' }));
      } else {
        setErrors(prev => ({ ...prev, email: '' }));
      }
    };
    
    const handlePasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const value = e.target.value;
      setFormData(prev => ({ ...prev, password: value }));
      
      if (value && value.length < 8) {
        setErrors(prev => ({ ...prev, password: 'Password must be at least 8 characters' }));
      } else {
        setErrors(prev => ({ ...prev, password: '' }));
      }
    };
    
    return (
      <div className="space-y-6 w-96">
        <FormField
          label="Email Address"
          description="We'll never share your email with anyone else."
          error={errors.email}
          required
          htmlFor="email"
        >
          <Input
            id="email"
            type="email"
            value={formData.email}
            onChange={handleEmailChange}
            leftIcon={<Mail className="w-4 h-4" />}
            placeholder="<EMAIL>"
            clearable
            onClear={() => setFormData(prev => ({ ...prev, email: '' }))}
          />
        </FormField>
        
        <FormField
          label="Password"
          error={errors.password}
          required
          htmlFor="password"
        >
          <PasswordInput
            id="password"
            value={formData.password}
            onChange={handlePasswordChange}
            placeholder="Enter your password"
          />
        </FormField>
        
        <FormField
          label="Confirm Password"
          success={formData.confirmPassword && formData.password === formData.confirmPassword ? 'Passwords match!' : undefined}
          error={formData.confirmPassword && formData.password !== formData.confirmPassword ? 'Passwords do not match' : undefined}
          htmlFor="confirmPassword"
        >
          <PasswordInput
            id="confirmPassword"
            value={formData.confirmPassword}
            onChange={(e) => setFormData(prev => ({ ...prev, confirmPassword: e.target.value }))}
            placeholder="Confirm your password"
          />
        </FormField>
        
        <FormField
          label="Bio"
          description="Tell us a little about yourself."
          htmlFor="bio"
        >
          <Textarea
            id="bio"
            value={formData.bio}
            onChange={(e) => setFormData(prev => ({ ...prev, bio: e.target.value }))}
            placeholder="I'm a passionate cook who loves..."
            resize="vertical"
          />
        </FormField>
      </div>
    );
  },
};

// Disabled State
export const Disabled: Story = {
  render: () => (
    <div className="space-y-4 w-80">
      <Input disabled placeholder="Disabled input" />
      <Input 
        disabled 
        value="Disabled with value" 
        leftIcon={<User className="w-4 h-4" />} 
      />
      <PasswordInput disabled placeholder="Disabled password" />
      <Textarea disabled placeholder="Disabled textarea" />
    </div>
  ),
};

// Kitchen-themed Examples
export const KitchenExamples: Story = {
  render: () => (
    <div className="space-y-6 w-96">
      <FormField
        label="Recipe Name"
        description="Give your recipe a catchy name"
        required
      >
        <Input
          placeholder="Grandma's Secret Pasta"
          leftIcon={<Calendar className="w-4 h-4" />}
        />
      </FormField>
      
      <FormField
        label="Search Ingredients"
        description="Find ingredients for your recipe"
      >
        <SearchInput
          placeholder="Search for tomatoes, basil, cheese..."
          onSearch={(value) => console.log('Searching:', value)}
        />
      </FormField>
      
      <FormField
        label="Cooking Instructions"
        description="Step-by-step instructions"
      >
        <Textarea
          placeholder="1. Heat oil in a large pan...&#10;2. Add garlic and cook until fragrant...&#10;3. Add tomatoes and simmer..."
          size="lg"
        />
      </FormField>
    </div>
  ),
};

// All Variants Showcase
export const AllVariants: Story = {
  render: () => (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 max-w-6xl">
      <div className="space-y-3">
        <h3 className="font-semibold">Default Variant</h3>
        <Input placeholder="Default" />
        <Input placeholder="With icon" leftIcon={<Search className="w-4 h-4" />} />
        <Input placeholder="Error state" state="error" />
      </div>
      
      <div className="space-y-3">
        <h3 className="font-semibold">Filled Variant</h3>
        <Input variant="filled" placeholder="Filled" />
        <Input variant="filled" placeholder="With icon" leftIcon={<Mail className="w-4 h-4" />} />
        <Input variant="filled" placeholder="Success state" state="success" />
      </div>
      
      <div className="space-y-3">
        <h3 className="font-semibold">Ghost Variant</h3>
        <Input variant="ghost" placeholder="Ghost" />
        <Input variant="ghost" placeholder="With icon" leftIcon={<User className="w-4 h-4" />} />
        <Input variant="ghost" placeholder="Warning state" state="warning" />
      </div>
    </div>
  ),
};

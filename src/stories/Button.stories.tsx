import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import { fn } from '@storybook/test';
import { ChefHat, Heart, ShoppingCart, Plus, Download, Settings } from 'lucide-react';
import { Button, ButtonGroup, IconButton } from '@/components/design-system/atoms/Button';

const meta = {
  title: 'Design System/Atoms/Button',
  component: Button,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: 'Enhanced Button component with multiple variants, sizes, and states. Built with accessibility and performance in mind.',
      },
    },
  },
  tags: ['autodocs'],
  argTypes: {
    variant: {
      control: 'select',
      options: ['primary', 'secondary', 'ghost', 'danger', 'outline', 'link'],
      description: 'Visual style variant of the button',
    },
    size: {
      control: 'select',
      options: ['xs', 'sm', 'md', 'lg', 'xl'],
      description: 'Size of the button',
    },
    fullWidth: {
      control: 'boolean',
      description: 'Whether the button should take full width',
    },
    loading: {
      control: 'boolean',
      description: 'Loading state with spinner',
    },
    disabled: {
      control: 'boolean',
      description: 'Disabled state',
    },
    children: {
      control: 'text',
      description: 'Button content',
    },
  },
  args: {
    onClick: fn(),
  },
} satisfies Meta<typeof Button>;

export default meta;
type Story = StoryObj<typeof meta>;

// Basic Button Stories
export const Primary: Story = {
  args: {
    variant: 'primary',
    children: 'Primary Button',
  },
};

export const Secondary: Story = {
  args: {
    variant: 'secondary',
    children: 'Secondary Button',
  },
};

export const Ghost: Story = {
  args: {
    variant: 'ghost',
    children: 'Ghost Button',
  },
};

export const Danger: Story = {
  args: {
    variant: 'danger',
    children: 'Danger Button',
  },
};

export const Outline: Story = {
  args: {
    variant: 'outline',
    children: 'Outline Button',
  },
};

export const Link: Story = {
  args: {
    variant: 'link',
    children: 'Link Button',
  },
};

// Size Variants
export const Sizes: Story = {
  render: () => (
    <div className="flex items-center gap-4">
      <Button size="xs">Extra Small</Button>
      <Button size="sm">Small</Button>
      <Button size="md">Medium</Button>
      <Button size="lg">Large</Button>
      <Button size="xl">Extra Large</Button>
    </div>
  ),
};

// States
export const Loading: Story = {
  args: {
    loading: true,
    children: 'Loading...',
    loadingText: 'Processing...',
  },
};

export const Disabled: Story = {
  args: {
    disabled: true,
    children: 'Disabled Button',
  },
};

export const FullWidth: Story = {
  args: {
    fullWidth: true,
    children: 'Full Width Button',
  },
  parameters: {
    layout: 'padded',
  },
};

// With Icons
export const WithLeftIcon: Story = {
  args: {
    leftIcon: <ChefHat className="w-4 h-4" />,
    children: 'Cook Recipe',
  },
};

export const WithRightIcon: Story = {
  args: {
    rightIcon: <ShoppingCart className="w-4 h-4" />,
    children: 'Add to Cart',
  },
};

export const WithBothIcons: Story = {
  args: {
    leftIcon: <Heart className="w-4 h-4" />,
    rightIcon: <Plus className="w-4 h-4" />,
    children: 'Add to Favorites',
  },
};

// Icon Button Stories
export const IconButtons: Story = {
  render: () => (
    <div className="flex items-center gap-4">
      <IconButton
        icon={<Settings className="w-4 h-4" />}
        aria-label="Settings"
        size="sm"
      />
      <IconButton
        icon={<Heart className="w-5 h-5" />}
        aria-label="Like"
        variant="primary"
      />
      <IconButton
        icon={<Download className="w-5 h-5" />}
        aria-label="Download"
        variant="outline"
        size="lg"
      />
    </div>
  ),
};

// Button Group Stories
export const ButtonGroups: Story = {
  render: () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-sm font-medium mb-3">Horizontal Group</h3>
        <ButtonGroup>
          <Button variant="outline">Previous</Button>
          <Button>Current</Button>
          <Button variant="outline">Next</Button>
        </ButtonGroup>
      </div>
      
      <div>
        <h3 className="text-sm font-medium mb-3">Attached Group</h3>
        <ButtonGroup attached>
          <Button variant="outline">Day</Button>
          <Button variant="outline">Week</Button>
          <Button>Month</Button>
          <Button variant="outline">Year</Button>
        </ButtonGroup>
      </div>
      
      <div>
        <h3 className="text-sm font-medium mb-3">Vertical Group</h3>
        <ButtonGroup orientation="vertical" attached>
          <Button variant="outline">Top</Button>
          <Button>Middle</Button>
          <Button variant="outline">Bottom</Button>
        </ButtonGroup>
      </div>
    </div>
  ),
};

// Interactive Examples
export const InteractiveExample: Story = {
  render: () => {
    const [loading, setLoading] = React.useState(false);
    const [liked, setLiked] = React.useState(false);

    const handleClick = () => {
      setLoading(true);
      setTimeout(() => {
        setLoading(false);
        setLiked(!liked);
      }, 2000);
    };

    return (
      <div className="space-y-4">
        <Button
          onClick={handleClick}
          loading={loading}
          leftIcon={<Heart className={`w-4 h-4 ${liked ? 'fill-current' : ''}`} />}
          variant={liked ? 'danger' : 'outline'}
          loadingText="Processing..."
        >
          {liked ? 'Liked!' : 'Like Recipe'}
        </Button>
        
        <p className="text-sm text-gray-600">
          Click the button to see loading and state change
        </p>
      </div>
    );
  },
};

// All Variants Showcase
export const AllVariants: Story = {
  render: () => (
    <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
      <Button variant="primary">Primary</Button>
      <Button variant="secondary">Secondary</Button>
      <Button variant="ghost">Ghost</Button>
      <Button variant="danger">Danger</Button>
      <Button variant="outline">Outline</Button>
      <Button variant="link">Link</Button>
      
      <Button variant="primary" disabled>Primary Disabled</Button>
      <Button variant="secondary" loading>Secondary Loading</Button>
      <Button variant="ghost" leftIcon={<ChefHat className="w-4 h-4" />}>
        With Icon
      </Button>
      <Button variant="danger" size="sm">Small Danger</Button>
      <Button variant="outline" size="lg">Large Outline</Button>
      <Button variant="link" size="xs">Tiny Link</Button>
    </div>
  ),
  parameters: {
    layout: 'padded',
  },
};

// Accessibility Example
export const AccessibilityExample: Story = {
  render: () => (
    <div className="space-y-4">
      <div>
        <h3 className="text-sm font-medium mb-2">Keyboard Navigation</h3>
        <p className="text-xs text-gray-600 mb-3">
          Use Tab to navigate, Enter/Space to activate
        </p>
        <div className="flex gap-2">
          <Button>First</Button>
          <Button variant="secondary">Second</Button>
          <Button variant="outline">Third</Button>
        </div>
      </div>
      
      <div>
        <h3 className="text-sm font-medium mb-2">Screen Reader Support</h3>
        <div className="flex gap-2">
          <IconButton
            icon={<Heart className="w-4 h-4" />}
            aria-label="Add to favorites"
          />
          <Button loading loadingText="Saving recipe...">
            Save Recipe
          </Button>
          <Button disabled>
            Unavailable Action
          </Button>
        </div>
      </div>
    </div>
  ),
};

import { useMemo, useCallback, useRef, useEffect, useState } from 'react';

/**
 * Hook để memoize expensive calculations
 */
export function useExpensiveCalculation<T>(
  calculation: () => T,
  dependencies: React.DependencyList
): T {
  return useMemo(calculation, dependencies);
}

/**
 * Hook để debounce functions
 */
export function useDebounce<T extends (...args: any[]) => any>(
  callback: T,
  delay: number
): T {
  const timeoutRef = useRef<NodeJS.Timeout>();

  const debouncedCallback = useCallback(
    (...args: Parameters<T>) => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
      
      timeoutRef.current = setTimeout(() => {
        callback(...args);
      }, delay);
    },
    [callback, delay]
  ) as T;

  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  return debouncedCallback;
}

/**
 * Hook để throttle functions
 */
export function useThrottle<T extends (...args: any[]) => any>(
  callback: T,
  delay: number
): T {
  const lastRun = useRef(Date.now());

  const throttledCallback = useCallback(
    (...args: Parameters<T>) => {
      if (Date.now() - lastRun.current >= delay) {
        callback(...args);
        lastRun.current = Date.now();
      }
    },
    [callback, delay]
  ) as T;

  return throttledCallback;
}

/**
 * Hook để track performance metrics
 */
export function usePerformanceMetrics(componentName: string) {
  const renderStartTime = useRef<number>();
  const renderCount = useRef(0);

  useEffect(() => {
    renderStartTime.current = performance.now();
    renderCount.current += 1;
  });

  useEffect(() => {
    if (renderStartTime.current) {
      const renderTime = performance.now() - renderStartTime.current;
      
      // Log performance metrics in development
      if (process.env.NODE_ENV === 'development') {
        console.log(`🚀 ${componentName} render #${renderCount.current}: ${renderTime.toFixed(2)}ms`);
      }
    }
  });

  return {
    renderCount: renderCount.current,
  };
}

/**
 * Hook để lazy load data
 */
export function useLazyLoad<T>(
  loadFunction: () => Promise<T>,
  dependencies: React.DependencyList = []
) {
  const [data, setData] = useState<T | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  const load = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const result = await loadFunction();
      setData(result);
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Unknown error'));
    } finally {
      setLoading(false);
    }
  }, dependencies);

  return { data, loading, error, load };
}

/**
 * Hook để optimize re-renders với shallow comparison
 */
export function useShallowMemo<T extends Record<string, any>>(obj: T): T {
  return useMemo(() => obj, Object.values(obj));
}

/**
 * Hook để track component mount/unmount
 */
export function useComponentLifecycle(componentName: string) {
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      console.log(`🟢 ${componentName} mounted`);
    }
    
    return () => {
      if (process.env.NODE_ENV === 'development') {
        console.log(`🔴 ${componentName} unmounted`);
      }
    };
  }, [componentName]);
}

/**
 * Hook để optimize array operations
 */
export function useOptimizedArray<T>(
  array: T[],
  keyExtractor: (item: T, index: number) => string | number
) {
  return useMemo(() => {
    const map = new Map<string | number, T>();
    array.forEach((item, index) => {
      const key = keyExtractor(item, index);
      map.set(key, item);
    });
    return { array, map };
  }, [array, keyExtractor]);
}

/**
 * Hook để batch state updates
 */
export function useBatchedUpdates() {
  const [updates, setUpdates] = useState<(() => void)[]>([]);
  
  const batchUpdate = useCallback((updateFn: () => void) => {
    setUpdates(prev => [...prev, updateFn]);
  }, []);

  const flushUpdates = useCallback(() => {
    updates.forEach(update => update());
    setUpdates([]);
  }, [updates]);

  // Auto-flush updates on next tick
  useEffect(() => {
    if (updates.length > 0) {
      const timeoutId = setTimeout(flushUpdates, 0);
      return () => clearTimeout(timeoutId);
    }
  }, [updates, flushUpdates]);

  return { batchUpdate, flushUpdates };
}

import React, { Suspense, lazy } from 'react';
import { BrowserRouter, Routes, Route, Navigate } from "react-router-dom";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { AuthProvider } from "@/contexts/AuthContext";
import { MealPlanningProvider } from "@/contexts/MealPlanningContext";
import { KitchenProvider } from "@/contexts/KitchenContext";
import { CookingModeProvider } from "@/contexts/CookingModeContext";
import { ShoppingCartProvider } from "./contexts/ShoppingCartContext";

// Import cơ bản trước
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { ThemeProvider } from "@/components/ui/theme-provider";

// Core components that need to load immediately
import ConnectionStatusIndicator from "./components/ConnectionStatusIndicator";
import SupabaseInitializer from "./components/SupabaseInitializer";
import PerformanceMonitor from "./components/ui/performance-monitor";
import NotFound from "./pages/NotFound";

// Lazy load pages for better performance
const Index = lazy(() => import("./pages/Index"));
const LoginPage = lazy(() => import("./pages/LoginPage"));
const RegisterPage = lazy(() => import("./pages/RegisterPage"));
const ProfilePage = lazy(() => import("./pages/ProfilePage"));
const RecipeDetailPage = lazy(() => import("./pages/RecipeDetailPage"));
const BlogPage = lazy(() => import("./pages/BlogPage"));
const BlogDetailPage = lazy(() => import("./pages/BlogDetailPage"));
const AdminPage = lazy(() => import("./pages/AdminPage"));
const RecipeLibraryPage = lazy(() => import("./pages/RecipeLibraryPage"));
const MyRecipesPageNew = lazy(() => import("./pages/MyRecipesPageNew"));
const MealPlansPage = lazy(() => import("./pages/MealPlansPage"));
const DashboardPage = lazy(() => import("./pages/DashboardPage"));
const ImprovedShoppingCartPage = lazy(() => import("./pages/ImprovedShoppingCartPage"));
const MealPlannerPage = lazy(() => import("./pages/MealPlannerPage"));
const MealTemplatesPage = lazy(() => import("./pages/MealTemplatesPage"));
const VietnameseFoodPage = lazy(() => import("./pages/VietnameseFoodPage"));
const AdminBulkManagementPage = lazy(() => import("./pages/AdminBulkManagementPage"));
const DataImportPage = lazy(() => import("./pages/DataImportPage"));
const DebugPage = lazy(() => import("./pages/DebugPage"));
const DesignSystemDemo = lazy(() => import("./pages/DesignSystemDemo"));
const SimpleDesignDemo = lazy(() => import("./pages/SimpleDesignDemo"));
const LayoutDemo = lazy(() => import("./pages/LayoutDemo"));

// Lazy load heavy components
const KitchenCommandCenter = lazy(() => import("./components/KitchenCommandCenter"));
const CookingMode = lazy(() => import("./components/cooking/CookingMode"));
const MealPlanManager = lazy(() => import("./components/meal-planning/MealPlanManager"));
const ImprovedMealPlannerPage = lazy(() => import("./components/meal-planning/ImprovedMealPlannerPage"));
const EnhancedMealPlannerMain = lazy(() => import("./components/meal-planning/EnhancedMealPlannerMain"));
const MobileCookingMode = lazy(() => import("./components/cooking/MobileCookingMode"));
const AllInOneCookingMode = lazy(() => import("./components/cooking/AllInOneCookingMode"));

// Loading Component for Suspense
const LoadingSpinner = () => (
  <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-orange-50 to-red-50">
    <div className="text-center">
      <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-500 mx-auto mb-4"></div>
      <p className="text-gray-600 text-lg">Đang tải...</p>
    </div>
  </div>
);

// Error Boundary Component
class ErrorBoundary extends React.Component<{children: React.ReactNode}, {hasError: boolean, error: Error | null}> {
  constructor(props: {children: React.ReactNode}) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error };
  }

  componentDidCatch(error, errorInfo) {
    console.error('🚨 Error caught by boundary:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="min-h-screen flex items-center justify-center bg-red-50 p-6">
          <div className="bg-white rounded-lg shadow-lg p-8 max-w-md w-full text-center">
            <div className="text-red-500 text-6xl mb-4">❌</div>
            <h1 className="text-2xl font-bold text-gray-900 mb-4">
              Có lỗi xảy ra
            </h1>
            <p className="text-gray-600 mb-6">
              {this.state.error?.message || 'Unknown error'}
            </p>
            <button
              onClick={() => window.location.reload()}
              className="bg-orange-500 hover:bg-orange-600 text-white font-medium py-2 px-6 rounded-lg transition-colors"
            >
              Tải lại trang
            </button>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

const queryClient = new QueryClient();

// Simple test pages first
const TestHomePage = () => {
  console.log('🏠 Home page rendering...');
  return (
    <div style={{
      padding: '20px',
      backgroundColor: '#f0f0f0',
      minHeight: '100vh',
      fontFamily: 'Arial, sans-serif'
    }}>
      <h1 style={{ color: '#333', fontSize: '24px', marginBottom: '20px' }}>
        🎉 Angiday Recipe Hub - Đang khôi phục...
      </h1>
      <p style={{ color: '#666', fontSize: '16px', marginBottom: '10px' }}>
        Trang chủ đang được khôi phục từng bước. Vui lòng chờ...
      </p>
      <div style={{
        backgroundColor: '#fff',
        padding: '15px',
        borderRadius: '8px',
        border: '1px solid #ddd',
        marginTop: '20px'
      }}>
        <h2 style={{ color: '#333', fontSize: '18px', marginBottom: '10px' }}>
          Tiến độ khôi phục:
        </h2>
        <ul style={{ color: '#666', fontSize: '14px' }}>
          <li>✅ React hoạt động bình thường</li>
          <li>✅ Routing hoạt động</li>
          <li>✅ QueryClient đã load</li>
          <li>✅ AuthContext đã load</li>
          <li>✅ MealPlanningContext đã load</li>
          <li>🔄 Đang load trang chủ...</li>
        </ul>
      </div>
    </div>
  );
};

const App = () => {
  console.log('🎯 App component rendering...');

  return (
    <ErrorBoundary>
      <QueryClientProvider client={queryClient}>
        <ThemeProvider defaultTheme="light" storageKey="angiday-theme">
          <SupabaseInitializer>
            <AuthProvider>
                <MealPlanningProvider>
                  <KitchenProvider>
                    <ShoppingCartProvider>
                      <CookingModeProvider>
                        <TooltipProvider>
                        <Toaster />
                        <Sonner />
                        <ConnectionStatusIndicator position="top-right" autoHide={true} />
                        <PerformanceMonitor componentName="App" />
                        <BrowserRouter>
                          <Suspense fallback={<LoadingSpinner />}>
                            <Routes>
                              {/* Main Pages */}
                              <Route path="/" element={<Index />} />

                              {/* Authentication */}
                              <Route path="/login" element={<LoginPage />} />
                              <Route path="/register" element={<RegisterPage />} />
                              <Route path="/profile" element={<ProfilePage />} />

                              {/* Dashboard */}
                              <Route path="/dashboard" element={<DashboardPage />} />

                              {/* Blog */}
                              <Route path="/blog" element={<BlogPage />} />
                              <Route path="/blog/:id" element={<BlogDetailPage />} />

                              {/* Recipes */}
                              <Route path="/recipes" element={<RecipeLibraryPage />} />
                              <Route path="/my-recipes" element={<MyRecipesPageNew />} />
                              <Route path="/recipes-library" element={<Navigate to="/recipes" replace />} />
                              <Route path="/recipes/:id" element={<RecipeDetailPage />} />

                              {/* Shopping */}
                              <Route path="/shopping-cart" element={<ImprovedShoppingCartPage />} />

                              {/* Meal Planning */}
                              <Route path="/meal-planner" element={<MealPlannerPage />} />
                              <Route path="/meal-templates" element={<MealTemplatesPage />} />
                              <Route path="/vietnamese-food" element={<VietnameseFoodPage />} />

                              {/* Vietnamese URL redirects to English */}
                              <Route path="/ke-hoach-nau-an" element={<Navigate to="/meal-planner" replace />} />
                              <Route path="/thuc-don-mau" element={<Navigate to="/meal-templates" replace />} />
                              <Route path="/mon-an-viet-nam" element={<Navigate to="/vietnamese-food" replace />} />

                              {/* Admin */}
                              <Route path="/admin" element={<AdminPage />} />
                              <Route path="/admin/bulk-management" element={<AdminBulkManagementPage />} />
                              <Route path="/data-import" element={<DataImportPage />} />

                              {/* Kitchen Command Center */}
                              <Route path="/kitchen" element={<KitchenCommandCenter />} />
                              <Route path="/daily-menu" element={<Navigate to="/meal-plans" replace />} />
                              <Route path="/meal-plans" element={<MealPlansPage />} />

                              {/* Vietnamese URL redirects */}
                              <Route path="/thuc-don" element={<Navigate to="/meal-plans" replace />} />
                              <Route path="/bep" element={<Navigate to="/kitchen" replace />} />

                              {/* Cooking routes */}
                              <Route path="/cooking-mode" element={<CookingMode />} />
                              <Route path="/mobile-cooking" element={<MobileCookingMode />} />
                              <Route path="/all-in-one-cooking" element={<AllInOneCookingMode />} />

                              <Route path="/meal-planning" element={<MealPlanManager />} />

                              {/* Debug pages */}
                              <Route path="/debug" element={<DebugPage />} />
                              <Route path="/design-system" element={<DesignSystemDemo />} />
                              <Route path="/simple-design" element={<SimpleDesignDemo />} />
                              <Route path="/layout-demo" element={<LayoutDemo />} />
                              <Route path="/meal-planner-test" element={<ImprovedMealPlannerPage />} />
                              <Route path="/enhanced-planner" element={<EnhancedMealPlannerMain />} />

                              {/* 404 Page */}
                              <Route path="*" element={<NotFound />} />
                            </Routes>
                          </Suspense>
                        </BrowserRouter>
                      </TooltipProvider>
                    </CookingModeProvider>
                  </ShoppingCartProvider>
                </KitchenProvider>
              </MealPlanningProvider>
            </AuthProvider>
          </SupabaseInitializer>
        </ThemeProvider>
      </QueryClientProvider>
    </ErrorBoundary>
  );
};

export default App;

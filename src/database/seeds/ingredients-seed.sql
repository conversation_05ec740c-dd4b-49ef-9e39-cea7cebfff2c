-- =====================================================
-- Dữ liệu seed cho nguyên liệu và giá cả Việt Nam 2024
-- Dựa trên nghiên cứu thị trường thực tế
-- =====================================================

-- =====================================================
-- 1. DANH MỤC NGUYÊN LIỆU
-- =====================================================

INSERT INTO ingredient_categories (id, name, display_name, description, sort_order) VALUES
('aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', 'meat_seafood', 'Thịt & Hải sản', 'Thịt các lo<PERSON>, cá, tôm, cua, mực...', 1),
('bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb', 'vegetables', 'Rau củ quả', 'Rau xanh, củ quả, trái cây tươi', 2),
('cccccccc-cccc-cccc-cccc-cccccccccccc', 'grains_cereals', 'Ngũ cốc & Tinh bột', 'Gạo, bột, bánh mì, mì, bún...', 3),
('dddddddd-dddd-dddd-dddd-dddddddddddd', 'dairy_eggs', 'Sữa & Trứng', 'Sữa, phô mai, bơ, trứng các loại', 4),
('eeeeeeee-eeee-eeee-eeee-eeeeeeeeeeee', 'seasonings', 'Gia vị & Nước chấm', 'Muối, đường, nước mắm, tương ớt...', 5),
('ffffffff-ffff-ffff-ffff-ffffffffffff', 'oils_fats', 'Dầu ăn & Chất béo', 'Dầu ăn, mỡ, bơ thực vật...', 6),
('gggggggg-gggg-gggg-gggg-gggggggggggg', 'herbs_spices', 'Rau thơm & Gia vị khô', 'Ngò, húng, tiêu, quế, hồi...', 7),
('hhhhhhhh-hhhh-hhhh-hhhh-hhhhhhhhhhhh', 'processed_foods', 'Thực phẩm chế biến', 'Chả cá, nem, giò, pate...', 8);

-- =====================================================
-- 2. NGUYÊN LIỆU THỊT & HẢI SẢN
-- =====================================================

INSERT INTO ingredients (id, name, display_name, category_id, base_unit_id, base_price_per_unit, alternative_names) VALUES
-- Thịt bò (giá: 280-320k/kg = 28-32k/lạng)
('10000001-0000-0000-0000-000000000001', 'thit_bo', 'Thịt bò', 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', '11111111-1111-1111-1111-111111111111', 30000, ARRAY['thịt bò', 'beef', 'thịt bò tươi']),
('10000001-0000-0000-0000-000000000002', 'thit_bo_ba_chi', 'Thịt bò ba chỉ', 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', '11111111-1111-1111-1111-111111111111', 32000, ARRAY['thịt bò ba chỉ', 'beef belly']),

-- Thịt heo (giá: 140-180k/kg = 14-18k/lạng)
('10000001-0000-0000-0000-000000000003', 'thit_heo', 'Thịt heo', 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', '11111111-1111-1111-1111-111111111111', 16000, ARRAY['thịt heo', 'pork', 'thịt lợn']),
('10000001-0000-0000-0000-000000000004', 'thit_heo_ba_chi', 'Thịt heo ba chỉ', 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', '11111111-1111-1111-1111-111111111111', 18000, ARRAY['thịt heo ba chỉ', 'pork belly']),

-- Thịt gà (giá: 70-90k/kg = 7-9k/lạng)
('10000001-0000-0000-0000-000000000005', 'thit_ga', 'Thịt gà', 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', '11111111-1111-1111-1111-111111111111', 8000, ARRAY['thịt gà', 'chicken']),
('10000001-0000-0000-0000-000000000006', 'canh_ga', 'Cánh gà', 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', '11111111-1111-1111-1111-111111111111', 9000, ARRAY['cánh gà', 'chicken wings']),

-- Hải sản (giá: 100-300k/kg = 10-30k/lạng)
('10000001-0000-0000-0000-000000000007', 'ca_tra', 'Cá tra', 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', '11111111-1111-1111-1111-111111111111', 12000, ARRAY['cá tra', 'pangasius']),
('10000001-0000-0000-0000-000000000008', 'tom_su', 'Tôm sú', 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', '11111111-1111-1111-1111-111111111111', 25000, ARRAY['tôm sú', 'tiger shrimp']),
('10000001-0000-0000-0000-000000000009', 'muc_tuoi', 'Mực tươi', 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', '11111111-1111-1111-1111-111111111111', 18000, ARRAY['mực tươi', 'fresh squid']);

-- =====================================================
-- 3. NGUYÊN LIỆU RAU CỦ QUẢ
-- =====================================================

INSERT INTO ingredients (id, name, display_name, category_id, base_unit_id, base_price_per_unit, alternative_names) VALUES
-- Rau lá (giá: 15-30k/kg = 1.5-3k/lạng)
('*************-0000-0000-000000000001', 'rau_muong', 'Rau muống', 'bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb', '11111111-1111-1111-1111-111111111111', 2000, ARRAY['rau muống', 'water spinach']),
('*************-0000-0000-000000000002', 'cai_thia', 'Cải thìa', 'bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb', '11111111-1111-1111-1111-111111111111', 2500, ARRAY['cải thìa', 'bok choy']),
('*************-0000-0000-000000000003', 'rau_can', 'Rau cần', 'bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb', '11111111-1111-1111-1111-111111111111', 3000, ARRAY['rau cần', 'celery']),

-- Củ quả (giá: 15-40k/kg = 1.5-4k/lạng, hoặc 3-8k/củ)
('*************-0000-0000-000000000004', 'ca_chua', 'Cà chua', 'bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb', '11111111-1111-1111-1111-111111111111', 2500, ARRAY['cà chua', 'tomato']),
('*************-0000-0000-000000000005', 'khoai_tay', 'Khoai tây', 'bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb', '11111111-1111-1111-1111-111111111111', 2000, ARRAY['khoai tây', 'potato']),
('*************-0000-0000-000000000006', 'ca_rot', 'Cà rốt', 'bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb', '11111111-1111-1111-1111-111111111111', 1800, ARRAY['cà rốt', 'carrot']),
('*************-0000-0000-000000000007', 'hanh_tay', 'Hành tây', 'bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb', '33333333-3333-3333-3333-333333333334', 5000, ARRAY['hành tây', 'onion']), -- Giá theo củ
('*************-0000-0000-000000000008', 'toi', 'Tỏi', 'bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb', '33333333-3333-3333-3333-33333333333b', 2000, ARRAY['tỏi', 'garlic']), -- Giá theo tép
('*************-0000-0000-000000000009', 'gung', 'Gừng', 'bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb', '11111111-1111-1111-1111-111111111111', 6000, ARRAY['gừng', 'ginger']);

-- =====================================================
-- 4. NGŨ CỐC & TINH BỘT
-- =====================================================

INSERT INTO ingredients (id, name, display_name, category_id, base_unit_id, base_price_per_unit, alternative_names) VALUES
-- Gạo (giá: 20-30k/kg = 2-3k/lạng)
('30000001-0000-0000-0000-000000000001', 'gao_te', 'Gạo tẻ', 'cccccccc-cccc-cccc-cccc-cccccccccccc', '11111111-1111-1111-1111-111111111111', 2500, ARRAY['gạo tẻ', 'jasmine rice']),
('30000001-0000-0000-0000-000000000002', 'gao_nep', 'Gạo nếp', 'cccccccc-cccc-cccc-cccc-cccccccccccc', '11111111-1111-1111-1111-111111111111', 3000, ARRAY['gạo nếp', 'sticky rice']),

-- Bột (giá: 15-25k/kg = 1.5-2.5k/lạng)
('30000001-0000-0000-0000-000000000003', 'bot_mi', 'Bột mì', 'cccccccc-cccc-cccc-cccc-cccccccccccc', '11111111-1111-1111-1111-111111111111', 2000, ARRAY['bột mì', 'wheat flour']),
('30000001-0000-0000-0000-000000000004', 'bot_nang', 'Bột năng', 'cccccccc-cccc-cccc-cccc-cccccccccccc', '11111111-1111-1111-1111-111111111111', 2200, ARRAY['bột năng', 'tapioca starch']),

-- Mì, bún (giá: 25-35k/kg = 2.5-3.5k/lạng)
('30000001-0000-0000-0000-000000000005', 'mi_tom', 'Mì tôm', 'cccccccc-cccc-cccc-cccc-cccccccccccc', '33333333-3333-3333-3333-33333333333d', 3000, ARRAY['mì tôm', 'instant noodles']), -- Giá theo gói
('30000001-0000-0000-0000-000000000006', 'bun_tuoi', 'Bún tươi', 'cccccccc-cccc-cccc-cccc-cccccccccccc', '11111111-1111-1111-1111-111111111111', 3000, ARRAY['bún tươi', 'fresh rice noodles']);

-- =====================================================
-- 5. SỮA & TRỨNG
-- =====================================================

INSERT INTO ingredients (id, name, display_name, category_id, base_unit_id, base_price_per_unit, alternative_names) VALUES
-- Sữa (giá: 25-35k/lít)
('40000001-0000-0000-0000-000000000001', 'sua_tuoi', 'Sữa tươi', 'dddddddd-dddd-dddd-dddd-dddddddddddd', '22222222-2222-2222-2222-222222222222', 30000, ARRAY['sữa tươi', 'fresh milk']),
('40000001-0000-0000-0000-000000000002', 'sua_dac', 'Sữa đặc', 'dddddddd-dddd-dddd-dddd-dddddddddddd', '33333333-3333-3333-3333-33333333333f', 25000, ARRAY['sữa đặc', 'condensed milk']), -- Giá theo lon

-- Trứng (giá: 3-5k/quả)
('40000001-0000-0000-0000-000000000003', 'trung_ga', 'Trứng gà', 'dddddddd-dddd-dddd-dddd-dddddddddddd', '33333333-3333-3333-3333-333333333335', 4000, ARRAY['trứng gà', 'chicken egg']), -- Giá theo quả
('40000001-0000-0000-0000-000000000004', 'trung_vit', 'Trứng vịt', 'dddddddd-dddd-dddd-dddd-dddddddddddd', '33333333-3333-3333-3333-333333333335', 5000, ARRAY['trứng vịt', 'duck egg']);

-- =====================================================
-- 6. GIA VỊ & NƯỚC CHẤM
-- =====================================================

INSERT INTO ingredients (id, name, display_name, category_id, base_unit_id, base_price_per_unit, alternative_names) VALUES
-- Gia vị cơ bản (giá: 10-50k/kg = 1-5k/lạng)
('50000001-0000-0000-0000-000000000001', 'muoi', 'Muối', 'eeeeeeee-eeee-eeee-eeee-eeeeeeeeeeee', '11111111-1111-1111-1111-111111111111', 1000, ARRAY['muối', 'salt']),
('50000001-0000-0000-0000-000000000002', 'duong', 'Đường', 'eeeeeeee-eeee-eeee-eeee-eeeeeeeeeeee', '11111111-1111-1111-1111-111111111111', 2000, ARRAY['đường', 'sugar']),
('50000001-0000-0000-0000-000000000003', 'nuoc_mam', 'Nước mắm', 'eeeeeeee-eeee-eeee-eeee-eeeeeeeeeeee', '22222222-2222-2222-2222-222222222222', 40000, ARRAY['nước mắm', 'fish sauce']),
('50000001-0000-0000-0000-000000000004', 'tuong_ot', 'Tương ớt', 'eeeeeeee-eeee-eeee-eeee-eeeeeeeeeeee', '33333333-3333-3333-3333-333333333340', 20000, ARRAY['tương ớt', 'chili sauce']), -- Giá theo chai
('50000001-0000-0000-0000-000000000005', 'xi_dau', 'Xì dầu', 'eeeeeeee-eeee-eeee-eeee-eeeeeeeeeeee', '33333333-3333-3333-3333-333333333340', 15000, ARRAY['xì dầu', 'soy sauce']);

-- =====================================================
-- 7. DẦU ĂN & CHẤT BÉO
-- =====================================================

INSERT INTO ingredients (id, name, display_name, category_id, base_unit_id, base_price_per_unit, alternative_names) VALUES
-- Dầu ăn (giá: 45-60k/lít)
('60000001-0000-0000-0000-000000000001', 'dau_an', 'Dầu ăn', 'ffffffff-ffff-ffff-ffff-ffffffffffff', '22222222-2222-2222-2222-222222222222', 50000, ARRAY['dầu ăn', 'cooking oil']),
('60000001-0000-0000-0000-000000000002', 'dau_oliu', 'Dầu oliu', 'ffffffff-ffff-ffff-ffff-ffffffffffff', '22222222-2222-2222-2222-222222222222', 120000, ARRAY['dầu oliu', 'olive oil']),
('60000001-0000-0000-0000-000000000003', 'bo', 'Bơ', 'ffffffff-ffff-ffff-ffff-ffffffffffff', '11111111-1111-1111-1111-111111111111', 100000, ARRAY['bơ', 'butter']);

-- =====================================================
-- 8. RAU THƠM & GIA VỊ KHÔ
-- =====================================================

INSERT INTO ingredients (id, name, display_name, category_id, base_unit_id, base_price_per_unit, alternative_names) VALUES
-- Rau thơm (giá: 2-5k/bó hoặc 20-50k/kg)
('70000001-0000-0000-0000-000000000001', 'ngo_gai', 'Ngò gai', 'gggggggg-gggg-gggg-gggg-gggggggggggg', '33333333-3333-3333-3333-33333333333c', 3000, ARRAY['ngò gai', 'cilantro']), -- Giá theo bó
('70000001-0000-0000-0000-000000000002', 'hung_que', 'Húng quế', 'gggggggg-gggg-gggg-gggg-gggggggggggg', '33333333-3333-3333-3333-33333333333c', 4000, ARRAY['húng quế', 'basil']),
('70000001-0000-0000-0000-000000000003', 'la_chuoi', 'Lá chuối', 'gggggggg-gggg-gggg-gggg-gggggggggggg', '33333333-3333-3333-3333-333333333337', 2000, ARRAY['lá chuối', 'banana leaf']), -- Giá theo lá

-- Gia vị khô (giá: 50-200k/kg = 5-20k/lạng)
('70000001-0000-0000-0000-000000000004', 'tieu_den', 'Tiêu đen', 'gggggggg-gggg-gggg-gggg-gggggggggggg', '11111111-1111-1111-1111-111111111111', 15000, ARRAY['tiêu đen', 'black pepper']),
('70000001-0000-0000-0000-000000000005', 'que', 'Quế', 'gggggggg-gggg-gggg-gggg-gggggggggggg', '11111111-1111-1111-1111-111111111111', 20000, ARRAY['quế', 'cinnamon']),
('70000001-0000-0000-0000-000000000006', 'hoi', 'Hồi', 'gggggggg-gggg-gggg-gggg-gggggggggggg', '11111111-1111-1111-1111-111111111111', 18000, ARRAY['hồi', 'star anise']);

-- =====================================================
-- 9. ĐƠN VỊ ĐƯỢC PHÉP CHO TỪNG NGUYÊN LIỆU
-- =====================================================

-- Thịt & Hải sản - thường bán theo kg, lạng, gram
INSERT INTO ingredient_allowed_units (ingredient_id, unit_id, is_primary) VALUES
-- Thịt bò
('10000001-0000-0000-0000-000000000001', '11111111-1111-1111-1111-111111111111', true),  -- lạng (primary)
('10000001-0000-0000-0000-000000000001', '11111111-1111-1111-1111-111111111114', false), -- kg
('10000001-0000-0000-0000-000000000001', '11111111-1111-1111-1111-111111111112', false), -- gram

-- Thịt heo
('10000001-0000-0000-0000-000000000003', '11111111-1111-1111-1111-111111111111', true),  -- lạng (primary)
('10000001-0000-0000-0000-000000000003', '11111111-1111-1111-1111-111111111114', false), -- kg
('10000001-0000-0000-0000-000000000003', '11111111-1111-1111-1111-111111111112', false), -- gram

-- Tôm sú
('10000001-0000-0000-0000-000000000008', '11111111-1111-1111-1111-111111111111', true),  -- lạng (primary)
('10000001-0000-0000-0000-000000000008', '11111111-1111-1111-1111-111111111114', false), -- kg
('10000001-0000-0000-0000-000000000008', '33333333-3333-3333-3333-333333333333', false); -- cái

-- Rau củ - có thể bán theo kg, lạng hoặc củ/quả
INSERT INTO ingredient_allowed_units (ingredient_id, unit_id, is_primary) VALUES
-- Cà chua
('*************-0000-0000-000000000004', '11111111-1111-1111-1111-111111111111', true),  -- lạng (primary)
('*************-0000-0000-000000000004', '11111111-1111-1111-1111-111111111114', false), -- kg
('*************-0000-0000-000000000004', '33333333-3333-3333-3333-333333333335', false), -- quả

-- Hành tây (chủ yếu bán theo củ)
('*************-0000-0000-000000000007', '33333333-3333-3333-3333-333333333334', true),  -- củ (primary)
('*************-0000-0000-000000000007', '11111111-1111-1111-1111-111111111111', false), -- lạng
('*************-0000-0000-000000000007', '11111111-1111-1111-1111-111111111114', false); -- kg

-- Tỏi (chủ yếu bán theo tép)
('*************-0000-0000-000000000008', '33333333-3333-3333-3333-33333333333b', true),  -- tép (primary)
('*************-0000-0000-000000000008', '33333333-3333-3333-3333-333333333334', false), -- củ
('*************-0000-0000-000000000008', '11111111-1111-1111-1111-111111111111', false); -- lạng

-- Ngũ cốc - thường bán theo kg, lạng
INSERT INTO ingredient_allowed_units (ingredient_id, unit_id, is_primary) VALUES
-- Gạo tẻ
('30000001-0000-0000-0000-000000000001', '11111111-1111-1111-1111-111111111114', true),  -- kg (primary)
('30000001-0000-0000-0000-000000000001', '11111111-1111-1111-1111-111111111111', false), -- lạng
('30000001-0000-0000-0000-000000000001', '22222222-2222-2222-2222-222222222228', false); -- cốc

-- Sữa & Trứng
INSERT INTO ingredient_allowed_units (ingredient_id, unit_id, is_primary) VALUES
-- Sữa tươi
('40000001-0000-0000-0000-000000000001', '22222222-2222-2222-2222-222222222222', true),  -- lít (primary)
('40000001-0000-0000-0000-000000000001', '22222222-2222-2222-2222-222222222223', false), -- ml
('40000001-0000-0000-0000-000000000001', '22222222-2222-2222-2222-222222222228', false); -- cốc

-- Trứng gà
('40000001-0000-0000-0000-000000000003', '33333333-3333-3333-3333-333333333335', true),  -- quả (primary)
('40000001-0000-0000-0000-000000000003', '33333333-3333-3333-3333-333333333342', false); -- tá

-- Gia vị - thường dùng ít, đo bằng muỗng, nhúm
INSERT INTO ingredient_allowed_units (ingredient_id, unit_id, is_primary) VALUES
-- Muối
('50000001-0000-0000-0000-000000000001', '22222222-2222-2222-2222-222222222227', true),  -- muỗng canh (primary)
('50000001-0000-0000-0000-000000000001', '22222222-2222-2222-2222-222222222226', false), -- thìa
('50000001-0000-0000-0000-000000000001', '44444444-4444-4444-4444-444444444444', false), -- nhúm
('50000001-0000-0000-0000-000000000001', '11111111-1111-1111-1111-111111111111', false); -- lạng

-- Nước mắm
('50000001-0000-0000-0000-000000000003', '22222222-2222-2222-2222-222222222227', true),  -- muỗng canh (primary)
('50000001-0000-0000-0000-000000000003', '22222222-2222-2222-2222-222222222223', false), -- ml
('50000001-0000-0000-0000-000000000003', '22222222-2222-2222-2222-222222222222', false), -- lít
('50000001-0000-0000-0000-000000000003', '33333333-3333-3333-3333-333333333340', false); -- chai

-- =====================================================
-- 10. GIÁ CẢ CHI TIẾT THEO ĐƠN VỊ VÀ KHU VỰC
-- =====================================================

-- Giá thịt bò theo các đơn vị khác nhau
INSERT INTO ingredient_prices (ingredient_id, unit_id, price, region, source, effective_date) VALUES
-- Thịt bò - Hà Nội
('10000001-0000-0000-0000-000000000001', '11111111-1111-1111-1111-111111111111', 30000, 'hanoi', 'market', '2024-01-01'), -- 30k/lạng
('10000001-0000-0000-0000-000000000001', '11111111-1111-1111-1111-111111111114', 300000, 'hanoi', 'market', '2024-01-01'), -- 300k/kg
('10000001-0000-0000-0000-000000000001', '11111111-1111-1111-1111-111111111112', 300, 'hanoi', 'market', '2024-01-01'), -- 300đ/gram

-- Thịt bò - TP.HCM (thường đắt hơn 5-10%)
('10000001-0000-0000-0000-000000000001', '11111111-1111-1111-1111-111111111111', 32000, 'hcm', 'market', '2024-01-01'), -- 32k/lạng
('10000001-0000-0000-0000-000000000001', '11111111-1111-1111-1111-111111111114', 320000, 'hcm', 'market', '2024-01-01'), -- 320k/kg

-- Hành tây theo củ và kg
('*************-0000-0000-000000000007', '33333333-3333-3333-3333-333333333334', 5000, 'vietnam', 'market', '2024-01-01'), -- 5k/củ
('*************-0000-0000-000000000007', '11111111-1111-1111-1111-111111111111', 3000, 'vietnam', 'market', '2024-01-01'), -- 3k/lạng
('*************-0000-0000-000000000007', '11111111-1111-1111-1111-111111111114', 30000, 'vietnam', 'market', '2024-01-01'), -- 30k/kg

-- Tỏi theo tép và củ
('*************-0000-0000-000000000008', '33333333-3333-3333-3333-33333333333b', 2000, 'vietnam', 'market', '2024-01-01'), -- 2k/tép
('*************-0000-0000-000000000008', '33333333-3333-3333-3333-333333333334', 15000, 'vietnam', 'market', '2024-01-01'), -- 15k/củ (≈7-8 tép)

-- Trứng gà
('40000001-0000-0000-0000-000000000003', '33333333-3333-3333-3333-333333333335', 4000, 'vietnam', 'market', '2024-01-01'), -- 4k/quả
('40000001-0000-0000-0000-000000000003', '33333333-3333-3333-3333-333333333342', 48000, 'vietnam', 'market', '2024-01-01'), -- 48k/tá (12 quả)

-- Nước mắm theo các đơn vị
('50000001-0000-0000-0000-000000000003', '22222222-2222-2222-2222-222222222227', 600, 'vietnam', 'market', '2024-01-01'), -- 600đ/muỗng canh (15ml)
('50000001-0000-0000-0000-000000000003', '22222222-2222-2222-2222-222222222223', 40, 'vietnam', 'market', '2024-01-01'), -- 40đ/ml
('50000001-0000-0000-0000-000000000003', '22222222-2222-2222-2222-222222222222', 40000, 'vietnam', 'market', '2024-01-01'), -- 40k/lít
('50000001-0000-0000-0000-000000000003', '33333333-3333-3333-3333-333333333340', 25000, 'vietnam', 'supermarket', '2024-01-01'); -- 25k/chai (500ml)

-- Cập nhật timestamps
UPDATE ingredients SET created_at = NOW(), updated_at = NOW();
UPDATE ingredient_categories SET created_at = NOW();
UPDATE ingredient_allowed_units SET created_at = NOW();
UPDATE ingredient_prices SET created_at = NOW();

-- =====================================================
-- Hệ thống chuẩn hóa đơn vị đo lường và giá cả nguyên liệu
-- Thiết kế cho thị trường Việt Nam
-- =====================================================

-- Bảng đơn vị đo lường
CREATE TABLE units (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(50) NOT NULL UNIQUE, -- 'gram', 'lạng', 'kg', 'lít', 'ml', 'củ', 'quả'
    display_name VARCHAR(100) NOT NULL, -- 'Gram (g)', 'Lạng (100g)', 'Kilogram (kg)'
    type VARCHAR(20) NOT NULL CHECK (type IN ('weight', 'volume', 'count')),
    base_unit_id UUID REFERENCES units(id), -- Đơn vị cơ sở (lạng cho weight, lít cho volume)
    conversion_factor DECIMAL(15,6) NOT NULL DEFAULT 1.0, -- Hệ số chuyển đổi về đơn vị cơ sở
    is_base_unit BOOLEAN DEFAULT FALSE, -- Đánh dấu đơn vị cơ sở
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Bảng danh mục nguyên liệu
CREATE TABLE ingredient_categories (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) NOT NULL UNIQUE,
    display_name VARCHAR(200) NOT NULL,
    description TEXT,
    parent_id UUID REFERENCES ingredient_categories(id),
    sort_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT NOW()
);

-- Bảng nguyên liệu
CREATE TABLE ingredients (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(200) NOT NULL,
    display_name VARCHAR(300) NOT NULL,
    category_id UUID NOT NULL REFERENCES ingredient_categories(id),
    base_unit_id UUID NOT NULL REFERENCES units(id), -- Đơn vị cơ sở để tính giá
    base_price_per_unit DECIMAL(12,2) NOT NULL, -- Giá cơ sở theo đơn vị cơ sở (VND)
    alternative_names TEXT[], -- Các tên gọi khác: ['thịt bò', 'beef', 'thịt bò tươi']
    description TEXT,
    nutritional_info JSONB, -- Thông tin dinh dưỡng
    storage_info JSONB, -- Thông tin bảo quản
    seasonality JSONB, -- Thông tin mùa vụ
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    
    -- Indexes
    CONSTRAINT ingredients_name_category_unique UNIQUE(name, category_id)
);

-- Bảng giá nguyên liệu theo thời gian và khu vực
CREATE TABLE ingredient_prices (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    ingredient_id UUID NOT NULL REFERENCES ingredients(id),
    unit_id UUID NOT NULL REFERENCES units(id),
    price DECIMAL(12,2) NOT NULL, -- Giá theo đơn vị cụ thể (VND)
    region VARCHAR(100) DEFAULT 'vietnam', -- 'hanoi', 'hcm', 'danang', 'vietnam'
    source VARCHAR(100), -- 'market', 'supermarket', 'online', 'manual'
    source_name VARCHAR(200), -- Tên chợ/siêu thị cụ thể
    effective_date DATE NOT NULL DEFAULT CURRENT_DATE,
    expiry_date DATE, -- Ngày hết hiệu lực (NULL = vô thời hạn)
    is_active BOOLEAN DEFAULT TRUE,
    notes TEXT,
    created_at TIMESTAMP DEFAULT NOW(),
    created_by UUID, -- User ID của người tạo
    
    -- Constraints
    CONSTRAINT ingredient_prices_positive_price CHECK (price > 0),
    CONSTRAINT ingredient_prices_valid_dates CHECK (expiry_date IS NULL OR expiry_date > effective_date)
);

-- Bảng đơn vị được phép cho từng nguyên liệu
CREATE TABLE ingredient_allowed_units (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    ingredient_id UUID NOT NULL REFERENCES ingredients(id),
    unit_id UUID NOT NULL REFERENCES units(id),
    is_primary BOOLEAN DEFAULT FALSE, -- Đơn vị chính thường dùng
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT ingredient_allowed_units_unique UNIQUE(ingredient_id, unit_id)
);

-- Bảng lịch sử thay đổi giá
CREATE TABLE price_history (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    ingredient_id UUID NOT NULL REFERENCES ingredients(id),
    unit_id UUID NOT NULL REFERENCES units(id),
    old_price DECIMAL(12,2),
    new_price DECIMAL(12,2) NOT NULL,
    change_reason VARCHAR(500),
    region VARCHAR(100) DEFAULT 'vietnam',
    changed_at TIMESTAMP DEFAULT NOW(),
    changed_by UUID -- User ID
);

-- =====================================================
-- INDEXES cho hiệu suất
-- =====================================================

-- Units indexes
CREATE INDEX idx_units_type ON units(type);
CREATE INDEX idx_units_base_unit ON units(base_unit_id);
CREATE INDEX idx_units_active ON units(is_active);

-- Ingredients indexes
CREATE INDEX idx_ingredients_category ON ingredients(category_id);
CREATE INDEX idx_ingredients_base_unit ON ingredients(base_unit_id);
CREATE INDEX idx_ingredients_active ON ingredients(is_active);
CREATE INDEX idx_ingredients_name_search ON ingredients USING gin(to_tsvector('vietnamese', name || ' ' || display_name));
CREATE INDEX idx_ingredients_alternative_names ON ingredients USING gin(alternative_names);

-- Ingredient prices indexes
CREATE INDEX idx_ingredient_prices_ingredient ON ingredient_prices(ingredient_id);
CREATE INDEX idx_ingredient_prices_unit ON ingredient_prices(unit_id);
CREATE INDEX idx_ingredient_prices_region ON ingredient_prices(region);
CREATE INDEX idx_ingredient_prices_date ON ingredient_prices(effective_date);
CREATE INDEX idx_ingredient_prices_active ON ingredient_prices(is_active);
CREATE INDEX idx_ingredient_prices_lookup ON ingredient_prices(ingredient_id, unit_id, region, effective_date) WHERE is_active = true;

-- Ingredient allowed units indexes
CREATE INDEX idx_ingredient_allowed_units_ingredient ON ingredient_allowed_units(ingredient_id);
CREATE INDEX idx_ingredient_allowed_units_primary ON ingredient_allowed_units(ingredient_id, is_primary) WHERE is_primary = true;

-- Price history indexes
CREATE INDEX idx_price_history_ingredient ON price_history(ingredient_id);
CREATE INDEX idx_price_history_date ON price_history(changed_at);

-- =====================================================
-- FUNCTIONS và TRIGGERS
-- =====================================================

-- Function để tự động cập nhật updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Triggers cho auto-update timestamp
CREATE TRIGGER update_units_updated_at BEFORE UPDATE ON units
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_ingredients_updated_at BEFORE UPDATE ON ingredients
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Function để ghi lại lịch sử thay đổi giá
CREATE OR REPLACE FUNCTION log_price_change()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'UPDATE' AND OLD.price != NEW.price THEN
        INSERT INTO price_history (ingredient_id, unit_id, old_price, new_price, region, change_reason)
        VALUES (NEW.ingredient_id, NEW.unit_id, OLD.price, NEW.price, NEW.region, 'Auto-logged price change');
    END IF;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Trigger để tự động log thay đổi giá
CREATE TRIGGER log_ingredient_price_changes AFTER UPDATE ON ingredient_prices
    FOR EACH ROW EXECUTE FUNCTION log_price_change();

-- =====================================================
-- VIEWS tiện ích
-- =====================================================

-- View để lấy giá hiện tại của nguyên liệu
CREATE VIEW current_ingredient_prices AS
SELECT DISTINCT ON (ip.ingredient_id, ip.unit_id, ip.region)
    ip.ingredient_id,
    i.name as ingredient_name,
    i.display_name as ingredient_display_name,
    ip.unit_id,
    u.name as unit_name,
    u.display_name as unit_display_name,
    ip.price,
    ip.region,
    ip.effective_date,
    ip.source,
    ip.source_name
FROM ingredient_prices ip
JOIN ingredients i ON ip.ingredient_id = i.id
JOIN units u ON ip.unit_id = u.id
WHERE ip.is_active = true
    AND i.is_active = true
    AND u.is_active = true
    AND ip.effective_date <= CURRENT_DATE
    AND (ip.expiry_date IS NULL OR ip.expiry_date > CURRENT_DATE)
ORDER BY ip.ingredient_id, ip.unit_id, ip.region, ip.effective_date DESC;

-- View để lấy thông tin đầy đủ nguyên liệu với đơn vị được phép
CREATE VIEW ingredient_units_view AS
SELECT 
    i.id as ingredient_id,
    i.name as ingredient_name,
    i.display_name as ingredient_display_name,
    i.category_id,
    ic.name as category_name,
    u.id as unit_id,
    u.name as unit_name,
    u.display_name as unit_display_name,
    u.type as unit_type,
    iau.is_primary,
    i.base_unit_id,
    bu.name as base_unit_name,
    i.base_price_per_unit
FROM ingredients i
JOIN ingredient_categories ic ON i.category_id = ic.id
JOIN ingredient_allowed_units iau ON i.id = iau.ingredient_id
JOIN units u ON iau.unit_id = u.id
JOIN units bu ON i.base_unit_id = bu.id
WHERE i.is_active = true 
    AND iau.is_active = true 
    AND u.is_active = true;

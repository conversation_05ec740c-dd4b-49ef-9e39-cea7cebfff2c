import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { useAuth } from './AuthContext';
import { Recipe } from '@/types/kitchen';
import { MealSlot } from './MealPlanningContext';
import { toast } from 'sonner';
import { IngredientPricingService } from '@/services/IngredientPricingService';
import { UnitConversionService } from '@/services/UnitConversionService';

// Consolidated Shopping List Interface
export interface ConsolidatedItem {
  name: string;
  totalQuantity: number;
  unit: string;
  category: string;
  totalEstimatedPrice: number;
  sources: {
    sessionId: string;
    sessionName: string;
    quantity: number;
    price: number;
  }[];
  priority: 'high' | 'medium' | 'low';
  isChecked: boolean;
}

export interface ConsolidatedShoppingList {
  items: ConsolidatedItem[];
  totalItems: number;
  totalEstimatedCost: number;
  categories: {
    [category: string]: ConsolidatedItem[];
  };
  sessions: {
    id: string;
    name: string;
    type: string;
    isSelected: boolean;
    itemCount: number;
    totalCost: number;
  }[];
}

// Shopping Session Interface
export interface ShoppingSession {
  id: string;
  type: 'recipe' | 'meal' | 'menu' | 'meal-package' | 'manual';
  name: string;
  description?: string;
  addedAt: string;
  items: ShoppingCartItem[];
  totalEstimatedCost: number;
  isCollapsed?: boolean;
  isSelected?: boolean; // For bulk operations
  color?: string; // Visual grouping
  icon?: string; // Session type icon
}

// Shopping Cart Item Interface
export interface ShoppingCartItem {
  id: string;
  sessionId: string; // Link to shopping session
  name: string;
  quantity: number;
  unit: string;
  category: string;
  estimatedPrice?: number;
  actualPrice?: number;
  source: {
    type: 'recipe' | 'meal' | 'menu' | 'meal-package' | 'manual';
    id: string;
    name: string;
  };
  notes?: string;
  isChecked: boolean;
  addedAt: string;
  priority?: 'high' | 'medium' | 'low'; // Shopping priority
}

// Shopping Cart Context Interface
interface ShoppingCartContextType {
  // Cart State
  sessions: ShoppingSession[];
  items: ShoppingCartItem[];
  totalItems: number;
  totalEstimatedCost: number;
  totalActualCost: number;
  isLoading: boolean;

  // Session Actions
  addSession: (session: ShoppingSession) => void;
  removeSession: (sessionId: string) => void;
  toggleSessionCollapsed: (sessionId: string) => void;
  toggleSessionSelected: (sessionId: string) => void;
  selectAllSessions: () => void;
  deselectAllSessions: () => void;
  clearCart: () => void;

  // Cart Actions
  addItem: (item: Omit<ShoppingCartItem, 'id' | 'addedAt' | 'isChecked' | 'sessionId'>, sessionId?: string) => void;
  removeItem: (itemId: string) => void;
  updateItemQuantity: (itemId: string, quantity: number) => void;
  updateItemPrice: (itemId: string, price: number) => void;
  toggleItemChecked: (itemId: string) => void;

  // Bulk Actions
  addRecipeToCart: (recipe: Recipe, servings?: number, forceAdd?: boolean) => void;
  addMealToCart: (meal: MealSlot, forceAdd?: boolean) => void;
  addMenuToCart: (menuItems: MealSlot[], forceAdd?: boolean) => void;
  addMealPackageToCart: (mealPackage: any, servings?: number, forceAdd?: boolean) => void;

  // Duplicate Check Functions
  checkRecipeExists: (recipe: Recipe) => { exists: boolean; sessionName?: string; sessionId?: string };
  checkMealExists: (meal: MealSlot) => { exists: boolean; sessionName?: string; sessionId?: string };
  checkMenuExists: (menuItems: MealSlot[]) => { exists: boolean; sessionName?: string; sessionId?: string };
  checkMealPackageExists: (mealPackage: any) => { exists: boolean; sessionName?: string; sessionId?: string };

  // Smart Features
  mergeDuplicateItems: () => void;
  optimizeShoppingList: () => void;
  generateConsolidatedShoppingList: () => ConsolidatedShoppingList;
  getSelectedSessionsItems: () => ShoppingCartItem[];

  // Persistence
  saveCart: () => Promise<void>;
  loadCart: () => Promise<void>;
}

const ShoppingCartContext = createContext<ShoppingCartContextType | undefined>(undefined);

// Shopping Cart Provider
interface ShoppingCartProviderProps {
  children: ReactNode;
}

export const ShoppingCartProvider: React.FC<ShoppingCartProviderProps> = ({ children }) => {
  const { user } = useAuth();
  const [sessions, setSessions] = useState<ShoppingSession[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  // Initialize services
  const pricingService = IngredientPricingService.getInstance();
  const unitService = UnitConversionService.getInstance();

  // Flatten all items from all sessions
  const items = sessions.flatMap(session => session.items);

  // Load cart from localStorage on mount
  useEffect(() => {
    if (user) {
      loadCartFromStorage();
    }
  }, [user]);

  // Save cart to localStorage whenever sessions change
  useEffect(() => {
    if (user && sessions.length > 0) {
      saveCartToStorage();
    }
  }, [sessions, user]);

  // Generate unique IDs
  const generateItemId = () => {
    return `item_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  };

  const generateSessionId = () => {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  };

  // Load cart from localStorage
  const loadCartFromStorage = () => {
    try {
      const savedCart = localStorage.getItem(`shopping_cart_${user?.id}`);
      if (savedCart) {
        const parsedCart = JSON.parse(savedCart);
        setSessions(parsedCart);
      }
    } catch (error) {
      console.error('Error loading cart from storage:', error);
    }
  };

  // Save cart to localStorage
  const saveCartToStorage = () => {
    try {
      localStorage.setItem(`shopping_cart_${user?.id}`, JSON.stringify(sessions));
    } catch (error) {
      console.error('Error saving cart to storage:', error);
    }
  };

  // Get session styling based on type
  const getSessionStyling = (type: string) => {
    const styles = {
      'recipe': { color: '#f97316', icon: '🍳' }, // orange
      'meal': { color: '#3b82f6', icon: '🍽️' }, // blue
      'menu': { color: '#8b5cf6', icon: '📋' }, // purple
      'meal-package': { color: '#10b981', icon: '📦' }, // green
      'manual': { color: '#6b7280', icon: '✏️' }, // gray
    };
    return styles[type as keyof typeof styles] || styles.manual;
  };

  // Create or find session
  const createOrFindSession = (type: 'recipe' | 'meal' | 'menu' | 'meal-package' | 'manual', name: string, description?: string): string => {
    // For recipes and manual items, always create new session
    if (type === 'recipe' || type === 'manual') {
      const sessionId = generateSessionId();
      const styling = getSessionStyling(type);
      const newSession: ShoppingSession = {
        id: sessionId,
        type,
        name,
        description,
        addedAt: new Date().toISOString(),
        items: [],
        totalEstimatedCost: 0,
        isCollapsed: false,
        isSelected: true, // Default to selected
        color: styling.color,
        icon: styling.icon,
      };
      setSessions(prev => [...prev, newSession]);
      return sessionId;
    }

    // For meals and menus, try to find existing session or create new one
    const existingSession = sessions.find(session =>
      session.type === type && session.name === name
    );

    if (existingSession) {
      return existingSession.id;
    } else {
      const sessionId = generateSessionId();
      const styling = getSessionStyling(type);
      const newSession: ShoppingSession = {
        id: sessionId,
        type,
        name,
        description,
        addedAt: new Date().toISOString(),
        items: [],
        totalEstimatedCost: 0,
        isCollapsed: false,
        isSelected: true, // Default to selected
        color: styling.color,
        icon: styling.icon,
      };
      setSessions(prev => [...prev, newSession]);
      return sessionId;
    }
  };

  // Add single item to cart
  const addItem = (itemData: Omit<ShoppingCartItem, 'id' | 'addedAt' | 'isChecked' | 'sessionId'>, sessionId?: string) => {
    const targetSessionId = sessionId || createOrFindSession('manual', 'Thêm thủ công');

    const newItem: ShoppingCartItem = {
      ...itemData,
      id: generateItemId(),
      sessionId: targetSessionId,
      addedAt: new Date().toISOString(),
      isChecked: false,
    };

    setSessions(prevSessions => {
      return prevSessions.map(session => {
        if (session.id === targetSessionId) {
          // Check for existing item with same name in this session
          const existingItemIndex = session.items.findIndex(
            item => item.name === newItem.name && item.unit === newItem.unit
          );

          if (existingItemIndex >= 0) {
            // Update quantity of existing item
            const updatedItems = [...session.items];
            updatedItems[existingItemIndex].quantity += newItem.quantity;
            updatedItems[existingItemIndex].estimatedPrice =
              (updatedItems[existingItemIndex].estimatedPrice || 0) + (newItem.estimatedPrice || 0);

            toast.success(`Đã cập nhật số lượng ${newItem.name}`);
            return {
              ...session,
              items: updatedItems,
              totalEstimatedCost: updatedItems.reduce((sum, item) => sum + (item.estimatedPrice || 0), 0)
            };
          } else {
            // Add new item
            const updatedItems = [...session.items, newItem];
            toast.success(`Đã thêm ${newItem.name} vào giỏ hàng`);
            return {
              ...session,
              items: updatedItems,
              totalEstimatedCost: updatedItems.reduce((sum, item) => sum + (item.estimatedPrice || 0), 0)
            };
          }
        }
        return session;
      });
    });
  };

  // Add session
  const addSession = (session: ShoppingSession) => {
    // Set sessionId for all items
    const sessionWithIds = {
      ...session,
      items: session.items.map(item => ({
        ...item,
        sessionId: session.id
      }))
    };

    setSessions(prevSessions => {
      // Check if session already exists
      const existingIndex = prevSessions.findIndex(s => s.id === session.id);
      if (existingIndex >= 0) {
        // Update existing session
        const updated = [...prevSessions];
        updated[existingIndex] = sessionWithIds;
        return updated;
      } else {
        // Add new session
        return [...prevSessions, sessionWithIds];
      }
    });

    toast.success(`Đã thêm "${session.name}" vào giỏ hàng`);
  };

  // Remove session
  const removeSession = (sessionId: string) => {
    setSessions(prevSessions => {
      const session = prevSessions.find(s => s.id === sessionId);
      if (session) {
        toast.success(`Đã xóa ${session.name} khỏi giỏ hàng`);
      }
      return prevSessions.filter(s => s.id !== sessionId);
    });
  };

  // Toggle session collapsed
  const toggleSessionCollapsed = (sessionId: string) => {
    setSessions(prevSessions =>
      prevSessions.map(session =>
        session.id === sessionId
          ? { ...session, isCollapsed: !session.isCollapsed }
          : session
      )
    );
  };

  // Toggle session selected state
  const toggleSessionSelected = (sessionId: string) => {
    setSessions(prevSessions =>
      prevSessions.map(session =>
        session.id === sessionId
          ? { ...session, isSelected: !session.isSelected }
          : session
      )
    );
  };

  // Select all sessions
  const selectAllSessions = () => {
    setSessions(prevSessions =>
      prevSessions.map(session => ({ ...session, isSelected: true }))
    );
  };

  // Deselect all sessions
  const deselectAllSessions = () => {
    setSessions(prevSessions =>
      prevSessions.map(session => ({ ...session, isSelected: false }))
    );
  };

  // Remove item from cart
  const removeItem = (itemId: string) => {
    setSessions(prevSessions => {
      return prevSessions.map(session => {
        const item = session.items.find(item => item.id === itemId);
        if (item) {
          toast.success(`Đã xóa ${item.name} khỏi giỏ hàng`);
          const updatedItems = session.items.filter(item => item.id !== itemId);
          return {
            ...session,
            items: updatedItems,
            totalEstimatedCost: updatedItems.reduce((sum, item) => sum + (item.estimatedPrice || 0), 0)
          };
        }
        return session;
      }).filter(session => session.items.length > 0); // Remove empty sessions
    });
  };

  // Update item quantity
  const updateItemQuantity = (itemId: string, quantity: number) => {
    if (quantity <= 0) {
      removeItem(itemId);
      return;
    }

    setSessions(prevSessions =>
      prevSessions.map(session => ({
        ...session,
        items: session.items.map(item =>
          item.id === itemId ? { ...item, quantity } : item
        ),
        totalEstimatedCost: session.items.reduce((sum, item) =>
          sum + (item.id === itemId ? (item.estimatedPrice || 0) * quantity / item.quantity : (item.estimatedPrice || 0)), 0
        )
      }))
    );
  };

  // Update item price
  const updateItemPrice = (itemId: string, price: number) => {
    setSessions(prevSessions =>
      prevSessions.map(session => ({
        ...session,
        items: session.items.map(item =>
          item.id === itemId ? { ...item, actualPrice: price } : item
        )
      }))
    );
  };

  // Toggle item checked status
  const toggleItemChecked = (itemId: string) => {
    setSessions(prevSessions =>
      prevSessions.map(session => ({
        ...session,
        items: session.items.map(item =>
          item.id === itemId ? { ...item, isChecked: !item.isChecked } : item
        )
      }))
    );
  };

  // Clear entire cart
  const clearCart = () => {
    setSessions([]);
    if (user) {
      localStorage.removeItem(`shopping_cart_${user.id}`);
    }
    toast.success('Đã xóa toàn bộ giỏ hàng');
  };

  // Check if recipe already exists in cart
  const checkRecipeExists = (recipe: Recipe): { exists: boolean; sessionName?: string; sessionId?: string } => {
    const existingSession = sessions.find(session =>
      session.type === 'recipe' &&
      session.items.some(item => item.source.id === recipe.id)
    );

    if (existingSession) {
      return {
        exists: true,
        sessionName: existingSession.name,
        sessionId: existingSession.id
      };
    }

    return { exists: false };
  };

  // Add recipe ingredients to cart with duplicate check
  const addRecipeToCart = (recipe: Recipe, servings: number = 1, forceAdd: boolean = false) => {
    if (!recipe.ingredients) {
      toast.error('Công thức này không có danh sách nguyên liệu');
      return;
    }

    // Check for existing recipe
    const existingCheck = checkRecipeExists(recipe);
    if (existingCheck.exists && !forceAdd) {
      toast.warning(
        `Món "${recipe.title}" đã có trong giỏ hàng (${existingCheck.sessionName}). Bạn có muốn thêm lần nữa không?`,
        {
          action: {
            label: 'Thêm lại',
            onClick: () => addRecipeToCart(recipe, servings, true)
          },
          duration: 5000,
        }
      );
      return;
    }

    const ingredients = Array.isArray(recipe.ingredients)
      ? recipe.ingredients
      : JSON.parse(recipe.ingredients || '[]');

    // Create a new session for this recipe
    const sessionId = createOrFindSession(
      'recipe',
      recipe.title,
      `${servings} khẩu phần • ${ingredients.length} nguyên liệu`
    );

    ingredients.forEach((ingredient: string) => {
      // Parse ingredient string to extract name, quantity, unit
      const parsed = parseIngredient(ingredient);

      addItem({
        name: parsed.name,
        quantity: parsed.quantity * servings,
        unit: parsed.unit,
        category: categorizeIngredient(parsed.name),
        source: {
          type: 'recipe',
          id: recipe.id,
          name: recipe.title,
        },
        estimatedPrice: estimateIngredientPrice(parsed.name, parsed.quantity * servings, parsed.unit),
      }, sessionId);
    });

    if (forceAdd && existingCheck.exists) {
      toast.success(`Đã thêm lại nguyên liệu từ "${recipe.title}" vào giỏ hàng`);
    } else {
      toast.success(`Đã thêm nguyên liệu từ "${recipe.title}" vào giỏ hàng`);
    }
  };

  // Check if meal already exists in cart
  const checkMealExists = (meal: MealSlot): { exists: boolean; sessionName?: string; sessionId?: string } => {
    const mealTypeLabels = {
      breakfast: 'Bữa sáng',
      lunch: 'Bữa trưa',
      dinner: 'Bữa tối',
      snack: 'Bữa phụ'
    };

    const expectedSessionName = `${mealTypeLabels[meal.mealType]} - ${meal.recipe?.title}`;
    const existingSession = sessions.find(session =>
      session.type === 'meal' &&
      session.name === expectedSessionName &&
      session.items.some(item => item.source.id === meal.id)
    );

    if (existingSession) {
      return {
        exists: true,
        sessionName: existingSession.name,
        sessionId: existingSession.id
      };
    }

    return { exists: false };
  };

  // Add meal ingredients to cart with duplicate check
  const addMealToCart = (meal: MealSlot, forceAdd: boolean = false) => {
    if (!meal.recipe) {
      toast.error('Bữa ăn này không có công thức');
      return;
    }

    // Check for existing meal
    const existingCheck = checkMealExists(meal);
    if (existingCheck.exists && !forceAdd) {
      toast.warning(
        `Bữa ăn "${existingCheck.sessionName}" đã có trong giỏ hàng. Bạn có muốn thêm lần nữa không?`,
        {
          action: {
            label: 'Thêm lại',
            onClick: () => addMealToCart(meal, true)
          },
          duration: 5000,
        }
      );
      return;
    }

    const mealTypeLabels = {
      breakfast: 'Bữa sáng',
      lunch: 'Bữa trưa',
      dinner: 'Bữa tối',
      snack: 'Bữa phụ'
    };

    const sessionName = `${mealTypeLabels[meal.mealType]} - ${meal.recipe.title}`;
    const sessionId = createOrFindSession('meal', sessionName, `${meal.date}`);

    const ingredients = Array.isArray(meal.recipe.ingredients)
      ? meal.recipe.ingredients
      : JSON.parse(meal.recipe.ingredients || '[]');

    ingredients.forEach((ingredient: string) => {
      const parsed = parseIngredient(ingredient);

      addItem({
        name: parsed.name,
        quantity: parsed.quantity,
        unit: parsed.unit,
        category: categorizeIngredient(parsed.name),
        source: {
          type: 'meal',
          id: meal.id,
          name: sessionName,
        },
        estimatedPrice: estimateIngredientPrice(parsed.name, parsed.quantity, parsed.unit),
      }, sessionId);
    });

    if (forceAdd && existingCheck.exists) {
      toast.success(`Đã thêm lại nguyên liệu từ "${sessionName}" vào giỏ hàng`);
    } else {
      toast.success(`Đã thêm nguyên liệu từ "${sessionName}" vào giỏ hàng`);
    }
  };

  // Check if menu already exists in cart
  const checkMenuExists = (menuItems: MealSlot[]): { exists: boolean; sessionName?: string; sessionId?: string } => {
    const menuDate = menuItems[0]?.date || new Date().toISOString().split('T')[0];
    const expectedSessionName = `Thực đơn ${menuDate}`;

    const existingSession = sessions.find(session =>
      session.type === 'menu' &&
      session.name === expectedSessionName
    );

    if (existingSession) {
      return {
        exists: true,
        sessionName: existingSession.name,
        sessionId: existingSession.id
      };
    }

    return { exists: false };
  };

  // Add menu ingredients to cart with duplicate check
  const addMenuToCart = (menuItems: MealSlot[], forceAdd: boolean = false) => {
    if (!menuItems || menuItems.length === 0) {
      toast.error('Thực đơn không có món ăn nào');
      return;
    }

    // Check for existing menu
    const existingCheck = checkMenuExists(menuItems);
    if (existingCheck.exists && !forceAdd) {
      toast.warning(
        `"${existingCheck.sessionName}" đã có trong giỏ hàng. Bạn có muốn thêm lần nữa không?`,
        {
          action: {
            label: 'Thêm lại',
            onClick: () => addMenuToCart(menuItems, true)
          },
          duration: 5000,
        }
      );
      return;
    }

    const menuDate = menuItems[0]?.date || new Date().toISOString().split('T')[0];
    const sessionName = `Thực đơn ${menuDate}`;
    const sessionId = createOrFindSession(
      'menu',
      sessionName,
      `${menuItems.length} bữa ăn • ${menuItems.reduce((total, meal) => {
        if (!meal.recipe?.ingredients) return total;
        const ingredients = Array.isArray(meal.recipe.ingredients)
          ? meal.recipe.ingredients
          : JSON.parse(meal.recipe.ingredients || '[]');
        return total + ingredients.length;
      }, 0)} nguyên liệu`
    );

    menuItems.forEach(meal => {
      if (meal.recipe) {
        const ingredients = Array.isArray(meal.recipe.ingredients)
          ? meal.recipe.ingredients
          : JSON.parse(meal.recipe.ingredients || '[]');

        ingredients.forEach((ingredient: string) => {
          const parsed = parseIngredient(ingredient);

          addItem({
            name: parsed.name,
            quantity: parsed.quantity,
            unit: parsed.unit,
            category: categorizeIngredient(parsed.name),
            source: {
              type: 'menu',
              id: meal.id,
              name: `${sessionName} - ${meal.recipe.title}`,
            },
            estimatedPrice: estimateIngredientPrice(parsed.name, parsed.quantity, parsed.unit),
          }, sessionId);
        });
      }
    });

    if (forceAdd && existingCheck.exists) {
      toast.success(`Đã thêm lại nguyên liệu từ "${sessionName}" vào giỏ hàng`);
    } else {
      toast.success(`Đã thêm nguyên liệu từ "${sessionName}" vào giỏ hàng`);
    }
  };

  // Check if meal package already exists in cart
  const checkMealPackageExists = (mealPackage: any): { exists: boolean; sessionName?: string; sessionId?: string } => {
    const existingSession = sessions.find(session =>
      session.type === 'meal-package' &&
      session.name === mealPackage.title &&
      session.items.some(item => item.source.id === mealPackage.id)
    );

    if (existingSession) {
      return {
        exists: true,
        sessionName: existingSession.name,
        sessionId: existingSession.id
      };
    }

    return { exists: false };
  };

  // Add meal package ingredients to cart with duplicate check
  const addMealPackageToCart = (mealPackage: any, servings: number = 1, forceAdd: boolean = false) => {
    if (!mealPackage.sampleMeals || mealPackage.sampleMeals.length === 0) {
      toast.error('Thực đơn này không có danh sách món ăn');
      return;
    }

    // Check for existing meal package
    const existingCheck = checkMealPackageExists(mealPackage);
    if (existingCheck.exists && !forceAdd) {
      toast.warning(
        `Gói thực đơn "${mealPackage.title}" đã có trong giỏ hàng. Bạn có muốn thêm lần nữa không?`,
        {
          action: {
            label: 'Thêm lại',
            onClick: () => addMealPackageToCart(mealPackage, servings, true)
          },
          duration: 5000,
        }
      );
      return;
    }

    const sessionId = createOrFindSession(
      'meal-package',
      mealPackage.title,
      `${mealPackage.duration} • ${mealPackage.servings} • ${mealPackage.sampleMeals.length} món`
    );

    // For meal packages, we'll create sample ingredients based on the meal names
    mealPackage.sampleMeals.forEach((mealName: string, index: number) => {
      // Generate sample ingredients for each meal
      const sampleIngredients = generateSampleIngredientsForMeal(mealName);

      sampleIngredients.forEach((ingredient: string) => {
        const parsed = parseIngredient(ingredient);

        addItem({
          name: parsed.name,
          quantity: parsed.quantity * servings,
          unit: parsed.unit,
          category: categorizeIngredient(parsed.name),
          source: {
            type: 'meal-package',
            id: mealPackage.id,
            name: `${mealPackage.title} - ${mealName}`,
          },
          estimatedPrice: estimateIngredientPrice(parsed.name, parsed.quantity * servings, parsed.unit),
        }, sessionId);
      });
    });

    if (forceAdd && existingCheck.exists) {
      toast.success(`Đã thêm lại nguyên liệu từ "${mealPackage.title}" vào giỏ hàng`);
    } else {
      toast.success(`Đã thêm nguyên liệu từ "${mealPackage.title}" vào giỏ hàng`);
    }
  };

  // Get items from selected sessions only
  const getSelectedSessionsItems = (): ShoppingCartItem[] => {
    return sessions
      .filter(session => session.isSelected)
      .flatMap(session => session.items);
  };

  // Generate consolidated shopping list from selected sessions
  const generateConsolidatedShoppingList = (): ConsolidatedShoppingList => {
    const selectedSessions = sessions.filter(session => session.isSelected);
    const selectedItems = selectedSessions.flatMap(session => session.items);

    // Group items by name and unit
    const itemGroups: { [key: string]: ConsolidatedItem } = {};

    selectedItems.forEach(item => {
      const key = `${item.name.toLowerCase()}_${item.unit.toLowerCase()}`;

      if (itemGroups[key]) {
        // Merge with existing item
        itemGroups[key].totalQuantity += item.quantity;
        itemGroups[key].totalEstimatedPrice += item.estimatedPrice || 0;
        itemGroups[key].sources.push({
          sessionId: item.sessionId,
          sessionName: sessions.find(s => s.id === item.sessionId)?.name || 'Unknown',
          quantity: item.quantity,
          price: item.estimatedPrice || 0
        });
      } else {
        // Create new consolidated item
        itemGroups[key] = {
          name: item.name,
          totalQuantity: item.quantity,
          unit: item.unit,
          category: item.category,
          totalEstimatedPrice: item.estimatedPrice || 0,
          sources: [{
            sessionId: item.sessionId,
            sessionName: sessions.find(s => s.id === item.sessionId)?.name || 'Unknown',
            quantity: item.quantity,
            price: item.estimatedPrice || 0
          }],
          priority: item.priority || 'medium',
          isChecked: false
        };
      }
    });

    const consolidatedItems = Object.values(itemGroups);

    // Group by category
    const categories: { [category: string]: ConsolidatedItem[] } = {};
    consolidatedItems.forEach(item => {
      if (!categories[item.category]) {
        categories[item.category] = [];
      }
      categories[item.category].push(item);
    });

    // Sort categories by priority
    Object.keys(categories).forEach(category => {
      categories[category].sort((a, b) => {
        const priorityOrder = { high: 3, medium: 2, low: 1 };
        return priorityOrder[b.priority] - priorityOrder[a.priority];
      });
    });

    return {
      items: consolidatedItems,
      totalItems: consolidatedItems.reduce((sum, item) => sum + item.totalQuantity, 0),
      totalEstimatedCost: consolidatedItems.reduce((sum, item) => sum + item.totalEstimatedPrice, 0),
      categories,
      sessions: selectedSessions.map(session => ({
        id: session.id,
        name: session.name,
        type: session.type,
        isSelected: session.isSelected || false,
        itemCount: session.items.length,
        totalCost: session.totalEstimatedCost
      }))
    };
  };

  // Helper function to generate sample ingredients for a meal name
  const generateSampleIngredientsForMeal = (mealName: string): string[] => {
    // This is a simplified version - in a real app, you'd have a database of recipes
    const commonIngredients = [
      '200g thịt',
      '1 củ hành',
      '2 tép tỏi',
      '1 muỗng canh dầu ăn',
      '1 muỗng canh nước mắm',
      '100g rau xanh',
      '200g cơm'
    ];

    // Return 3-5 random ingredients for each meal
    const numIngredients = Math.floor(Math.random() * 3) + 3;
    return commonIngredients.slice(0, numIngredients);
  };

  // Helper function to parse ingredient string using the unit service
  const parseIngredient = (ingredient: string) => {
    try {
      return unitService.parseIngredientString(ingredient);
    } catch (error) {
      console.error('Error parsing ingredient string:', error);
      // Fallback to simple parsing
      return parseIngredientFallback(ingredient);
    }
  };

  // Fallback parsing function (simplified version of old logic)
  const parseIngredientFallback = (ingredient: string) => {
    const cleanIngredient = ingredient.trim();

    // Simple patterns
    const pattern1 = cleanIngredient.match(/^(\d+(?:\.\d+)?)\s*(g|kg|ml|l|tbsp|tsp|cup|muỗng|thìa|cốc|lít|gram|kilogram)?\s+(.+)$/i);
    if (pattern1) {
      return {
        quantity: parseFloat(pattern1[1]),
        unit: pattern1[2] || 'phần',
        name: pattern1[3].trim(),
      };
    }

    const pattern2 = cleanIngredient.match(/^(\d+(?:\/\d+)?)\s*(quả|củ|cây|lá|miếng|khoanh|nhánh|bó|gói|hộp|lon|chai)?\s+(.+)$/i);
    if (pattern2) {
      const fractionMatch = pattern2[1].match(/^(\d+)\/(\d+)$/);
      const quantity = fractionMatch ? parseFloat(fractionMatch[1]) / parseFloat(fractionMatch[2]) : parseFloat(pattern2[1]);
      return {
        quantity,
        unit: pattern2[2] || 'phần',
        name: pattern2[3].trim(),
      };
    }

    // Default
    return {
      quantity: 1,
      unit: 'phần',
      name: cleanIngredient,
    };
  };

  // Helper function to categorize ingredients
  const categorizeIngredient = (name: string): string => {
    const lowerName = name.toLowerCase();

    const categories = {
      'Thịt & Hải sản': [
        'thịt', 'gà', 'heo', 'bò', 'vịt', 'ngan', 'cừu', 'dê',
        'cá', 'tôm', 'cua', 'mực', 'bạch tuộc', 'sò', 'nghêu', 'ốc',
        'hàu', 'ghẹ', 'tôm hùm', 'cá hồi', 'cá ngừ', 'thịt ba chỉ',
        'thịt nạc', 'sườn', 'xương', 'gan', 'tim', 'lòng'
      ],
      'Rau củ quả': [
        'rau', 'củ', 'cà', 'khoai', 'bắp cải', 'cải', 'xà lách',
        'cà rót', 'cà chua', 'ớt', 'chanh', 'cam', 'táo', 'chuối',
        'dưa', 'bí', 'su hào', 'củ cải', 'cà rốt', 'khoai tây',
        'khoai lang', 'măng', 'nấm', 'đậu', 'đỗ', 'rau muống',
        'rau cần', 'rau thơm', 'húng', 'ngò', 'kinh giới'
      ],
      'Gia vị & Condiments': [
        'muối', 'đường', 'tiêu', 'tỏi', 'hành', 'gừng', 'sả',
        'nước mắm', 'tương ớt', 'dầu ăn', 'dầu mè', 'giấm',
        'mật ong', 'đậu phộng', 'mè', 'vừng', 'quế', 'hồi',
        'đinh hương', 'thảo quả', 'hạt nêm', 'bột ngọt',
        'mayonnaise', 'tương', 'miso', 'wasabi'
      ],
      'Ngũ cốc & Tinh bột': [
        'gạo', 'mì', 'bánh mì', 'bột', 'bún', 'phở', 'miến',
        'bánh tráng', 'bánh phở', 'yến mạch', 'lúa mì',
        'bột mì', 'bột gạo', 'bột năng', 'bột sắn', 'bánh quy'
      ],
      'Sữa & Trứng': [
        'sữa', 'trứng', 'phô mai', 'bơ', 'kem', 'yaourt', 'yogurt',
        'sữa chua', 'sữa đặc', 'sữa tươi', 'whipping cream',
        'cream cheese', 'mozzarella', 'cheddar'
      ],
      'Đồ khô & Hạt': [
        'đậu phộng', 'hạnh nhân', 'óc chó', 'điều', 'hạt dẻ',
        'nho khô', 'mơ khô', 'khế khô', 'tôm khô', 'mực khô',
        'nấm khô', 'hành khô', 'tỏi khô'
      ],
      'Đồ uống': [
        'nước', 'trà', 'cà phê', 'bia', 'rượu', 'nước ngọt',
        'nước ép', 'sinh tố', 'soda', 'nước khoáng'
      ]
    };

    for (const [category, keywords] of Object.entries(categories)) {
      if (keywords.some(keyword => lowerName.includes(keyword))) {
        return category;
      }
    }
    return 'Khác';
  };

  // New improved price estimation using the pricing service
  const estimateIngredientPrice = (name: string, quantity: number, unit: string = 'phần'): number => {
    try {
      const result = pricingService.calculatePrice(name, quantity, unit);

      if (result.success) {
        return Math.round(result.totalPrice);
      } else {
        console.warn(`Price calculation failed for ${name}: ${result.error}`);
        // Fallback to simple estimation
        return estimateIngredientPriceFallback(name, quantity, unit);
      }
    } catch (error) {
      console.error('Error in price calculation:', error);
      return estimateIngredientPriceFallback(name, quantity, unit);
    }
  };

  // Fallback price estimation (simplified version of old logic)
  const estimateIngredientPriceFallback = (name: string, quantity: number, unit: string): number => {
    const lowerName = name.toLowerCase();
    const lowerUnit = unit.toLowerCase();

    // Simple price mapping (per base unit)
    const simplePrices: { [key: string]: number } = {
      'thịt': 200, // 200đ per gram
      'cá': 120,
      'tôm': 250,
      'rau': 20,
      'củ': 15,
      'gạo': 25,
      'trứng': 4000, // per piece
      'sữa': 30, // per ml
      'dầu': 50, // per ml
      'muối': 10, // per gram
      'đường': 20, // per gram
    };

    // Find matching category
    for (const [keyword, pricePerUnit] of Object.entries(simplePrices)) {
      if (lowerName.includes(keyword)) {
        // For count-based items
        if (lowerUnit.includes('củ') || lowerUnit.includes('quả') || lowerUnit.includes('cái')) {
          return Math.round(pricePerUnit * quantity);
        }

        // For weight/volume - assume gram/ml
        let multiplier = 1;
        if (lowerUnit.includes('kg')) multiplier = 1000;
        else if (lowerUnit.includes('lạng')) multiplier = 100;
        else if (lowerUnit.includes('lít')) multiplier = 1000;

        return Math.round(pricePerUnit * quantity * multiplier);
      }
    }

    // Default fallback
    return Math.round(quantity * 1000); // 1000đ per unit
  };

  // Merge duplicate items
  const mergeDuplicateItems = () => {
    const mergedItems: ShoppingCartItem[] = [];
    const itemMap = new Map<string, ShoppingCartItem>();

    items.forEach(item => {
      const key = `${item.name}_${item.unit}`;
      if (itemMap.has(key)) {
        const existing = itemMap.get(key)!;
        existing.quantity += item.quantity;
        existing.estimatedPrice = (existing.estimatedPrice || 0) + (item.estimatedPrice || 0);
      } else {
        itemMap.set(key, { ...item });
      }
    });

    setItems(Array.from(itemMap.values()));
    toast.success('Đã gộp các mục trùng lặp');
  };

  // Optimize shopping list (placeholder)
  const optimizeShoppingList = () => {
    mergeDuplicateItems();
    // Additional optimization logic can be added here
    toast.success('Đã tối ưu hóa danh sách mua sắm');
  };

  // Persistence functions (placeholder for future backend integration)
  const saveCart = async () => {
    setIsLoading(true);
    try {
      // TODO: Implement backend save
      saveCartToStorage();
      toast.success('Đã lưu giỏ hàng');
    } catch (error) {
      toast.error('Không thể lưu giỏ hàng');
    } finally {
      setIsLoading(false);
    }
  };

  const loadCart = async () => {
    setIsLoading(true);
    try {
      // TODO: Implement backend load
      loadCartFromStorage();
    } catch (error) {
      toast.error('Không thể tải giỏ hàng');
    } finally {
      setIsLoading(false);
    }
  };

  // Calculate totals
  const totalItems = items.reduce((sum, item) => sum + item.quantity, 0);
  const totalEstimatedCost = items.reduce((sum, item) => sum + (item.estimatedPrice || 0), 0);
  const totalActualCost = items.reduce((sum, item) => sum + (item.actualPrice || item.estimatedPrice || 0), 0);

  const value: ShoppingCartContextType = {
    // State
    sessions,
    items,
    totalItems,
    totalEstimatedCost,
    totalActualCost,
    isLoading,

    // Session Actions
    addSession,
    removeSession,
    toggleSessionCollapsed,
    toggleSessionSelected,
    selectAllSessions,
    deselectAllSessions,
    clearCart,

    // Actions
    addItem,
    removeItem,
    updateItemQuantity,
    updateItemPrice,
    toggleItemChecked,

    // Bulk Actions
    addRecipeToCart,
    addMealToCart,
    addMenuToCart,
    addMealPackageToCart,

    // Duplicate Check Functions
    checkRecipeExists,
    checkMealExists,
    checkMenuExists,
    checkMealPackageExists,

    // Smart Features
    mergeDuplicateItems,
    optimizeShoppingList,
    generateConsolidatedShoppingList,
    getSelectedSessionsItems,

    // Persistence
    saveCart,
    loadCart,
  };

  return (
    <ShoppingCartContext.Provider value={value}>
      {children}
    </ShoppingCartContext.Provider>
  );
};

// Hook to use Shopping Cart Context
export const useShoppingCart = () => {
  const context = useContext(ShoppingCartContext);
  if (context === undefined) {
    throw new Error('useShoppingCart must be used within a ShoppingCartProvider');
  }
  return context;
};

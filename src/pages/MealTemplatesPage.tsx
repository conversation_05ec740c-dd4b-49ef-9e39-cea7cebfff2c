import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ArrowLeft, ChefHat, Lightbulb, Target, Clock } from 'lucide-react';
import { Link } from 'react-router-dom';
import Header from '@/components/Header';
import { MealTemplateExplorer } from '@/components/meal-templates/MealTemplateExplorer';
import { MealTemplate } from '@/data/vietnameseMealTemplates';
import { toast } from 'sonner';

const MealTemplatesPage: React.FC = () => {
  const [selectedTemplate, setSelectedTemplate] = useState<MealTemplate | null>(null);

  const handleSelectTemplate = (template: MealTemplate) => {
    setSelectedTemplate(template);
    toast.success(`<PERSON><PERSON> chọn thực đơn "${template.name}"`);
    // TODO: Navigate to meal planner with this template
    console.log('Selected template:', template);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      <div className="max-w-7xl mx-auto px-4 py-8">
        {/* Page Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center space-x-4">
            <Button variant="ghost" size="icon" asChild>
              <Link to="/">
                <ArrowLeft className="h-5 w-5" />
              </Link>
            </Button>
            <div>
              <h1 className="text-3xl font-bold text-gray-900 flex items-center">
                <ChefHat className="h-8 w-8 mr-3 text-orange-500" />
                Thực Đơn Mẫu "Mâm Cơm Việt"
              </h1>
              <p className="text-gray-600 mt-1">
                Chọn thực đơn phù hợp với mục tiêu và hoàn cảnh của bạn
              </p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
              <Target className="h-3 w-3 mr-1" />
              4 thực đơn mẫu
            </Badge>
          </div>
        </div>

        {/* Value Proposition */}
        <Card className="mb-8 border-blue-200 bg-blue-50">
          <CardContent className="p-6">
            <div className="flex items-start gap-4">
              <Lightbulb className="h-6 w-6 text-blue-600 mt-1" />
              <div>
                <h3 className="font-semibold text-blue-800 mb-2">💡 Trải nghiệm "Ra giá trị trong 5 phút"</h3>
                <div className="text-blue-700 space-y-1">
                  <p>• <strong>Bước 1:</strong> Chọn thực đơn mẫu phù hợp với mục tiêu (nhanh gọn, tiết kiệm, healthy...)</p>
                  <p>• <strong>Bước 2:</strong> Nhấn "Áp dụng thực đơn" để tự động tạo kế hoạch ăn uống 7 ngày</p>
                  <p>• <strong>Bước 3:</strong> Xem danh sách đi chợ được tạo tự động, gộp nguyên liệu và quy đổi đơn vị</p>
                  <p>• <strong>Kết quả:</strong> Có ngay thực đơn tuần hoàn chỉnh với list mua sắm chi tiết!</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Quick Stats */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8">
          <Card className="text-center p-4">
            <div className="text-2xl font-bold text-blue-600">⚡</div>
            <div className="text-sm font-medium text-gray-900">Nhanh Gọn</div>
            <div className="text-xs text-gray-600">30 phút/bữa</div>
          </Card>
          <Card className="text-center p-4">
            <div className="text-2xl font-bold text-green-600">💰</div>
            <div className="text-sm font-medium text-gray-900">Tiết Kiệm</div>
            <div className="text-xs text-gray-600">150k/tuần</div>
          </Card>
          <Card className="text-center p-4">
            <div className="text-2xl font-bold text-emerald-600">🥗</div>
            <div className="text-sm font-medium text-gray-900">Healthy</div>
            <div className="text-xs text-gray-600">Ít dầu mỡ</div>
          </Card>
          <Card className="text-center p-4">
            <div className="text-2xl font-bold text-orange-600">⭐</div>
            <div className="text-sm font-medium text-gray-900">Đặc Biệt</div>
            <div className="text-xs text-gray-600">Cuối tuần</div>
          </Card>
        </div>

        {/* Main Explorer */}
        <MealTemplateExplorer onSelectTemplate={handleSelectTemplate} />

        {/* How it works */}
        <Card className="mt-8">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Clock className="h-5 w-5 text-blue-500" />
              Cách Thức Hoạt Động
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="text-center">
                <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
                  <span className="text-xl font-bold text-blue-600">1</span>
                </div>
                <h4 className="font-semibold text-gray-900 mb-2">Chọn Thực Đơn</h4>
                <p className="text-sm text-gray-600">
                  Duyệt qua các thực đơn mẫu được thiết kế theo mục tiêu cụ thể: nhanh gọn, tiết kiệm, healthy, đặc biệt
                </p>
              </div>
              <div className="text-center">
                <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3">
                  <span className="text-xl font-bold text-green-600">2</span>
                </div>
                <h4 className="font-semibold text-gray-900 mb-2">Áp Dụng Tự Động</h4>
                <p className="text-sm text-gray-600">
                  Hệ thống tự động tạo kế hoạch ăn uống 7 ngày với các món ăn được sắp xếp hợp lý cho sáng-trưa-tối
                </p>
              </div>
              <div className="text-center">
                <div className="w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-3">
                  <span className="text-xl font-bold text-orange-600">3</span>
                </div>
                <h4 className="font-semibold text-gray-900 mb-2">Danh Sách Mua Sắm</h4>
                <p className="text-sm text-gray-600">
                  Nhận ngay danh sách đi chợ chi tiết với nguyên liệu được gộp, quy đổi đơn vị và phân loại theo quầy
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Features */}
        <Card className="mt-8">
          <CardHeader>
            <CardTitle>Đặc Điểm Nổi Bật</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-semibold text-gray-900 mb-3">🇻🇳 Bản địa hóa sâu:</h4>
                <ul className="space-y-2 text-sm text-gray-600">
                  <li>• Thực đơn "mâm cơm" truyền thống Việt Nam</li>
                  <li>• Nguyên liệu và đơn vị đo lường quen thuộc</li>
                  <li>• Phân chia theo vùng miền và dịp đặc biệt</li>
                  <li>• Cân bằng dinh dưỡng theo khẩu vị Việt</li>
                </ul>
              </div>
              <div>
                <h4 className="font-semibold text-gray-900 mb-3">🎯 Thực dụng cao:</h4>
                <ul className="space-y-2 text-sm text-gray-600">
                  <li>• Ước tính chi phí và thời gian thực tế</li>
                  <li>• Danh sách mua sắm được tối ưu hóa</li>
                  <li>• Mẹo nấu ăn và thay thế nguyên liệu</li>
                  <li>• Phù hợp với thiết bị bếp gia đình Việt</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default MealTemplatesPage;

import React, { useState } from 'react';
import { Heart, Clock, Users, Star, ChefHat, Settings } from 'lucide-react';

// Simple Button Component for testing
const TestButton: React.FC<{
  variant?: 'primary' | 'secondary' | 'ghost';
  size?: 'sm' | 'md' | 'lg';
  children: React.ReactNode;
  onClick?: () => void;
  loading?: boolean;
}> = ({ 
  variant = 'primary', 
  size = 'md', 
  children, 
  onClick,
  loading = false 
}) => {
  const baseClasses = 'inline-flex items-center justify-center font-medium rounded-lg transition-all duration-150';
  
  const variantClasses = {
    primary: 'bg-orange-500 text-white hover:bg-orange-600 hover:-translate-y-0.5 hover:shadow-lg',
    secondary: 'bg-gray-100 text-gray-900 border border-gray-300 hover:bg-gray-200',
    ghost: 'bg-transparent text-gray-700 hover:bg-gray-100'
  };
  
  const sizeClasses = {
    sm: 'px-3 py-1.5 text-sm',
    md: 'px-4 py-2 text-sm',
    lg: 'px-6 py-3 text-base'
  };
  
  return (
    <button
      className={`${baseClasses} ${variantClasses[variant]} ${sizeClasses[size]} ${loading ? 'opacity-70 cursor-wait' : ''}`}
      onClick={onClick}
      disabled={loading}
    >
      {loading && (
        <svg className="animate-spin -ml-1 mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24">
          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"/>
          <path className="opacity-75" fill="currentColor" d="m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"/>
        </svg>
      )}
      {children}
    </button>
  );
};

// Simple Card Component for testing
const TestCard: React.FC<{
  variant?: 'default' | 'elevated' | 'outlined';
  children: React.ReactNode;
  interactive?: boolean;
  onClick?: () => void;
}> = ({ 
  variant = 'default', 
  children, 
  interactive = false,
  onClick 
}) => {
  const baseClasses = 'rounded-xl overflow-hidden transition-all duration-200';
  
  const variantClasses = {
    default: 'bg-white border border-gray-200 shadow-sm hover:shadow-md hover:-translate-y-1',
    elevated: 'bg-white shadow-lg hover:shadow-xl hover:-translate-y-2',
    outlined: 'bg-white border-2 border-gray-200 hover:border-orange-300 hover:shadow-sm'
  };
  
  const interactiveClasses = interactive ? 'cursor-pointer focus:outline-none focus:ring-2 focus:ring-orange-500/20' : '';
  
  return (
    <div
      className={`${baseClasses} ${variantClasses[variant]} ${interactiveClasses}`}
      onClick={onClick}
      tabIndex={interactive ? 0 : undefined}
    >
      {children}
    </div>
  );
};

const SimpleDesignDemo: React.FC = () => {
  const [liked, setLiked] = useState<Set<number>>(new Set());
  const [loading, setLoading] = useState<number | null>(null);

  const toggleLike = (id: number) => {
    setLoading(id);
    setTimeout(() => {
      setLiked(prev => {
        const newSet = new Set(prev);
        if (newSet.has(id)) {
          newSet.delete(id);
        } else {
          newSet.add(id);
        }
        return newSet;
      });
      setLoading(null);
    }, 1000);
  };

  const recipes = [
    {
      id: 1,
      title: "Creamy Pasta Carbonara",
      description: "Classic Italian pasta with eggs, cheese, and pancetta",
      image: "https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=400&h=300&fit=crop",
      time: "25 min",
      servings: 4,
      rating: 4.8,
    },
    {
      id: 2,
      title: "Grilled Salmon Teriyaki",
      description: "Fresh salmon glazed with homemade teriyaki sauce",
      image: "https://images.unsplash.com/photo-1467003909585-2f8a72700288?w=400&h=300&fit=crop",
      time: "20 min",
      servings: 2,
      rating: 4.6,
    },
    {
      id: 3,
      title: "Vegetable Stir Fry",
      description: "Colorful mix of fresh vegetables in savory sauce",
      image: "https://images.unsplash.com/photo-1512621776951-a57141f2eefd?w=400&h=300&fit=crop",
      time: "15 min",
      servings: 3,
      rating: 4.4,
    },
  ];

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="container mx-auto px-4 max-w-6xl">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-2">
            Simple Design System Demo
          </h1>
          <p className="text-lg text-gray-600">
            Testing enhanced UI components
          </p>
        </div>

        {/* Button Showcase */}
        <TestCard variant="default">
          <div className="p-6">
            <h2 className="text-2xl font-semibold mb-6">Enhanced Buttons</h2>
            
            <div className="mb-6">
              <h3 className="text-lg font-medium mb-3">Button Variants</h3>
              <div className="flex flex-wrap gap-3">
                <TestButton variant="primary">Primary Button</TestButton>
                <TestButton variant="secondary">Secondary Button</TestButton>
                <TestButton variant="ghost">Ghost Button</TestButton>
              </div>
            </div>

            <div className="mb-6">
              <h3 className="text-lg font-medium mb-3">Button Sizes</h3>
              <div className="flex flex-wrap items-center gap-3">
                <TestButton size="sm">Small</TestButton>
                <TestButton size="md">Medium</TestButton>
                <TestButton size="lg">Large</TestButton>
              </div>
            </div>

            <div className="mb-6">
              <h3 className="text-lg font-medium mb-3">Button States</h3>
              <div className="flex flex-wrap gap-3">
                <TestButton loading>Loading Button</TestButton>
                <TestButton variant="secondary">
                  <ChefHat className="w-4 h-4 mr-2" />
                  With Icon
                </TestButton>
              </div>
            </div>
          </div>
        </TestCard>

        {/* Card Showcase */}
        <div className="mt-8">
          <div className="mb-6">
            <h2 className="text-2xl font-semibold mb-4">Enhanced Cards</h2>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
              <TestCard variant="default">
                <div className="p-4">
                  <h4 className="font-medium mb-2">Default Card</h4>
                  <p className="text-sm text-gray-600">Standard card styling</p>
                </div>
              </TestCard>
              
              <TestCard variant="elevated">
                <div className="p-4">
                  <h4 className="font-medium mb-2">Elevated Card</h4>
                  <p className="text-sm text-gray-600">Enhanced shadow</p>
                </div>
              </TestCard>
              
              <TestCard variant="outlined">
                <div className="p-4">
                  <h4 className="font-medium mb-2">Outlined Card</h4>
                  <p className="text-sm text-gray-600">Prominent border</p>
                </div>
              </TestCard>
            </div>
          </div>

          {/* Recipe Cards */}
          <div>
            <h3 className="text-xl font-semibold mb-4">Recipe Collection</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {recipes.map((recipe) => (
                <TestCard key={recipe.id} variant="default" interactive>
                  <img
                    src={recipe.image}
                    alt={recipe.title}
                    className="w-full h-48 object-cover"
                  />
                  
                  <div className="p-4">
                    <div className="flex items-start justify-between mb-2">
                      <h4 className="font-semibold text-lg truncate flex-1">
                        {recipe.title}
                      </h4>
                      <button
                        onClick={() => toggleLike(recipe.id)}
                        disabled={loading === recipe.id}
                        className="ml-2 p-1 rounded-full hover:bg-gray-100 transition-colors"
                      >
                        {loading === recipe.id ? (
                          <div className="w-5 h-5 border-2 border-gray-300 border-t-red-500 rounded-full animate-spin" />
                        ) : (
                          <Heart 
                            className={`w-5 h-5 transition-colors ${
                              liked.has(recipe.id) 
                                ? 'fill-red-500 text-red-500' 
                                : 'text-gray-400 hover:text-red-500'
                            }`} 
                          />
                        )}
                      </button>
                    </div>
                    
                    <p className="text-sm text-gray-600 mb-3 line-clamp-2">
                      {recipe.description}
                    </p>

                    <div className="flex items-center gap-4 text-sm text-gray-600 mb-4">
                      <div className="flex items-center gap-1">
                        <Clock className="w-4 h-4" />
                        <span>{recipe.time}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <Users className="w-4 h-4" />
                        <span>{recipe.servings}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <Star className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                        <span>{recipe.rating}</span>
                      </div>
                    </div>

                    <div className="flex gap-2">
                      <TestButton variant="ghost" size="sm">
                        <Settings className="w-4 h-4" />
                      </TestButton>
                      <TestButton size="sm">
                        <ChefHat className="w-4 h-4 mr-1" />
                        Cook
                      </TestButton>
                    </div>
                  </div>
                </TestCard>
              ))}
            </div>
          </div>
        </div>

        {/* Stats */}
        <div className="mt-8">
          <TestCard variant="elevated">
            <div className="p-6 text-center">
              <h3 className="text-xl font-semibold mb-4">Kitchen Stats</h3>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <div className="text-3xl font-bold text-orange-600">
                    {recipes.length}
                  </div>
                  <div className="text-sm text-gray-600">Recipes</div>
                </div>
                <div>
                  <div className="text-3xl font-bold text-orange-600">
                    {liked.size}
                  </div>
                  <div className="text-sm text-gray-600">Favorites</div>
                </div>
              </div>
            </div>
          </TestCard>
        </div>

        {/* Footer */}
        <div className="mt-8 text-center">
          <TestCard variant="outlined">
            <div className="p-6">
              <h3 className="text-xl font-semibold mb-2">Design System Test</h3>
              <p className="text-gray-600 mb-4">
                Simple components working correctly!
              </p>
              <div className="flex justify-center gap-3">
                <TestButton variant="secondary">View More</TestButton>
                <TestButton>Get Started</TestButton>
              </div>
            </div>
          </TestCard>
        </div>
      </div>
    </div>
  );
};

export default SimpleDesignDemo;

import React, { useState } from 'react';
import { 
  AppLayout, 
  PageLayout, 
  DashboardLayout 
} from '@/components/design-system/layout/AppLayout';
import { 
  Container, 
  Section, 
  Grid, 
  Flex, 
  Stack, 
  HStack 
} from '@/components/design-system/layout/Container';
import { 
  Sidebar, 
  KitchenSidebar, 
  NavItem, 
  NavGroup 
} from '@/components/design-system/navigation/Sidebar';
import { 
  Card, 
  CardHeader, 
  CardTitle, 
  CardDescription, 
  CardContent 
} from '@/components/design-system/atoms/Card';
import { Button, ButtonGroup } from '@/components/design-system/atoms/Button';
import { 
  Typography, 
  Heading1, 
  Heading2, 
  Heading3, 
  Text 
} from '@/components/design-system/atoms/Typography';
import { 
  ChefHat, 
  Layout, 
  Sidebar as SidebarIcon, 
  Grid3X3, 
  Layers,
  Home,
  Calendar,
  ShoppingCart,
  Heart,
  Settings,
  User,
  TrendingUp,
  BookOpen,
  Search
} from 'lucide-react';

const LayoutDemo: React.FC = () => {
  const [currentDemo, setCurrentDemo] = useState<'layouts' | 'containers' | 'sidebar' | 'dashboard'>('layouts');
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [activeNavItem, setActiveNavItem] = useState('dashboard');

  const renderLayoutDemo = () => (
    <Section spacing="lg">
      <Container size="xl">
        <div className="text-center mb-12">
          <Heading1 className="mb-4">Layout System Demo</Heading1>
          <Text color="muted" className="text-lg max-w-2xl mx-auto">
            Comprehensive layout components built with responsive design and accessibility in mind.
          </Text>
        </div>

        {/* Layout Variants */}
        <div className="space-y-12">
          <div>
            <Heading2 className="mb-6">Layout Variants</Heading2>
            <Grid cols={2} gap="lg">
              <Card>
                <CardHeader>
                  <CardTitle size="sm">Default Layout</CardTitle>
                  <CardDescription>Clean white background for content-focused pages</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="h-32 bg-white border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center">
                    <Text color="muted">Default Layout Preview</Text>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle size="sm">App Layout</CardTitle>
                  <CardDescription>Gray background perfect for application interfaces</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="h-32 bg-gray-50 border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center">
                    <Text color="muted">App Layout Preview</Text>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle size="sm">Landing Layout</CardTitle>
                  <CardDescription>Gradient background for marketing and landing pages</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="h-32 bg-gradient-to-br from-orange-50 to-red-50 border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center">
                    <Text color="muted">Landing Layout Preview</Text>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle size="sm">Kitchen Layout</CardTitle>
                  <CardDescription>Warm gradient perfect for cooking and recipe apps</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="h-32 bg-gradient-to-br from-orange-25 via-white to-red-25 border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center">
                    <Text color="muted">Kitchen Layout Preview</Text>
                  </div>
                </CardContent>
              </Card>
            </Grid>
          </div>

          {/* Page Layout Features */}
          <div>
            <Heading2 className="mb-6">Page Layout Features</Heading2>
            <Card>
              <CardContent className="p-8">
                <div className="space-y-6">
                  <div className="flex items-center justify-between border-b border-gray-200 pb-4">
                    <div>
                      <Heading3>Recipe Management</Heading3>
                      <Text color="muted">Manage your favorite recipes and cooking plans</Text>
                    </div>
                    <ButtonGroup>
                      <Button variant="outline">Export</Button>
                      <Button>Add Recipe</Button>
                    </ButtonGroup>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <Card variant="outlined">
                      <CardContent className="text-center p-6">
                        <ChefHat className="w-12 h-12 mx-auto mb-4 text-orange-500" />
                        <Text weight="medium">Total Recipes</Text>
                        <Text className="text-2xl font-bold text-orange-600">156</Text>
                      </CardContent>
                    </Card>
                    
                    <Card variant="outlined">
                      <CardContent className="text-center p-6">
                        <Calendar className="w-12 h-12 mx-auto mb-4 text-blue-500" />
                        <Text weight="medium">Meal Plans</Text>
                        <Text className="text-2xl font-bold text-blue-600">24</Text>
                      </CardContent>
                    </Card>
                    
                    <Card variant="outlined">
                      <CardContent className="text-center p-6">
                        <Heart className="w-12 h-12 mx-auto mb-4 text-red-500" />
                        <Text weight="medium">Favorites</Text>
                        <Text className="text-2xl font-bold text-red-600">42</Text>
                      </CardContent>
                    </Card>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </Container>
    </Section>
  );

  const renderContainerDemo = () => (
    <Section spacing="lg">
      <Container size="xl">
        <div className="text-center mb-12">
          <Heading1 className="mb-4">Container & Grid System</Heading1>
          <Text color="muted" className="text-lg">
            Flexible container and grid components for responsive layouts.
          </Text>
        </div>

        <div className="space-y-12">
          {/* Container Sizes */}
          <div>
            <Heading2 className="mb-6">Container Sizes</Heading2>
            <Stack spacing="lg">
              {['sm', 'md', 'lg', 'xl', '2xl'].map((size) => (
                <div key={size}>
                  <Text weight="medium" className="mb-2">Container {size.toUpperCase()}</Text>
                  <Container size={size as any} className="bg-orange-50 border-2 border-dashed border-orange-200 py-4">
                    <Text color="muted" className="text-center">
                      Container with size="{size}"
                    </Text>
                  </Container>
                </div>
              ))}
            </Stack>
          </div>

          {/* Grid System */}
          <div>
            <Heading2 className="mb-6">Grid System</Heading2>
            <div className="space-y-8">
              <div>
                <Text weight="medium" className="mb-4">2 Columns</Text>
                <Grid cols={2} gap="md">
                  <Card><CardContent className="p-4 text-center">Column 1</CardContent></Card>
                  <Card><CardContent className="p-4 text-center">Column 2</CardContent></Card>
                </Grid>
              </div>

              <div>
                <Text weight="medium" className="mb-4">3 Columns</Text>
                <Grid cols={3} gap="md">
                  <Card><CardContent className="p-4 text-center">Column 1</CardContent></Card>
                  <Card><CardContent className="p-4 text-center">Column 2</CardContent></Card>
                  <Card><CardContent className="p-4 text-center">Column 3</CardContent></Card>
                </Grid>
              </div>

              <div>
                <Text weight="medium" className="mb-4">4 Columns</Text>
                <Grid cols={4} gap="md">
                  <Card><CardContent className="p-4 text-center">Col 1</CardContent></Card>
                  <Card><CardContent className="p-4 text-center">Col 2</CardContent></Card>
                  <Card><CardContent className="p-4 text-center">Col 3</CardContent></Card>
                  <Card><CardContent className="p-4 text-center">Col 4</CardContent></Card>
                </Grid>
              </div>
            </div>
          </div>

          {/* Flex System */}
          <div>
            <Heading2 className="mb-6">Flex System</Heading2>
            <div className="space-y-6">
              <div>
                <Text weight="medium" className="mb-4">Horizontal Stack (HStack)</Text>
                <HStack spacing="md" className="p-4 bg-gray-50 rounded-lg">
                  <Card className="flex-1"><CardContent className="p-4 text-center">Item 1</CardContent></Card>
                  <Card className="flex-1"><CardContent className="p-4 text-center">Item 2</CardContent></Card>
                  <Card className="flex-1"><CardContent className="p-4 text-center">Item 3</CardContent></Card>
                </HStack>
              </div>

              <div>
                <Text weight="medium" className="mb-4">Vertical Stack (Stack)</Text>
                <Stack spacing="md" className="p-4 bg-gray-50 rounded-lg max-w-md">
                  <Card><CardContent className="p-4 text-center">Item 1</CardContent></Card>
                  <Card><CardContent className="p-4 text-center">Item 2</CardContent></Card>
                  <Card><CardContent className="p-4 text-center">Item 3</CardContent></Card>
                </Stack>
              </div>
            </div>
          </div>
        </div>
      </Container>
    </Section>
  );

  const renderSidebarDemo = () => (
    <div className="h-screen flex">
      <KitchenSidebar
        collapsed={sidebarCollapsed}
        onToggle={() => setSidebarCollapsed(!sidebarCollapsed)}
        activeItem={activeNavItem}
        onItemClick={setActiveNavItem}
      />
      
      <div className="flex-1 overflow-auto">
        <Container className="py-8">
          <div className="text-center mb-8">
            <Heading1 className="mb-4">Sidebar Navigation Demo</Heading1>
            <Text color="muted" className="text-lg">
              Interactive sidebar with collapsible functionality and active states.
            </Text>
          </div>

          <div className="space-y-8">
            <Card>
              <CardHeader>
                <CardTitle>Sidebar Features</CardTitle>
                <CardDescription>
                  The sidebar includes navigation groups, badges, icons, and responsive behavior.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Grid cols={2} gap="md">
                  <div>
                    <Text weight="medium" className="mb-2">✨ Features</Text>
                    <ul className="space-y-1 text-sm text-gray-600">
                      <li>• Collapsible design</li>
                      <li>• Active state management</li>
                      <li>• Navigation groups</li>
                      <li>• Badge support</li>
                      <li>• Icon integration</li>
                      <li>• Keyboard accessible</li>
                    </ul>
                  </div>
                  <div>
                    <Text weight="medium" className="mb-2">🎯 Current State</Text>
                    <ul className="space-y-1 text-sm text-gray-600">
                      <li>• Collapsed: {sidebarCollapsed ? 'Yes' : 'No'}</li>
                      <li>• Active Item: {activeNavItem}</li>
                      <li>• Width: {sidebarCollapsed ? '64px' : '256px'}</li>
                    </ul>
                  </div>
                </Grid>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-8 text-center">
                <ChefHat className="w-16 h-16 mx-auto mb-4 text-orange-500" />
                <Heading3 className="mb-2">Welcome to Angiday Kitchen</Heading3>
                <Text color="muted">
                  Use the sidebar to navigate between different sections of the application.
                  Try collapsing and expanding the sidebar to see the responsive behavior.
                </Text>
              </CardContent>
            </Card>
          </div>
        </Container>
      </div>
    </div>
  );

  const renderDashboardDemo = () => (
    <DashboardLayout
      sidebar={
        <KitchenSidebar
          collapsed={false}
          activeItem={activeNavItem}
          onItemClick={setActiveNavItem}
        />
      }
      topbar={
        <Flex justify="between" align="center">
          <Heading3>Dashboard Overview</Heading3>
          <HStack spacing="sm">
            <Button variant="outline" size="sm">Export</Button>
            <Button size="sm">Add Recipe</Button>
          </HStack>
        </Flex>
      }
    >
      <div className="space-y-6">
        <Grid cols={4} gap="md">
          <Card>
            <CardContent className="p-6 text-center">
              <BookOpen className="w-8 h-8 mx-auto mb-2 text-blue-500" />
              <Text className="text-2xl font-bold">156</Text>
              <Text color="muted" className="text-sm">Total Recipes</Text>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6 text-center">
              <Calendar className="w-8 h-8 mx-auto mb-2 text-green-500" />
              <Text className="text-2xl font-bold">24</Text>
              <Text color="muted" className="text-sm">Meal Plans</Text>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6 text-center">
              <Heart className="w-8 h-8 mx-auto mb-2 text-red-500" />
              <Text className="text-2xl font-bold">42</Text>
              <Text color="muted" className="text-sm">Favorites</Text>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6 text-center">
              <TrendingUp className="w-8 h-8 mx-auto mb-2 text-orange-500" />
              <Text className="text-2xl font-bold">89%</Text>
              <Text color="muted" className="text-sm">Success Rate</Text>
            </CardContent>
          </Card>
        </Grid>

        <Grid cols={3} gap="md">
          <Card className="col-span-2">
            <CardHeader>
              <CardTitle>Recent Activity</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-64 bg-gray-50 rounded-lg flex items-center justify-center">
                <Text color="muted">Activity Chart Placeholder</Text>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
            </CardHeader>
            <CardContent>
              <Stack spacing="sm">
                <Button variant="outline" className="w-full justify-start">
                  <ChefHat className="w-4 h-4 mr-2" />
                  Add Recipe
                </Button>
                <Button variant="outline" className="w-full justify-start">
                  <Calendar className="w-4 h-4 mr-2" />
                  Plan Meal
                </Button>
                <Button variant="outline" className="w-full justify-start">
                  <ShoppingCart className="w-4 h-4 mr-2" />
                  Shopping List
                </Button>
              </Stack>
            </CardContent>
          </Card>
        </Grid>
      </div>
    </DashboardLayout>
  );

  if (currentDemo === 'sidebar') {
    return renderSidebarDemo();
  }

  if (currentDemo === 'dashboard') {
    return renderDashboardDemo();
  }

  return (
    <AppLayout variant="app">
      <Container className="py-8">
        {/* Demo Navigation */}
        <div className="mb-8">
          <ButtonGroup>
            <Button 
              variant={currentDemo === 'layouts' ? 'primary' : 'outline'}
              onClick={() => setCurrentDemo('layouts')}
              leftIcon={<Layout className="w-4 h-4" />}
            >
              Layouts
            </Button>
            <Button 
              variant={currentDemo === 'containers' ? 'primary' : 'outline'}
              onClick={() => setCurrentDemo('containers')}
              leftIcon={<Grid3X3 className="w-4 h-4" />}
            >
              Containers
            </Button>
            <Button 
              variant={currentDemo === 'sidebar' ? 'primary' : 'outline'}
              onClick={() => setCurrentDemo('sidebar')}
              leftIcon={<SidebarIcon className="w-4 h-4" />}
            >
              Sidebar
            </Button>
            <Button 
              variant={currentDemo === 'dashboard' ? 'primary' : 'outline'}
              onClick={() => setCurrentDemo('dashboard')}
              leftIcon={<Layers className="w-4 h-4" />}
            >
              Dashboard
            </Button>
          </ButtonGroup>
        </div>

        {/* Demo Content */}
        {currentDemo === 'layouts' && renderLayoutDemo()}
        {currentDemo === 'containers' && renderContainerDemo()}
      </Container>
    </AppLayout>
  );
};

export default LayoutDemo;

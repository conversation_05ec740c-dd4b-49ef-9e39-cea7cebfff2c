import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { Separator } from '@/components/ui/separator';
import {
  ShoppingCart,
  Trash2,
  Plus,
  Minus,
  ArrowLeft,
  Download,
  Share2,
  Package,
  DollarSign,
  Clock,
  CheckCircle,
  AlertCircle,
  Sparkles,
  BookOpen,
  ChefHat,
  Calendar,
  Utensils,
  ChevronDown,
  ChevronRight
} from 'lucide-react';
import { useShoppingCart } from '@/contexts/ShoppingCartContext';
import { formatVNDPrice } from '@/utils/vndPriceUtils';
import { toast } from 'sonner';
import Header from '@/components/Header';
import AddFromSourceModal from '@/components/shopping/AddFromSourceModal';
import { BulkDeleteManager } from '@/components/shopping/BulkDeleteManager';

const ShoppingCartPage: React.FC = () => {
  const {
    sessions,
    items,
    totalItems,
    totalEstimatedCost,
    totalActualCost,
    removeItem,
    removeSession,
    toggleSessionCollapsed,
    updateItemQuantity,
    updateItemPrice,
    toggleItemChecked,
    clearCart,
    mergeDuplicateItems,
    optimizeShoppingList
  } = useShoppingCart();

  const [editingPriceId, setEditingPriceId] = useState<string | null>(null);
  const [tempPrice, setTempPrice] = useState<string>('');
  const [showAddFromModal, setShowAddFromModal] = useState(false);
  const [addFromModalTab, setAddFromModalTab] = useState<'recipes' | 'meals' | 'menus'>('recipes');

  // We'll display by sessions instead of categories
  // Keep this for backward compatibility but won't use it
  const itemsByCategory = items.reduce((acc, item) => {
    if (!acc[item.category]) {
      acc[item.category] = [];
    }
    acc[item.category].push(item);
    return acc;
  }, {} as Record<string, typeof items>);

  // Calculate statistics
  const checkedItems = items.filter(item => item.isChecked).length;
  const completionPercentage = totalItems > 0 ? Math.round((checkedItems / totalItems) * 100) : 0;

  const handlePriceEdit = (itemId: string, currentPrice?: number) => {
    setEditingPriceId(itemId);
    setTempPrice(currentPrice ? currentPrice.toString() : '');
  };

  const handlePriceSave = (itemId: string) => {
    const price = parseFloat(tempPrice);
    if (!isNaN(price) && price >= 0) {
      updateItemPrice(itemId, price);
      toast.success('Đã cập nhật giá');
    }
    setEditingPriceId(null);
    setTempPrice('');
  };

  const handlePriceCancel = () => {
    setEditingPriceId(null);
    setTempPrice('');
  };

  const getCategoryIcon = (category: string) => {
    const icons = {
      'Thịt & Hải sản': '🥩',
      'Rau củ quả': '🥕',
      'Gia vị & Condiments': '🧂',
      'Ngũ cốc & Tinh bột': '🌾',
      'Sữa & Trứng': '🥛',
      'Đồ khô & Hạt': '🥜',
      'Đồ uống': '🥤',
      'Khác': '📦'
    };
    return icons[category] || '📦';
  };

  const getSessionIcon = (type: string) => {
    const icons = {
      'recipe': '📝',
      'meal': '🍽️',
      'menu': '📅',
      'manual': '✏️'
    };
    return icons[type] || '📦';
  };

  const getSessionTypeLabel = (type: string) => {
    const labels = {
      'recipe': 'Công thức',
      'meal': 'Bữa ăn',
      'menu': 'Thực đơn',
      'manual': 'Thêm thủ công'
    };
    return labels[type] || type;
  };

  const exportShoppingList = () => {
    const listText = items.map(item => 
      `${item.isChecked ? '✅' : '⬜'} ${item.name} - ${item.quantity} ${item.unit} (${item.category})`
    ).join('\n');
    
    const blob = new Blob([listText], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'danh-sach-mua-sam.txt';
    a.click();
    URL.revokeObjectURL(url);
    toast.success('Đã xuất danh sách mua sắm');
  };

  const shareShoppingList = async () => {
    const listText = items.map(item => 
      `${item.name} - ${item.quantity} ${item.unit}`
    ).join('\n');
    
    if (navigator.share) {
      try {
        await navigator.share({
          title: 'Danh sách mua sắm',
          text: listText,
        });
      } catch (error) {
        console.error('Error sharing:', error);
      }
    } else {
      navigator.clipboard.writeText(listText);
      toast.success('Đã sao chép danh sách vào clipboard');
    }
  };

  if (totalItems === 0) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <div className="max-w-4xl mx-auto px-4 py-8">
          <div className="text-center py-16">
            <ShoppingCart className="h-24 w-24 text-gray-300 mx-auto mb-6" />
            <h2 className="text-2xl font-bold text-gray-900 mb-4">Giỏ hàng trống</h2>
            <p className="text-gray-600 mb-8">Hãy thêm nguyên liệu từ công thức, bữa ăn hoặc thực đơn vào giỏ hàng</p>

            {/* Quick Add Buttons */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 max-w-2xl mx-auto mb-8">
              <Card className="cursor-pointer hover:shadow-md transition-all" onClick={() => {
                setAddFromModalTab('recipes');
                setShowAddFromModal(true);
              }}>
                <CardContent className="p-6 text-center">
                  <BookOpen className="h-12 w-12 text-orange-500 mx-auto mb-3" />
                  <h3 className="font-semibold text-gray-900 mb-2">Từ công thức</h3>
                  <p className="text-sm text-gray-600">Chọn công thức yêu thích và thêm nguyên liệu</p>
                </CardContent>
              </Card>

              <Card className="cursor-pointer hover:shadow-md transition-all" onClick={() => {
                setAddFromModalTab('menus');
                setShowAddFromModal(true);
              }}>
                <CardContent className="p-6 text-center">
                  <Calendar className="h-12 w-12 text-blue-500 mx-auto mb-3" />
                  <h3 className="font-semibold text-gray-900 mb-2">Từ thực đơn</h3>
                  <p className="text-sm text-gray-600">Thêm nguyên liệu từ thực đơn có sẵn</p>
                </CardContent>
              </Card>

              <Card className="cursor-pointer hover:shadow-md transition-all" onClick={() => {
                setAddFromModalTab('meals');
                setShowAddFromModal(true);
              }}>
                <CardContent className="p-6 text-center">
                  <Utensils className="h-12 w-12 text-green-500 mx-auto mb-3" />
                  <h3 className="font-semibold text-gray-900 mb-2">Từ bữa ăn</h3>
                  <p className="text-sm text-gray-600">Thêm nguyên liệu từ bữa ăn hôm nay</p>
                </CardContent>
              </Card>
            </div>

            <div className="space-x-4">
              <Button asChild>
                <Link to="/recipes">
                  <BookOpen className="h-4 w-4 mr-2" />
                  Duyệt công thức
                </Link>
              </Button>
              <Button variant="outline" asChild>
                <Link to="/meal-plans">
                  <Calendar className="h-4 w-4 mr-2" />
                  Xem thực đơn
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      <div className="max-w-6xl mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center space-x-4">
            <Button variant="ghost" size="icon" asChild>
              <Link to="/">
                <ArrowLeft className="h-5 w-5" />
              </Link>
            </Button>
            <div>
              <h1 className="text-3xl font-bold text-gray-900 flex items-center">
                <ShoppingCart className="h-8 w-8 mr-3 text-orange-500" />
                Giỏ hàng
              </h1>
              <p className="text-gray-600 mt-1">
                {totalItems} món • {completionPercentage}% hoàn thành
              </p>
            </div>
          </div>
          
          <div className="flex items-center space-x-3">
            {/* Add From... Buttons */}
            <div className="flex items-center space-x-2 mr-4">
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  setAddFromModalTab('recipes');
                  setShowAddFromModal(true);
                }}
              >
                <BookOpen className="h-4 w-4 mr-2" />
                Thêm từ công thức
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  setAddFromModalTab('menus');
                  setShowAddFromModal(true);
                }}
              >
                <Calendar className="h-4 w-4 mr-2" />
                Thêm từ thực đơn
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  setAddFromModalTab('meals');
                  setShowAddFromModal(true);
                }}
              >
                <Utensils className="h-4 w-4 mr-2" />
                Thêm từ bữa ăn
              </Button>
            </div>

            {/* Utility Buttons */}
            <div className="flex items-center space-x-2 border-l border-gray-200 pl-4">
              <Button variant="outline" size="sm" onClick={mergeDuplicateItems}>
                <Sparkles className="h-4 w-4 mr-2" />
                Gộp trùng lặp
              </Button>
              <Button variant="outline" size="sm" onClick={optimizeShoppingList}>
                <CheckCircle className="h-4 w-4 mr-2" />
                Tối ưu hóa
              </Button>
              <Button variant="outline" size="sm" onClick={exportShoppingList}>
                <Download className="h-4 w-4 mr-2" />
                Xuất file
              </Button>
              <Button variant="outline" size="sm" onClick={shareShoppingList}>
                <Share2 className="h-4 w-4 mr-2" />
                Chia sẻ
              </Button>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Shopping List */}
          <div className="lg:col-span-2 space-y-6">
            {/* Progress Card */}
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="font-semibold text-gray-900">Tiến độ mua sắm</h3>
                  <span className="text-sm text-gray-600">{checkedItems}/{totalItems}</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-3">
                  <div 
                    className="bg-gradient-to-r from-orange-500 to-red-500 h-3 rounded-full transition-all duration-300"
                    style={{ width: `${completionPercentage}%` }}
                  />
                </div>
                <p className="text-sm text-gray-600 mt-2">{completionPercentage}% hoàn thành</p>
              </CardContent>
            </Card>

            {/* Shopping Items by Session */}
            {sessions.map((session) => (
              <Card key={session.id}>
                <CardHeader className="pb-4">
                  <CardTitle className="flex items-center text-lg">
                    <button
                      onClick={() => toggleSessionCollapsed(session.id)}
                      className="flex items-center flex-1 text-left hover:text-orange-600 transition-colors"
                    >
                      <span className="text-2xl mr-3">{getSessionIcon(session.type)}</span>
                      <div className="flex-1">
                        <div className="flex items-center">
                          <span>{session.name}</span>
                          <Badge variant="outline" className="ml-2 text-xs">
                            {getSessionTypeLabel(session.type)}
                          </Badge>
                        </div>
                        {session.description && (
                          <p className="text-sm text-gray-600 font-normal mt-1">
                            {session.description}
                          </p>
                        )}
                      </div>
                      <div className="flex items-center space-x-2">
                        <Badge variant="secondary">
                          {session.items.length} món
                        </Badge>
                        <Badge variant="outline">
                          {formatVNDPrice(session.totalEstimatedCost)}
                        </Badge>
                        <span className="text-gray-400">
                          {session.isCollapsed ? '▶' : '▼'}
                        </span>
                      </div>
                    </button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => removeSession(session.id)}
                      className="ml-2 text-red-500 hover:text-red-700 hover:bg-red-50"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </CardTitle>
                </CardHeader>
                {!session.isCollapsed && (
                  <CardContent className="space-y-3">
                    {session.items.map((item) => (
                    <div
                      key={item.id}
                      className={`flex items-center space-x-4 p-4 rounded-lg border transition-all ${
                        item.isChecked ? 'bg-green-50 border-green-200' : 'bg-white border-gray-200 hover:border-gray-300'
                      }`}
                    >
                      <Checkbox
                        checked={item.isChecked}
                        onCheckedChange={() => toggleItemChecked(item.id)}
                      />
                      
                      <div className="flex-1">
                        <div className="flex items-center justify-between">
                          <h4 className={`font-medium ${item.isChecked ? 'line-through text-gray-500' : 'text-gray-900'}`}>
                            {item.name}
                          </h4>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => removeItem(item.id)}
                            className="text-red-500 hover:text-red-700 hover:bg-red-50"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                        
                        <div className="flex items-center space-x-4 mt-2">
                          {/* Quantity Controls */}
                          <div className="flex items-center space-x-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => updateItemQuantity(item.id, item.quantity - 1)}
                              disabled={item.quantity <= 1}
                            >
                              <Minus className="h-3 w-3" />
                            </Button>
                            <span className="w-12 text-center text-sm font-medium">
                              {item.quantity} {item.unit}
                            </span>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => updateItemQuantity(item.id, item.quantity + 1)}
                            >
                              <Plus className="h-3 w-3" />
                            </Button>
                          </div>

                          {/* Price */}
                          <div className="flex items-center space-x-2">
                            {editingPriceId === item.id ? (
                              <div className="flex items-center space-x-2">
                                <Input
                                  type="number"
                                  value={tempPrice}
                                  onChange={(e) => setTempPrice(e.target.value)}
                                  className="w-24 h-8"
                                  placeholder="Giá"
                                />
                                <Button size="sm" onClick={() => handlePriceSave(item.id)}>
                                  <CheckCircle className="h-3 w-3" />
                                </Button>
                                <Button variant="ghost" size="sm" onClick={handlePriceCancel}>
                                  ×
                                </Button>
                              </div>
                            ) : (
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handlePriceEdit(item.id, item.actualPrice || item.estimatedPrice)}
                                className="text-sm text-gray-600 hover:text-gray-900"
                              >
                                <DollarSign className="h-3 w-3 mr-1" />
                                {formatVNDPrice(item.actualPrice || item.estimatedPrice || 0)}
                              </Button>
                            )}
                          </div>

                          {/* Source */}
                          <Badge variant="outline" className="text-xs">
                            {item.source.name}
                          </Badge>
                        </div>
                      </div>
                    </div>
                  ))}
                  </CardContent>
                )}
              </Card>
            ))}
          </div>

          {/* Summary Sidebar */}
          <div className="space-y-6">
            {/* Cost Summary */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <DollarSign className="h-5 w-5 mr-2" />
                  Tổng chi phí
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between">
                  <span className="text-gray-600">Ước tính:</span>
                  <span className="font-medium">{formatVNDPrice(totalEstimatedCost)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Thực tế:</span>
                  <span className="font-medium">{formatVNDPrice(totalActualCost)}</span>
                </div>
                <Separator />
                <div className="flex justify-between text-lg font-bold">
                  <span>Tổng cộng:</span>
                  <span className="text-orange-600">{formatVNDPrice(totalActualCost)}</span>
                </div>
              </CardContent>
            </Card>

            {/* Quick Actions */}
            <Card>
              <CardHeader>
                <CardTitle>Thao tác nhanh</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <Button 
                  variant="outline" 
                  className="w-full justify-start"
                  onClick={clearCart}
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  Xóa toàn bộ
                </Button>
                <Button 
                  variant="outline" 
                  className="w-full justify-start"
                  onClick={() => items.forEach(item => toggleItemChecked(item.id))}
                >
                  <CheckCircle className="h-4 w-4 mr-2" />
                  Đánh dấu tất cả
                </Button>
                <Button className="w-full">
                  <Package className="h-4 w-4 mr-2" />
                  Hoàn thành mua sắm
                </Button>
              </CardContent>
            </Card>

            {/* Shopping Tips */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <AlertCircle className="h-5 w-5 mr-2" />
                  Mẹo mua sắm
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-2 text-sm text-gray-600">
                <p>• Mua rau củ tươi vào buổi sáng</p>
                <p>• Kiểm tra hạn sử dụng trước khi mua</p>
                <p>• So sánh giá ở nhiều cửa hàng</p>
                <p>• Mang túi tái sử dụng</p>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Bulk Delete Manager */}
        <div className="mt-8">
          <BulkDeleteManager />
        </div>

        {/* Add From Source Modal */}
        <AddFromSourceModal
          isOpen={showAddFromModal}
          onClose={() => setShowAddFromModal(false)}
          defaultTab={addFromModalTab}
        />
      </div>
    </div>
  );
};

export default ShoppingCartPage;

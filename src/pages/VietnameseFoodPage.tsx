import React from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ArrowLeft, ChefHat, Database, Download, Share2 } from 'lucide-react';
import { Link } from 'react-router-dom';
import Header from '@/components/Header';
import { VietnameseFoodExplorer } from '@/components/VietnameseFoodExplorer';

const VietnameseFoodPage: React.FC = () => {
  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      <div className="max-w-7xl mx-auto px-4 py-8">
        {/* Page Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center space-x-4">
            <Button variant="ghost" size="icon" asChild>
              <Link to="/">
                <ArrowLeft className="h-5 w-5" />
              </Link>
            </Button>
            <div>
              <h1 className="text-3xl font-bold text-gray-900 flex items-center">
                <ChefHat className="h-8 w-8 mr-3 text-orange-500" />
                Món Ăn Việt Nam
              </h1>
              <p className="text-gray-600 mt-1">
                Khám phá kho tàng ẩm thực Việt Nam với dữ liệu được cập nhật mới nhất
              </p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
              <Database className="h-3 w-3 mr-1" />
              Dữ liệu mới
            </Badge>
          </div>
        </div>

        {/* Info Banner */}
        <Card className="mb-8 border-blue-200 bg-blue-50">
          <CardContent className="p-6">
            <div className="flex items-start gap-4">
              <ChefHat className="h-6 w-6 text-blue-600 mt-1" />
              <div>
                <h3 className="font-semibold text-blue-800 mb-2">🍜 Dữ liệu món ăn Việt Nam mới được cập nhật</h3>
                <div className="text-blue-700 space-y-1">
                  <p>• <strong>14 món ăn</strong> được phân loại chi tiết theo thói quen ăn uống</p>
                  <p>• <strong>Món ăn sáng/ngoài hàng:</strong> Phở, bún, hủ tiếu, bánh mì...</p>
                  <p>• <strong>Món ăn cơm hàng ngày:</strong> Gà rang gừng, thịt kho, canh sườn...</p>
                  <p>• <strong>Phân chia vùng miền:</strong> Bắc, Trung, Nam với đặc sản riêng</p>
                  <p>• <strong>Thông tin đầy đủ:</strong> Nguyên liệu, cách làm, dinh dưỡng, giá cả</p>
                </div>
                <div className="flex gap-2 mt-4">
                  <Button variant="outline" size="sm">
                    <Download className="h-4 w-4 mr-2" />
                    Tải dữ liệu
                  </Button>
                  <Button variant="outline" size="sm">
                    <Share2 className="h-4 w-4 mr-2" />
                    Chia sẻ
                  </Button>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Main Explorer Component */}
        <VietnameseFoodExplorer />

        {/* Additional Info */}
        <Card className="mt-8">
          <CardHeader>
            <CardTitle>Thông Tin Thêm</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-semibold text-gray-900 mb-3">Phân loại món ăn:</h4>
                <ul className="space-y-2 text-sm text-gray-600">
                  <li>🌅 <strong>Món ăn sáng/ngoài hàng:</strong> Phở, bún, hủ tiếu, bánh mì - thường ăn ngoài hàng</li>
                  <li>🍽️ <strong>Món ăn cơm hàng ngày:</strong> Các món nấu tại nhà ăn với cơm</li>
                  <li>🍪 <strong>Món ăn vặt:</strong> Bánh xèo, chè, các món tráng miệng</li>
                </ul>
              </div>
              <div>
                <h4 className="font-semibold text-gray-900 mb-3">Vùng miền:</h4>
                <ul className="space-y-2 text-sm text-gray-600">
                  <li>🏔️ <strong>Miền Bắc:</strong> Phở bò Hà Nội, bún chả</li>
                  <li>🏛️ <strong>Miền Trung:</strong> Bún bò Huế, mì Quảng</li>
                  <li>🌴 <strong>Miền Nam:</strong> Hủ tiếu Nam Vang, bánh xèo</li>
                  <li>🇻🇳 <strong>Toàn quốc:</strong> Các món phổ biến khắp cả nước</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default VietnameseFoodPage;
import React, { useState } from 'react';
import {
  ChefHat,
  Heart,
  Clock,
  Users,
  Star,
  Bookmark,
  ShoppingCart,
  Plus,
  Settings,
  Search,
  Filter,
  Mail,
  User,
  Lock,
  Edit3,
  Save,
  X
} from 'lucide-react';
import { <PERSON><PERSON>, ButtonGroup, IconButton } from '@/components/design-system/atoms/Button';
import {
  Card,
  CardHeader,
  CardTitle,
  CardDescription,
  CardContent,
  CardFooter,
  CardImage
} from '@/components/design-system/atoms/Card';
import {
  Input,
  PasswordInput,
  SearchInput,
  Textarea,
  FormField
} from '@/components/design-system/atoms/Input';
import {
  Typography,
  Heading1,
  Heading2,
  Heading3,
  Text,
  SmallText,
  Link
} from '@/components/design-system/atoms/Typography';
import {
  <PERSON><PERSON>,
  <PERSON>dalHeader,
  ModalContent,
  ModalFooter,
  ConfirmationModal
} from '@/components/design-system/molecules/Modal';

const DesignSystemDemo: React.FC = () => {
  const [likedRecipes, setLikedRecipes] = useState<Set<number>>(new Set());
  const [loading, setLoading] = useState<number | null>(null);
  const [showModal, setShowModal] = useState(false);
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const [formData, setFormData] = useState({
    recipeName: '',
    email: '',
    password: '',
    description: '',
    searchQuery: '',
  });

  const toggleLike = (recipeId: number) => {
    setLoading(recipeId);
    setTimeout(() => {
      setLikedRecipes(prev => {
        const newSet = new Set(prev);
        if (newSet.has(recipeId)) {
          newSet.delete(recipeId);
        } else {
          newSet.add(recipeId);
        }
        return newSet;
      });
      setLoading(null);
    }, 1000);
  };

  const sampleRecipes = [
    {
      id: 1,
      title: "Creamy Pasta Carbonara",
      description: "Classic Italian pasta with eggs, cheese, and pancetta",
      image: "https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=400&h=300&fit=crop",
      time: "25 min",
      servings: 4,
      rating: 4.8,
    },
    {
      id: 2,
      title: "Grilled Salmon Teriyaki",
      description: "Fresh salmon glazed with homemade teriyaki sauce",
      image: "https://images.unsplash.com/photo-1467003909585-2f8a72700288?w=400&h=300&fit=crop",
      time: "20 min",
      servings: 2,
      rating: 4.6,
    },
    {
      id: 3,
      title: "Vegetable Stir Fry",
      description: "Colorful mix of fresh vegetables in savory sauce",
      image: "https://images.unsplash.com/photo-1512621776951-a57141f2eefd?w=400&h=300&fit=crop",
      time: "15 min",
      servings: 3,
      rating: 4.4,
    },
    {
      id: 4,
      title: "Chocolate Lava Cake",
      description: "Decadent dessert with molten chocolate center",
      image: "https://images.unsplash.com/photo-1606313564200-e75d5e30476c?w=400&h=300&fit=crop",
      time: "35 min",
      servings: 6,
      rating: 4.9,
    },
  ];

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="container mx-auto px-4 max-w-7xl">
        {/* Header */}
        <div className="mb-8">
          <Heading1 className="mb-2">
            Enhanced Design System Demo
          </Heading1>
          <Text color="muted" className="text-lg">
            Comprehensive showcase of UI components built with BMAD-METHOD
          </Text>
        </div>

        {/* Typography Showcase */}
        <section className="mb-12">
          <Card className="p-6">
            <Heading2 className="mb-6">Typography System</Heading2>

            {/* Headings */}
            <div className="mb-8">
              <Heading3 className="mb-4">Headings</Heading3>
              <div className="space-y-3">
                <Heading1>Heading 1 - Main Title</Heading1>
                <Heading2>Heading 2 - Section Title</Heading2>
                <Heading3>Heading 3 - Subsection</Heading3>
                <Typography variant="h4">Heading 4 - Component Title</Typography>
                <Typography variant="h5">Heading 5 - Small Section</Typography>
                <Typography variant="h6">Heading 6 - Minor Heading</Typography>
              </div>
            </div>

            {/* Body Text */}
            <div className="mb-8">
              <Heading3 className="mb-4">Body Text</Heading3>
              <div className="space-y-4 max-w-2xl">
                <Text>
                  This is body text (body1) with normal weight and relaxed line height.
                  Perfect for main content, descriptions, and general reading material.
                </Text>
                <SmallText>
                  This is smaller body text (body2) ideal for secondary information,
                  captions, and supporting details.
                </SmallText>
                <Typography variant="subtitle1">
                  Subtitle 1 - Used for section introductions
                </Typography>
                <Typography variant="subtitle2">
                  Subtitle 2 - Used for component descriptions
                </Typography>
              </div>
            </div>

            {/* Colors and Styles */}
            <div className="mb-8">
              <Heading3 className="mb-4">Colors & Styles</Heading3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Text color="default">Default text color</Text>
                  <Text color="muted">Muted text color</Text>
                  <Text color="subtle">Subtle text color</Text>
                  <Text color="primary">Primary brand color</Text>
                </div>
                <div className="space-y-2">
                  <Text color="success">Success message</Text>
                  <Text color="warning">Warning message</Text>
                  <Text color="danger">Error message</Text>
                  <Link href="#" color="primary">Interactive link</Link>
                </div>
              </div>
            </div>

            {/* Special Elements */}
            <div>
              <Heading3 className="mb-4">Special Elements</Heading3>
              <div className="space-y-4">
                <div className="flex items-center gap-4">
                  <Typography variant="code">const recipe = "delicious";</Typography>
                  <Typography variant="kbd">Ctrl + S</Typography>
                  <Typography variant="caption">Image Caption</Typography>
                </div>
                <Typography variant="overline">Overline Text</Typography>
              </div>
            </div>
          </Card>
        </section>

        {/* Button Showcase */}
        <section className="mb-12">
          <Card className="p-6">
            <Heading2 className="mb-6">Enhanced Buttons</Heading2>
            
            {/* Button Variants */}
            <div className="mb-6">
              <h3 className="text-lg font-medium mb-3">Variants</h3>
              <div className="flex flex-wrap gap-3">
                <Button variant="primary">Primary</Button>
                <Button variant="secondary">Secondary</Button>
                <Button variant="ghost">Ghost</Button>
                <Button variant="danger">Danger</Button>
                <Button variant="outline">Outline</Button>
                <Button variant="link">Link</Button>
              </div>
            </div>

            {/* Button Sizes */}
            <div className="mb-6">
              <h3 className="text-lg font-medium mb-3">Sizes</h3>
              <div className="flex flex-wrap items-center gap-3">
                <Button size="xs">Extra Small</Button>
                <Button size="sm">Small</Button>
                <Button size="md">Medium</Button>
                <Button size="lg">Large</Button>
                <Button size="xl">Extra Large</Button>
              </div>
            </div>

            {/* Button States */}
            <div className="mb-6">
              <h3 className="text-lg font-medium mb-3">States & Features</h3>
              <div className="flex flex-wrap gap-3">
                <Button loading loadingText="Saving...">
                  Loading Button
                </Button>
                <Button disabled>Disabled</Button>
                <Button leftIcon={<ChefHat className="w-4 h-4" />}>
                  With Icon
                </Button>
                <Button 
                  rightIcon={<ShoppingCart className="w-4 h-4" />}
                  variant="outline"
                >
                  Add to Cart
                </Button>
              </div>
            </div>

            {/* Icon Buttons */}
            <div className="mb-6">
              <h3 className="text-lg font-medium mb-3">Icon Buttons</h3>
              <div className="flex gap-3">
                <IconButton
                  icon={<Heart className="w-4 h-4" />}
                  aria-label="Like"
                  variant="ghost"
                />
                <IconButton
                  icon={<Bookmark className="w-4 h-4" />}
                  aria-label="Bookmark"
                  variant="outline"
                />
                <IconButton
                  icon={<Settings className="w-4 h-4" />}
                  aria-label="Settings"
                  variant="primary"
                />
              </div>
            </div>

            {/* Button Groups */}
            <div>
              <h3 className="text-lg font-medium mb-3">Button Groups</h3>
              <div className="space-y-4">
                <ButtonGroup>
                  <Button variant="outline">Previous</Button>
                  <Button>Current</Button>
                  <Button variant="outline">Next</Button>
                </ButtonGroup>
                
                <ButtonGroup attached>
                  <Button variant="outline">Day</Button>
                  <Button>Week</Button>
                  <Button variant="outline">Month</Button>
                </ButtonGroup>
              </div>
            </div>
          </Card>
        </section>

        {/* Input Showcase */}
        <section className="mb-12">
          <Card className="p-6">
            <Heading2 className="mb-6">Enhanced Input Components</Heading2>

            {/* Basic Inputs */}
            <div className="mb-8">
              <Heading3 className="mb-4">Input Variants</Heading3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-3">
                  <Text weight="medium">Default</Text>
                  <Input placeholder="Default input" />
                  <Input
                    placeholder="With icon"
                    leftIcon={<User className="w-4 h-4" />}
                  />
                </div>
                <div className="space-y-3">
                  <Text weight="medium">Filled</Text>
                  <Input variant="filled" placeholder="Filled input" />
                  <Input
                    variant="filled"
                    placeholder="With icon"
                    leftIcon={<Mail className="w-4 h-4" />}
                  />
                </div>
                <div className="space-y-3">
                  <Text weight="medium">Ghost</Text>
                  <Input variant="ghost" placeholder="Ghost input" />
                  <Input
                    variant="ghost"
                    placeholder="With icon"
                    leftIcon={<Search className="w-4 h-4" />}
                  />
                </div>
              </div>
            </div>

            {/* Input States */}
            <div className="mb-8">
              <Heading3 className="mb-4">Input States</Heading3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <Input state="default" placeholder="Default state" />
                <Input state="error" placeholder="Error state" />
                <Input state="success" placeholder="Success state" />
                <Input state="warning" placeholder="Warning state" />
              </div>
            </div>

            {/* Special Inputs */}
            <div className="mb-8">
              <Heading3 className="mb-4">Specialized Inputs</Heading3>
              <div className="space-y-4 max-w-md">
                <SearchInput
                  placeholder="Search recipes..."
                  value={formData.searchQuery}
                  onChange={(e) => setFormData(prev => ({ ...prev, searchQuery: e.target.value }))}
                  onSearch={(value) => alert(`Searching for: ${value}`)}
                />
                <PasswordInput
                  placeholder="Enter password"
                  value={formData.password}
                  onChange={(e) => setFormData(prev => ({ ...prev, password: e.target.value }))}
                />
                <Input
                  placeholder="Clearable input"
                  value={formData.recipeName}
                  onChange={(e) => setFormData(prev => ({ ...prev, recipeName: e.target.value }))}
                  clearable
                  onClear={() => setFormData(prev => ({ ...prev, recipeName: '' }))}
                />
              </div>
            </div>

            {/* Form Fields */}
            <div>
              <Heading3 className="mb-4">Form Fields with Validation</Heading3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 max-w-4xl">
                <FormField
                  label="Recipe Name"
                  description="Give your recipe a memorable name"
                  required
                  htmlFor="recipe-name"
                >
                  <Input
                    id="recipe-name"
                    placeholder="Grandma's Secret Pasta"
                    leftIcon={<ChefHat className="w-4 h-4" />}
                    value={formData.recipeName}
                    onChange={(e) => setFormData(prev => ({ ...prev, recipeName: e.target.value }))}
                  />
                </FormField>

                <FormField
                  label="Email Address"
                  error={formData.email && !formData.email.includes('@') ? 'Please enter a valid email' : undefined}
                  success={formData.email && formData.email.includes('@') ? 'Email looks good!' : undefined}
                  htmlFor="email"
                >
                  <Input
                    id="email"
                    type="email"
                    placeholder="<EMAIL>"
                    leftIcon={<Mail className="w-4 h-4" />}
                    value={formData.email}
                    onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
                  />
                </FormField>

                <FormField
                  label="Recipe Description"
                  description="Describe what makes this recipe special"
                  htmlFor="description"
                  className="md:col-span-2"
                >
                  <Textarea
                    id="description"
                    placeholder="This recipe has been passed down through generations..."
                    value={formData.description}
                    onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                    size="lg"
                  />
                </FormField>
              </div>
            </div>
          </Card>
        </section>

        {/* Card Showcase */}
        <section className="mb-12">
          <Card className="p-6 mb-6">
            <h2 className="text-2xl font-semibold mb-6">Enhanced Cards</h2>
            
            {/* Card Variants */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
              <Card variant="default">
                <CardContent>
                  <h4 className="font-medium mb-2">Default Card</h4>
                  <p className="text-sm text-gray-600">Standard card styling</p>
                </CardContent>
              </Card>
              
              <Card variant="elevated">
                <CardContent>
                  <h4 className="font-medium mb-2">Elevated Card</h4>
                  <p className="text-sm text-gray-600">Enhanced shadow</p>
                </CardContent>
              </Card>
              
              <Card variant="outlined">
                <CardContent>
                  <h4 className="font-medium mb-2">Outlined Card</h4>
                  <p className="text-sm text-gray-600">Prominent border</p>
                </CardContent>
              </Card>
              
              <Card variant="gradient">
                <CardContent>
                  <h4 className="font-medium mb-2">Gradient Card</h4>
                  <p className="text-sm text-gray-600">Gradient background</p>
                </CardContent>
              </Card>
            </div>
          </Card>

          {/* Recipe Cards Grid */}
          <div>
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-2xl font-semibold">Recipe Collection</h2>
              <div className="flex gap-2">
                <IconButton
                  icon={<Search className="w-4 h-4" />}
                  aria-label="Search recipes"
                  variant="outline"
                />
                <IconButton
                  icon={<Filter className="w-4 h-4" />}
                  aria-label="Filter recipes"
                  variant="outline"
                />
                <Button leftIcon={<Plus className="w-4 h-4" />}>
                  Add Recipe
                </Button>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              {sampleRecipes.map((recipe) => (
                <Card key={recipe.id} interactive className="group">
                  <CardImage
                    src={recipe.image}
                    alt={recipe.title}
                    aspectRatio="video"
                  />
                  
                  <CardHeader>
                    <div className="flex items-start justify-between">
                      <div className="flex-1 min-w-0">
                        <CardTitle size="sm" className="truncate">
                          {recipe.title}
                        </CardTitle>
                        <CardDescription size="sm" className="line-clamp-2 overflow-hidden">
                          {recipe.description}
                        </CardDescription>
                      </div>
                      <IconButton
                        icon={
                          <Heart 
                            className={`w-4 h-4 transition-colors ${
                              likedRecipes.has(recipe.id) 
                                ? 'fill-red-500 text-red-500' 
                                : 'text-gray-400 hover:text-red-500'
                            }`} 
                          />
                        }
                        aria-label={
                          likedRecipes.has(recipe.id) 
                            ? 'Remove from favorites' 
                            : 'Add to favorites'
                        }
                        variant="ghost"
                        size="sm"
                        loading={loading === recipe.id}
                        onClick={() => toggleLike(recipe.id)}
                      />
                    </div>
                  </CardHeader>

                  <CardContent>
                    <div className="flex items-center gap-4 text-sm text-gray-600">
                      <div className="flex items-center gap-1">
                        <Clock className="w-4 h-4" />
                        <span>{recipe.time}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <Users className="w-4 h-4" />
                        <span>{recipe.servings}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <Star className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                        <span>{recipe.rating}</span>
                      </div>
                    </div>
                  </CardContent>

                  <CardFooter justify="between">
                    <ButtonGroup size="sm">
                      <Button variant="outline" size="sm">
                        <Bookmark className="w-4 h-4" />
                      </Button>
                      <Button size="sm">
                        <ChefHat className="w-4 h-4" />
                        Cook
                      </Button>
                    </ButtonGroup>
                  </CardFooter>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Modal Showcase */}
        <section className="mb-12">
          <Card className="p-6">
            <Heading2 className="mb-6">Modal & Dialog Components</Heading2>

            <div className="space-y-4">
              <Text color="muted" className="mb-6">
                Interactive modals with focus management, keyboard navigation, and accessibility features.
              </Text>

              <div className="flex flex-wrap gap-4">
                <Button onClick={() => setShowModal(true)}>
                  Open Recipe Modal
                </Button>
                <Button
                  variant="danger"
                  onClick={() => setShowConfirmModal(true)}
                >
                  Delete Recipe
                </Button>
              </div>
            </div>

            {/* Recipe Modal */}
            <Modal
              open={showModal}
              onClose={() => setShowModal(false)}
              size="lg"
            >
              <ModalHeader
                title="Create New Recipe"
                description="Add a delicious recipe to your collection"
              />
              <ModalContent>
                <div className="space-y-6">
                  <FormField
                    label="Recipe Name"
                    required
                    htmlFor="modal-recipe-name"
                  >
                    <Input
                      id="modal-recipe-name"
                      placeholder="Enter recipe name"
                      leftIcon={<ChefHat className="w-4 h-4" />}
                    />
                  </FormField>

                  <FormField
                    label="Cooking Time"
                    htmlFor="cooking-time"
                  >
                    <Input
                      id="cooking-time"
                      placeholder="30 minutes"
                      leftIcon={<Clock className="w-4 h-4" />}
                    />
                  </FormField>

                  <FormField
                    label="Instructions"
                    htmlFor="instructions"
                  >
                    <Textarea
                      id="instructions"
                      placeholder="1. Heat oil in a large pan...&#10;2. Add garlic and cook until fragrant..."
                      size="lg"
                    />
                  </FormField>
                </div>
              </ModalContent>
              <ModalFooter>
                <Button variant="ghost" onClick={() => setShowModal(false)}>
                  Cancel
                </Button>
                <Button onClick={() => setShowModal(false)}>
                  <Save className="w-4 h-4 mr-2" />
                  Save Recipe
                </Button>
              </ModalFooter>
            </Modal>

            {/* Confirmation Modal */}
            <ConfirmationModal
              open={showConfirmModal}
              onClose={() => setShowConfirmModal(false)}
              onConfirm={() => {
                alert('Recipe deleted!');
                setShowConfirmModal(false);
              }}
              title="Delete Recipe"
              description="Are you sure you want to delete this recipe? This action cannot be undone."
              confirmText="Delete"
              cancelText="Cancel"
              confirmVariant="danger"
            />
          </Card>
        </section>

        {/* Interactive Demo */}
        <section className="mb-12">
          <Card className="p-6">
            <Heading2 className="mb-6">Interactive Features</Heading2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card variant="outlined" interactive>
                <CardContent className="text-center">
                  <ChefHat className="w-16 h-16 mx-auto mb-4 text-orange-500" />
                  <CardTitle>Start Cooking</CardTitle>
                  <CardDescription className="mb-4">
                    Begin your culinary journey with guided recipes
                  </CardDescription>
                  <Button fullWidth>Get Started</Button>
                </CardContent>
              </Card>

              <Card variant="gradient">
                <CardHeader>
                  <CardTitle>Kitchen Stats</CardTitle>
                  <CardDescription>Your cooking progress</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="text-center">
                      <div className="text-3xl font-bold text-orange-600">
                        {sampleRecipes.length}
                      </div>
                      <div className="text-sm text-gray-600">Recipes</div>
                    </div>
                    <div className="text-center">
                      <div className="text-3xl font-bold text-orange-600">
                        {likedRecipes.size}
                      </div>
                      <div className="text-sm text-gray-600">Favorites</div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </Card>
        </section>

        {/* Footer */}
        <Card className="p-8 text-center">
          <Heading2 className="mb-4">🎉 Enhanced Design System Complete</Heading2>
          <Text color="muted" className="mb-6 max-w-2xl mx-auto">
            Comprehensive UI component library built with BMAD-METHOD featuring enhanced buttons,
            inputs, typography, modals, and cards. All components include accessibility features,
            TypeScript support, and responsive design.
          </Text>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8 max-w-2xl mx-auto">
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-600">15+</div>
              <SmallText color="muted">Components</SmallText>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-600">50+</div>
              <SmallText color="muted">Variants</SmallText>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-600">100%</div>
              <SmallText color="muted">Accessible</SmallText>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-600">TS</div>
              <SmallText color="muted">TypeScript</SmallText>
            </div>
          </div>

          <ButtonGroup>
            <Button variant="outline" leftIcon={<Edit3 className="w-4 h-4" />}>
              View Documentation
            </Button>
            <Button leftIcon={<ChefHat className="w-4 h-4" />}>
              Start Cooking
            </Button>
          </ButtonGroup>
        </Card>
      </div>
    </div>
  );
};

export default DesignSystemDemo;

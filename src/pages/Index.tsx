
import React, { useState, useEffect } from 'react';
import PublicLayout from '@/components/layouts/PublicLayout';
import MobileLayout from '@/components/layouts/MobileLayout';
import HeroSection from '@/components/HeroSection';
import PopularRecipes from '@/components/PopularRecipes';
import EasyDinnerRecipes from '@/components/EasyDinnerRecipes';
import FeatureShowcase from '@/components/FeatureShowcase';
import FeaturedMealPackages from '@/components/FeaturedMealPackages';
import TodayMealPlanWidget from '@/components/dashboard/TodayMealPlanWidget';
import ShoppingStatusManager from '@/components/ShoppingStatusManager';
import UnifiedShoppingListModal from '@/components/UnifiedShoppingListModal';
import ErrorBoundary from '@/components/ErrorBoundary';
import WelcomeGuide from '@/components/WelcomeGuide';
import QuickMealPlanModal from '@/components/QuickMealPlanModal';
import RecipeImportButton from '@/components/recipe/RecipeImportButton';
import { FloatingActionButton } from '@/components/ui/mobile-navigation';
import { TouchButton } from '@/components/ui/mobile-touch';
import { MobileRecipeCard } from '@/components/ui/mobile-cards';

import { useAuth } from '@/contexts/AuthContext';
import { useKitchen } from '@/contexts/KitchenContext';
import { useMobile } from '@/hooks/useMobile';
import { supabaseHelpers } from '@/config/supabase';
import { Plus, Search, Calendar } from 'lucide-react';

const Index = () => {
  const { isAuthenticated } = useAuth();
  const { todayMeals, dailyShoppingStatus } = useKitchen();
  const { isMobile } = useMobile();
  const [showShoppingModal, setShowShoppingModal] = useState(false);
  const [showQuickMealPlanModal, setShowQuickMealPlanModal] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState<any>(null);

  // Test Supabase connection on load
  useEffect(() => {
    const testConnection = async () => {
      try {
        console.log('🔍 Testing Supabase connection...');
        const result = await supabaseHelpers.testConnection();
        setConnectionStatus(result);
        console.log('Connection test result:', result);

        // If connection fails, try to setup database
        if (!result.success && result.details?.basicConnection) {
          console.log('🔧 Attempting to setup database...');
          const setupResult = await supabaseHelpers.setupDatabase();
          console.log('Database setup result:', setupResult);

          // Test connection again after setup
          if (setupResult.success) {
            const retestResult = await supabaseHelpers.testConnection();
            setConnectionStatus(retestResult);
          }
        }
      } catch (error) {
        console.error('Connection test error:', error);
        setConnectionStatus({ success: false, error: error.message });
      }
    };

    testConnection();
  }, []);

  const handleGoShopping = () => {
    setShowShoppingModal(true);
  };

  const handleStartCooking = () => {
    // Navigate to cooking mode or show cooking interface
    console.log('Starting cooking mode...');
  };

  const handleImportRecipe = (recipe: any) => {
    console.log('Imported recipe:', recipe);
    // In a real app, this would save to the database
    // For now, just show success message
    alert(`Đã nhập công thức: ${recipe.title}`);
  };

  const handleSetupDatabase = async () => {
    try {
      console.log('🔧 Manual database setup...');
      const setupResult = await supabaseHelpers.setupDatabase();
      console.log('Database setup result:', setupResult);

      // Test connection again after setup
      const retestResult = await supabaseHelpers.testConnection();
      setConnectionStatus(retestResult);
    } catch (error) {
      console.error('Database setup error:', error);
    }
  };

  // Mobile layout
  if (isMobile) {
    return (
      <MobileLayout showHeader={false} showBottomNav={true}>
        <ErrorBoundary>
          {/* Mobile Hero Section */}
          <div className="relative bg-gradient-to-br from-orange-50 to-red-50 px-4 py-8">
            <div className="text-center">
              <h1 className="text-3xl font-bold text-gray-900 mb-2">
                Angiday
              </h1>
              <p className="text-gray-600 mb-6">
                Lập kế hoạch bữa ăn thông minh
              </p>

              {/* Quick Actions */}
              <div className="flex gap-3 justify-center">
                <TouchButton variant="primary" size="lg">
                  <Search className="w-5 h-5 mr-2" />
                  Tìm công thức
                </TouchButton>
                <TouchButton variant="secondary" size="lg">
                  <Calendar className="w-5 h-5 mr-2" />
                  Lập kế hoạch
                </TouchButton>
              </div>
            </div>
          </div>

          {/* Today's Meal Plan Widget - Mobile */}
          {isAuthenticated && (
            <div className="px-4 py-6">
              <TodayMealPlanWidget />
            </div>
          )}

          {/* Mobile Popular Recipes */}
          <div className="px-4 py-6">
            <h2 className="text-xl font-bold text-gray-900 mb-4">Công thức phổ biến</h2>
            <div className="space-y-4">
              {/* Mock recipe cards for mobile */}
              <MobileRecipeCard
                id="1"
                title="Phở bò truyền thống"
                description="Món phở bò đậm đà hương vị truyền thống Việt Nam"
                image="https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=400&h=300&fit=crop"
                cookTime={120}
                servings={4}
                rating={4.8}
                difficulty="medium"
              />
              <MobileRecipeCard
                id="2"
                title="Cơm tấm sườn nướng"
                description="Cơm tấm thơm ngon với sườn nướng đậm đà"
                image="https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=400&h=300&fit=crop"
                cookTime={45}
                servings={2}
                rating={4.6}
                difficulty="easy"
              />
            </div>
          </div>

          {/* Mobile Feature Showcase */}
          <div className="px-4 py-6 bg-gray-50">
            <h2 className="text-xl font-bold text-gray-900 mb-4">Tính năng nổi bật</h2>
            <div className="grid grid-cols-2 gap-4">
              <div className="bg-white rounded-lg p-4 text-center">
                <div className="text-3xl mb-2">📱</div>
                <h3 className="font-semibold text-sm">Nấu ăn thông minh</h3>
              </div>
              <div className="bg-white rounded-lg p-4 text-center">
                <div className="text-3xl mb-2">🛒</div>
                <h3 className="font-semibold text-sm">Danh sách mua sắm</h3>
              </div>
              <div className="bg-white rounded-lg p-4 text-center">
                <div className="text-3xl mb-2">📅</div>
                <h3 className="font-semibold text-sm">Lập kế hoạch</h3>
              </div>
              <div className="bg-white rounded-lg p-4 text-center">
                <div className="text-3xl mb-2">🇻🇳</div>
                <h3 className="font-semibold text-sm">Món Việt</h3>
              </div>
            </div>
          </div>
        </ErrorBoundary>

        {/* Mobile Floating Action Button */}
        <FloatingActionButton
          icon={<Plus className="w-6 h-6" />}
          onClick={() => setShowQuickMealPlanModal(true)}
        />

        {/* Modals */}
        <UnifiedShoppingListModal
          isOpen={showShoppingModal}
          onClose={() => setShowShoppingModal(false)}
          dailyShoppingStatusId={dailyShoppingStatus?.id || ''}
          mode="enhanced"
          enablePriceTracking={true}
          enableCategoryBreakdown={true}
          enableExport={true}
        />

        <QuickMealPlanModal
          isOpen={showQuickMealPlanModal}
          onClose={() => setShowQuickMealPlanModal(false)}
          onApply={(planId) => {
            console.log('Applied meal plan from Index:', planId);
          }}
        />
      </MobileLayout>
    );
  }

  // Desktop layout
  return (
    <PublicLayout>
      <HeroSection />

      {/* Recipe Import Section */}
      <section className="py-8 bg-white border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex flex-col sm:flex-row items-center justify-between">
            <div className="mb-4 sm:mb-0">
              <h2 className="text-xl font-semibold text-gray-900">
                Có công thức yêu thích?
              </h2>
              <p className="text-gray-600">
                Nhập công thức từ bất kỳ trang web, văn bản hoặc file nào
              </p>
            </div>
            <RecipeImportButton
              onImport={handleImportRecipe}
              variant="default"
              size="default"
              showDropdown={true}
            />
          </div>
        </div>
      </section>

      {/* Today's Meal Plan Widget - Only show for authenticated users */}
      {isAuthenticated && (
        <section className="py-12 bg-gradient-to-br from-orange-50 to-green-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <TodayMealPlanWidget />
          </div>
        </section>
      )}

      <FeaturedMealPackages />
      <EasyDinnerRecipes />
      <FeatureShowcase />

      {/* Unified Shopping List Modal */}
      <UnifiedShoppingListModal
        isOpen={showShoppingModal}
        onClose={() => setShowShoppingModal(false)}
        dailyShoppingStatusId={dailyShoppingStatus?.id || ''}
        mode="enhanced"
        enablePriceTracking={true}
        enableCategoryBreakdown={true}
        enableExport={true}
      />

      {/* Welcome Guide for new users */}
      <WelcomeGuide
        onStartQuickSetup={() => setShowQuickMealPlanModal(true)}
      />

      {/* Quick Meal Plan Modal */}
      <QuickMealPlanModal
        isOpen={showQuickMealPlanModal}
        onClose={() => setShowQuickMealPlanModal(false)}
        onApply={(planId) => {
          console.log('Applied meal plan from Index:', planId);
        }}
      />
    </PublicLayout>
  );
};

export default Index;

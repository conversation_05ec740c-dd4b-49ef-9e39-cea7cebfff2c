import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { ArrowLeft, Shield, Trash2, Database } from 'lucide-react';
import { Link } from 'react-router-dom';
import Header from '@/components/Header';
import { BulkMenuMealManager } from '@/components/admin/BulkMenuMealManager';
import { BulkDeleteManager } from '@/components/shopping/BulkDeleteManager';

const AdminBulkManagementPage: React.FC = () => {
  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      <div className="max-w-7xl mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center space-x-4">
            <Button variant="ghost" size="icon" asChild>
              <Link to="/admin">
                <ArrowLeft className="h-5 w-5" />
              </Link>
            </Button>
            <div>
              <h1 className="text-3xl font-bold text-gray-900 flex items-center">
                <Shield className="h-8 w-8 mr-3 text-red-500" />
                Quản Lý Xóa Hàng Loạt
              </h1>
              <p className="text-gray-600 mt-1">
                Công cụ quản lý và xóa dữ liệu hàng loạt cho admin
              </p>
            </div>
          </div>
        </div>

        {/* Warning Banner */}
        <Card className="mb-8 border-red-200 bg-red-50">
          <CardContent className="p-6">
            <div className="flex items-start gap-4">
              <Trash2 className="h-6 w-6 text-red-600 mt-1" />
              <div>
                <h3 className="font-semibold text-red-800 mb-2">⚠️ Cảnh báo quan trọng</h3>
                <div className="text-red-700 space-y-1">
                  <p>• Tất cả các thao tác xóa đều <strong>KHÔNG THỂ HOÀN TÁC</strong></p>
                  <p>• Hãy kiểm tra kỹ trước khi thực hiện xóa</p>
                  <p>• Nên tạo backup dữ liệu trước khi thực hiện xóa hàng loạt</p>
                  <p>• Chỉ admin có quyền truy cập trang này</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Management Sections */}
        <div className="grid grid-cols-1 xl:grid-cols-2 gap-8">
          {/* Menu & Meal Management */}
          <div>
            <Card className="mb-6">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Database className="h-5 w-5 text-blue-500" />
                  Quản Lý Thực Đơn & Bữa Ăn
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600 mb-4">
                  Xóa hàng loạt món ăn, bữa ăn, thực đơn và kế hoạch tuần
                </p>
                <div className="space-y-2 text-sm text-gray-500">
                  <p>• Món ăn: Xóa các công thức nấu ăn</p>
                  <p>• Bữa ăn: Xóa các bữa ăn đã lên lịch</p>
                  <p>• Thực đơn: Xóa các thực đơn đã tạo</p>
                  <p>• Kế hoạch tuần: Xóa kế hoạch ăn uống theo tuần</p>
                </div>
              </CardContent>
            </Card>
            <BulkMenuMealManager />
          </div>

          {/* Shopping Cart Management */}
          <div>
            <Card className="mb-6">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Trash2 className="h-5 w-5 text-orange-500" />
                  Quản Lý Giỏ Hàng
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600 mb-4">
                  Xóa hàng loạt các mục trong giỏ hàng và nhóm mua sắm
                </p>
                <div className="space-y-2 text-sm text-gray-500">
                  <p>• Món trong giỏ: Xóa các nguyên liệu đã thêm</p>
                  <p>• Nhóm mua sắm: Xóa các session/nhóm</p>
                  <p>• Xóa toàn bộ: Làm sạch toàn bộ giỏ hàng</p>
                </div>
              </CardContent>
            </Card>
            <BulkDeleteManager />
          </div>
        </div>

        {/* Additional Tools */}
        <Card className="mt-8">
          <CardHeader>
            <CardTitle>Công Cụ Bổ Sung</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Button variant="outline" className="h-auto p-4 flex flex-col items-center gap-2">
                <Database className="h-6 w-6 text-blue-500" />
                <div className="text-center">
                  <div className="font-medium">Backup Dữ Liệu</div>
                  <div className="text-xs text-gray-500">Tạo bản sao lưu</div>
                </div>
              </Button>

              <Button variant="outline" className="h-auto p-4 flex flex-col items-center gap-2">
                <Trash2 className="h-6 w-6 text-red-500" />
                <div className="text-center">
                  <div className="font-medium">Xóa Dữ Liệu Cũ</div>
                  <div className="text-xs text-gray-500">Dọn dẹp tự động</div>
                </div>
              </Button>

              <Button variant="outline" className="h-auto p-4 flex flex-col items-center gap-2">
                <Shield className="h-6 w-6 text-green-500" />
                <div className="text-center">
                  <div className="font-medium">Khôi Phục</div>
                  <div className="text-xs text-gray-500">Phục hồi từ backup</div>
                </div>
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Usage Guidelines */}
        <Card className="mt-8">
          <CardHeader>
            <CardTitle>Hướng Dẫn Sử Dụng</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-semibold text-gray-900 mb-3">Quy trình xóa an toàn:</h4>
                <ol className="list-decimal list-inside space-y-2 text-sm text-gray-600">
                  <li>Tạo backup dữ liệu trước khi xóa</li>
                  <li>Kiểm tra kỹ các mục cần xóa</li>
                  <li>Sử dụng bộ lọc để tìm chính xác</li>
                  <li>Chọn từng mục hoặc chọn tất cả</li>
                  <li>Xác nhận xóa và theo dõi kết quả</li>
                </ol>
              </div>
              <div>
                <h4 className="font-semibold text-gray-900 mb-3">Lưu ý quan trọng:</h4>
                <ul className="list-disc list-inside space-y-2 text-sm text-gray-600">
                  <li>Không thể hoàn tác sau khi xóa</li>
                  <li>Xóa thực đơn sẽ ảnh hưởng đến lịch trình</li>
                  <li>Xóa món ăn sẽ ảnh hưởng đến thực đơn</li>
                  <li>Kiểm tra dependencies trước khi xóa</li>
                  <li>Thông báo cho team trước khi xóa dữ liệu quan trọng</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default AdminBulkManagementPage;
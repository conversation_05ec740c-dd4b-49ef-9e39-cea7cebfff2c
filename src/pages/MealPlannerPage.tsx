import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { 
  ArrowLeft, 
  Calendar, 
  ChefHat, 
  Lightbulb, 
  ShoppingCart,
  Download,
  Share2,
  Plus,
  Clock,
  Target
} from 'lucide-react';
import { Link, useSearchParams } from 'react-router-dom';
import Header from '@/components/Header';
import { WeeklyMealPlanner } from '@/components/meal-planner/WeeklyMealPlanner';
import { MealTemplateExplorer } from '@/components/meal-templates/MealTemplateExplorer';
import { MealTemplate, getMealTemplateById } from '@/data/vietnameseMealTemplates';
import { toast } from 'sonner';

const MealPlannerPage: React.FC = () => {
  const [searchParams] = useSearchParams();
  const [selectedTemplate, setSelectedTemplate] = useState<MealTemplate | null>(null);
  const [activeTab, setActiveTab] = useState('planner');

  // Check if template ID is provided in URL
  useEffect(() => {
    const templateId = searchParams.get('template');
    if (templateId) {
      const template = getMealTemplateById(templateId);
      if (template) {
        setSelectedTemplate(template);
        setActiveTab('planner');
        toast.success(`Đã chọn thực đơn "${template.name}"`);
      }
    }
  }, [searchParams]);

  const handleSelectTemplate = (template: MealTemplate) => {
    setSelectedTemplate(template);
    setActiveTab('planner');
    toast.success(`Đã chọn thực đơn "${template.name}"`);
  };

  const handleCreateNewPlan = () => {
    setSelectedTemplate(null);
    setActiveTab('planner');
    toast.success('Đã tạo kế hoạch tuần mới');
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      <div className="max-w-7xl mx-auto px-4 py-8">
        {/* Page Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center space-x-4">
            <Button variant="ghost" size="icon" asChild>
              <Link to="/">
                <ArrowLeft className="h-5 w-5" />
              </Link>
            </Button>
            <div>
              <h1 className="text-3xl font-bold text-gray-900 flex items-center">
                <Calendar className="h-8 w-8 mr-3 text-blue-500" />
                Lập Kế Hoạch Bữa Ăn
              </h1>
              <p className="text-gray-600 mt-1">
                Tạo thực đơn tuần và danh sách mua sắm thông minh
              </p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm">
              <Download className="h-4 w-4 mr-2" />
              Xuất PDF
            </Button>
            <Button variant="outline" size="sm">
              <Share2 className="h-4 w-4 mr-2" />
              Chia sẻ
            </Button>
          </div>
        </div>

        {/* Quick Actions */}
        <Card className="mb-8 border-blue-200 bg-blue-50">
          <CardContent className="p-6">
            <div className="flex items-start gap-4">
              <Lightbulb className="h-6 w-6 text-blue-600 mt-1" />
              <div className="flex-1">
                <h3 className="font-semibold text-blue-800 mb-2">🎯 Bắt đầu nhanh</h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <Button 
                    variant="outline" 
                    className="h-auto p-4 flex flex-col items-center gap-2 bg-white hover:bg-blue-50"
                    onClick={() => setActiveTab('templates')}
                  >
                    <ChefHat className="h-6 w-6 text-orange-500" />
                    <div className="text-center">
                      <div className="font-medium">Chọn thực đơn mẫu</div>
                      <div className="text-xs text-gray-500">Áp dụng ngay trong 1 click</div>
                    </div>
                  </Button>
                  
                  <Button 
                    variant="outline" 
                    className="h-auto p-4 flex flex-col items-center gap-2 bg-white hover:bg-blue-50"
                    onClick={handleCreateNewPlan}
                  >
                    <Plus className="h-6 w-6 text-green-500" />
                    <div className="text-center">
                      <div className="font-medium">Tạo kế hoạch mới</div>
                      <div className="text-xs text-gray-500">Bắt đầu từ đầu</div>
                    </div>
                  </Button>
                  
                  <Button 
                    variant="outline" 
                    className="h-auto p-4 flex flex-col items-center gap-2 bg-white hover:bg-blue-50"
                    onClick={() => setActiveTab('planner')}
                  >
                    <ShoppingCart className="h-6 w-6 text-blue-500" />
                    <div className="text-center">
                      <div className="font-medium">Xem danh sách mua</div>
                      <div className="text-xs text-gray-500">Từ kế hoạch hiện tại</div>
                    </div>
                  </Button>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Current Template Info */}
        {selectedTemplate && (
          <Card className="mb-6 border-green-200 bg-green-50">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <Target className="h-5 w-5 text-green-600" />
                  <div>
                    <div className="font-medium text-green-800">
                      Đang áp dụng: {selectedTemplate.name}
                    </div>
                    <div className="text-sm text-green-700">
                      {selectedTemplate.description}
                    </div>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant="outline" className="bg-white">
                    <Clock className="h-3 w-3 mr-1" />
                    {selectedTemplate.totalTime}
                  </Badge>
                  <Button 
                    variant="ghost" 
                    size="sm"
                    onClick={() => setSelectedTemplate(null)}
                    className="text-green-700 hover:text-green-800"
                  >
                    Bỏ chọn
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Main Content */}
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="planner" className="flex items-center gap-2">
              <Calendar className="h-4 w-4" />
              Lập Kế Hoạch Tuần
            </TabsTrigger>
            <TabsTrigger value="templates" className="flex items-center gap-2">
              <ChefHat className="h-4 w-4" />
              Thực Đơn Mẫu
            </TabsTrigger>
          </TabsList>

          <TabsContent value="planner" className="mt-6">
            <WeeklyMealPlanner 
              initialTemplate={selectedTemplate || undefined}
              key={selectedTemplate?.id || 'new'}
            />
          </TabsContent>

          <TabsContent value="templates" className="mt-6">
            <MealTemplateExplorer onSelectTemplate={handleSelectTemplate} />
          </TabsContent>
        </Tabs>

        {/* How it works */}
        <Card className="mt-8">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Lightbulb className="h-5 w-5 text-blue-500" />
              Cách Sử Dụng Meal Planner
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="text-center">
                <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
                  <span className="text-xl font-bold text-blue-600">1</span>
                </div>
                <h4 className="font-semibold text-gray-900 mb-2">Chọn Cách Bắt Đầu</h4>
                <p className="text-sm text-gray-600">
                  Áp dụng thực đơn mẫu có sẵn hoặc tạo kế hoạch mới từ đầu
                </p>
              </div>
              <div className="text-center">
                <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3">
                  <span className="text-xl font-bold text-green-600">2</span>
                </div>
                <h4 className="font-semibold text-gray-900 mb-2">Kéo Thả Món Ăn</h4>
                <p className="text-sm text-gray-600">
                  Kéo món ăn từ danh sách vào các khung thời gian: Sáng, Trưa, Tối, Ăn vặt
                </p>
              </div>
              <div className="text-center">
                <div className="w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-3">
                  <span className="text-xl font-bold text-orange-600">3</span>
                </div>
                <h4 className="font-semibold text-gray-900 mb-2">Xuất Danh Sách</h4>
                <p className="text-sm text-gray-600">
                  Hệ thống tự động tạo danh sách mua sắm với nguyên liệu được gộp và quy đổi
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Features */}
        <Card className="mt-8">
          <CardHeader>
            <CardTitle>Tính Năng Nổi Bật</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-semibold text-gray-900 mb-3">🎯 Thông minh & Tiện lợi:</h4>
                <ul className="space-y-2 text-sm text-gray-600">
                  <li>• Kéo thả trực quan, dễ sử dụng</li>
                  <li>• Tự động tính toán chi phí và thời gian</li>
                  <li>• Gợi ý món ăn phù hợp theo bữa</li>
                  <li>• Lưu và chia sẻ kế hoạch</li>
                </ul>
              </div>
              <div>
                <h4 className="font-semibold text-gray-900 mb-3">📊 Thống kê & Báo cáo:</h4>
                <ul className="space-y-2 text-sm text-gray-600">
                  <li>• Tổng chi phí và thời gian nấu ăn</li>
                  <li>• Thông tin dinh dưỡng tổng hợp</li>
                  <li>• Danh sách mua sắm tự động</li>
                  <li>• Xuất PDF để in hoặc chia sẻ</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default MealPlannerPage;

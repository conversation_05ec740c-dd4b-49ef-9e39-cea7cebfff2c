import React from 'react';
import { ArrowLeft } from 'lucide-react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import DebugPanel from '@/components/DebugPanel';

const DebugPage: React.FC = () => {
  const handleGoBack = () => {
    window.history.back();
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="container mx-auto px-4">
        <div className="mb-6">
          <Button 
            onClick={handleGoBack}
            variant="outline"
            className="mb-4"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Quay lại
          </Button>
          
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Connection Debug
          </h1>
          <p className="text-gray-600">
            Kiểm tra trạng thái kết nối và vị trí lưu trữ dữ liệu
          </p>
        </div>
        
        <DebugPanel />
        
        <div className="mt-8 p-4 bg-white rounded-lg border">
          <h2 className="text-lg font-semibold mb-3">Hướng dẫn sử dụng</h2>
          <div className="space-y-2 text-sm text-gray-600">
            <p>• <strong>Refresh Debug:</strong> Cập nhật thông tin trạng thái kết nối</p>
            <p>• <strong>Setup Database:</strong> Tạo tables trong Supabase (chỉ hiện khi cần)</p>
            <p>• Kiểm tra Console (F12) để xem log chi tiết</p>
            <p>• Dữ liệu hiện tại có thể được lưu ở localStorage hoặc Supabase</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DebugPage;

import React from 'react';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import DataImporter from '@/components/DataImporter';

const DataImportPage: React.FC = () => {
  return (
    <div className="min-h-screen bg-gray-50">
      <Header />

      {/* Breadcrumb */}
      <nav className="bg-white border-b">
        <div className="max-w-7xl mx-auto px-4 py-3">
          <div className="flex items-center space-x-2 text-sm text-gray-600">
            <a href="/" className="hover:text-orange-600">Trang chủ</a>
            <span>/</span>
            <span className="text-gray-900">Data Import</span>
          </div>
        </div>
      </nav>

      <main className="py-8">
        <div className="max-w-7xl mx-auto px-4">
          <div className="text-center mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">
              Data Import Tool
            </h1>
            <p className="text-gray-600">
              Import dữ liệu mẫu vào Supabase database
            </p>
          </div>
          
          <DataImporter />
        </div>
      </main>
      
      <Footer />
    </div>
  );
};

export default DataImportPage;

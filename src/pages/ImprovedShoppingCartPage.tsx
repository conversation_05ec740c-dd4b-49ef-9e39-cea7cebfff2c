import React from 'react';
import PublicLayout from '@/components/layouts/PublicLayout';
import ImprovedShoppingCart from '@/components/shopping/ImprovedShoppingCart';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  ShoppingCart, 
  CheckCircle, 
  Package, 
  Users, 
  Star,
  ArrowRight,
  Lightbulb
} from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { useNavigate } from 'react-router-dom';

const ImprovedShoppingCartPage: React.FC = () => {
  const { isAuthenticated } = useAuth();
  const navigate = useNavigate();

  if (!isAuthenticated) {
    return (
      <PublicLayout>
        <div className="min-h-screen bg-gray-50 flex items-center justify-center">
          <Card className="max-w-md">
            <CardContent className="p-8 text-center">
              <ShoppingCart className="h-16 w-16 text-gray-300 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Cần đăng nhập
              </h3>
              <p className="text-gray-600 mb-4">
                Vui lòng đăng nhập để sử dụng tính năng giỏ hàng thông minh
              </p>
              <Button onClick={() => navigate('/login')}>
                Đăng nhập
              </Button>
            </CardContent>
          </Card>
        </div>
      </PublicLayout>
    );
  }

  return (
    <PublicLayout>
      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <section className="py-12 bg-gradient-to-br from-green-50 to-blue-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center">
              <h1 className="text-4xl font-bold text-gray-900 mb-4">
                🛒 Giỏ Hàng Thông Minh
              </h1>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                Tổ chức theo nhóm, chọn lọc thông minh, tạo danh sách mua sắm tối ưu
              </p>
            </div>
          </div>
        </section>

        {/* Features Overview */}
        <section className="py-8">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid md:grid-cols-3 gap-6 mb-8">
              <Card className="border-green-200 bg-green-50">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-green-800">
                    <Package className="h-5 w-5" />
                    Tổ Chức Theo Nhóm
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2 text-sm text-green-700">
                    <li>• Mỗi lần thêm tạo nhóm riêng</li>
                    <li>• Màu sắc và icon phân biệt</li>
                    <li>• Collapse/expand dễ dàng</li>
                    <li>• Xóa từng nhóm độc lập</li>
                  </ul>
                </CardContent>
              </Card>

              <Card className="border-blue-200 bg-blue-50">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-blue-800">
                    <CheckCircle className="h-5 w-5" />
                    Chọn Lọc Thông Minh
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2 text-sm text-blue-700">
                    <li>• Checkbox cho từng nhóm</li>
                    <li>• Chọn/bỏ chọn tất cả</li>
                    <li>• Tính tổng tiền theo lựa chọn</li>
                    <li>• Visual feedback rõ ràng</li>
                  </ul>
                </CardContent>
              </Card>

              <Card className="border-purple-200 bg-purple-50">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-purple-800">
                    <Star className="h-5 w-5" />
                    Danh Sách Tối Ưu
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2 text-sm text-purple-700">
                    <li>• Gộp nguyên liệu trùng lặp</li>
                    <li>• Phân loại theo danh mục</li>
                    <li>• Hiển thị nguồn gốc</li>
                    <li>• Xuất PDF/chia sẻ</li>
                  </ul>
                </CardContent>
              </Card>
            </div>
          </div>
        </section>

        {/* Main Shopping Cart */}
        <section className="py-8">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <ImprovedShoppingCart />
          </div>
        </section>

        {/* How to Use */}
        <section className="py-12 bg-white">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-8">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">
                💡 Cách Sử Dụng
              </h2>
              <p className="text-lg text-gray-600">
                Hướng dẫn sử dụng giỏ hàng thông minh hiệu quả
              </p>
            </div>

            <div className="grid md:grid-cols-2 gap-8">
              <div className="space-y-6">
                <div className="flex items-start gap-4">
                  <div className="bg-green-100 rounded-full p-2 flex-shrink-0">
                    <span className="text-green-600 font-bold">1</span>
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900 mb-2">Thêm Món Vào Giỏ</h3>
                    <p className="text-gray-600 text-sm">
                      Từ trang chủ, bấm nút "Thêm vào giỏ" trên món ăn, bữa ăn hoặc thực đơn. 
                      Mỗi lần thêm sẽ tạo một nhóm riêng với màu sắc và icon phân biệt.
                    </p>
                  </div>
                </div>

                <div className="flex items-start gap-4">
                  <div className="bg-blue-100 rounded-full p-2 flex-shrink-0">
                    <span className="text-blue-600 font-bold">2</span>
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900 mb-2">Chọn Nhóm Cần Mua</h3>
                    <p className="text-gray-600 text-sm">
                      Sử dụng checkbox để chọn những nhóm bạn muốn mua. 
                      Có thể chọn tất cả hoặc chỉ một vài nhóm cụ thể.
                    </p>
                  </div>
                </div>

                <div className="flex items-start gap-4">
                  <div className="bg-purple-100 rounded-full p-2 flex-shrink-0">
                    <span className="text-purple-600 font-bold">3</span>
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900 mb-2">Tạo Danh Sách Mua Sắm</h3>
                    <p className="text-gray-600 text-sm">
                      Bấm "Tạo danh sách mua sắm" để gộp và tối ưu hóa. 
                      Hệ thống sẽ gộp nguyên liệu trùng và phân loại theo danh mục.
                    </p>
                  </div>
                </div>
              </div>

              <div className="space-y-6">
                <div className="flex items-start gap-4">
                  <div className="bg-orange-100 rounded-full p-2 flex-shrink-0">
                    <span className="text-orange-600 font-bold">4</span>
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900 mb-2">Quản Lý Linh Hoạt</h3>
                    <p className="text-gray-600 text-sm">
                      Điều chỉnh số lượng, xóa món không cần, 
                      collapse/expand nhóm để dễ quản lý.
                    </p>
                  </div>
                </div>

                <div className="flex items-start gap-4">
                  <div className="bg-red-100 rounded-full p-2 flex-shrink-0">
                    <span className="text-red-600 font-bold">5</span>
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900 mb-2">Xuất & Chia Sẻ</h3>
                    <p className="text-gray-600 text-sm">
                      Xuất danh sách ra PDF hoặc chia sẻ với người khác. 
                      Tick vào món đã mua khi đi chợ.
                    </p>
                  </div>
                </div>

                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                  <div className="flex items-center gap-2 mb-2">
                    <Lightbulb className="h-4 w-4 text-yellow-600" />
                    <span className="font-semibold text-yellow-800">Mẹo hay</span>
                  </div>
                  <p className="text-yellow-700 text-sm">
                    Sử dụng QuickCartWidget ở góc phải dưới để truy cập nhanh giỏ hàng 
                    và thực hiện các thao tác cơ bản mà không cần vào trang chi tiết.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Call to Action */}
        <section className="py-12 bg-gradient-to-br from-green-50 to-blue-50">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Bắt Đầu Tạo Danh Sách Mua Sắm Thông Minh
            </h2>
            <p className="text-lg text-gray-600 mb-8">
              Quay về trang chủ để thêm món ăn vào giỏ và trải nghiệm tính năng mới
            </p>
            <div className="flex items-center justify-center gap-4">
              <Button
                onClick={() => navigate('/')}
                className="bg-green-600 hover:bg-green-700 text-white"
              >
                <ArrowRight className="h-4 w-4 mr-2" />
                Về Trang Chủ
              </Button>
              <Button
                onClick={() => navigate('/add-to-cart-demo')}
                variant="outline"
              >
                Xem Demo
              </Button>
            </div>
          </div>
        </section>
      </div>
    </PublicLayout>
  );
};

export default ImprovedShoppingCartPage;

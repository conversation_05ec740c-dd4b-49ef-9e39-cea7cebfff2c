import React, { useEffect, useState } from 'react';
import { AdapterFactory } from '@/services/adapters/AdapterFactory';
import { supabaseHelpers } from '@/config/supabase';
import { DataMigrationService } from '@/services/DataMigrationService';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { 
  CheckCircle, 
  AlertTriangle, 
  RefreshCw, 
  Database,
  Cloud,
  HardDrive
} from 'lucide-react';

interface SupabaseInitializerProps {
  children: React.ReactNode;
}

interface InitializationState {
  stage: 'checking' | 'connecting' | 'setting-up' | 'migrating' | 'ready' | 'error';
  progress: number;
  message: string;
  error?: string;
  showMigrationPrompt?: boolean;
  migrationData?: {
    localCount: number;
    supabaseCount: number;
  };
}

const SupabaseInitializer: React.FC<SupabaseInitializerProps> = ({ children }) => {
  const [state, setState] = useState<InitializationState>({
    stage: 'checking',
    progress: 0,
    message: 'Initializing...'
  });

  const updateState = (updates: Partial<InitializationState>) => {
    setState(prev => ({ ...prev, ...updates }));
  };

  const initializeSupabase = async () => {
    try {
      // Stage 1: Check current connection
      updateState({
        stage: 'checking',
        progress: 10,
        message: 'Checking Supabase connection...'
      });

      const connectionTest = await supabaseHelpers.testConnection();
      
      if (connectionTest.success) {
        updateState({
          stage: 'ready',
          progress: 100,
          message: 'Supabase is ready!'
        });
        
        // Check for migration needs
        await checkMigrationNeeds();
        return;
      }

      // Stage 2: Try to connect with enhanced adapter
      updateState({
        stage: 'connecting',
        progress: 30,
        message: 'Attempting enhanced connection...'
      });

      try {
        const enhancedAdapter = await AdapterFactory.createSupabaseAdapterWithRetry({
          retryAttempts: 3,
          autoSetupDatabase: true,
          fallbackToLocalStorage: false
        });

        updateState({
          stage: 'ready',
          progress: 100,
          message: 'Supabase connected successfully!'
        });

        // Check for migration needs
        await checkMigrationNeeds();
        return;

      } catch (enhancedError) {
        console.log('Enhanced adapter failed, trying database setup...');
        
        // Stage 3: Try database setup
        updateState({
          stage: 'setting-up',
          progress: 60,
          message: 'Setting up database tables...'
        });

        const setupResult = await supabaseHelpers.setupDatabase();
        
        if (setupResult.success) {
          // Test connection again after setup
          const retestResult = await supabaseHelpers.testConnection();
          
          if (retestResult.success) {
            updateState({
              stage: 'ready',
              progress: 100,
              message: 'Database setup completed successfully!'
            });
            
            await checkMigrationNeeds();
            return;
          }
        }

        throw new Error(`Database setup failed: ${setupResult.message}`);
      }

    } catch (error) {
      console.error('Supabase initialization failed:', error);
      updateState({
        stage: 'error',
        progress: 0,
        message: 'Failed to initialize Supabase',
        error: error.message
      });
    }
  };

  const checkMigrationNeeds = async () => {
    try {
      const migrationService = new DataMigrationService();
      const migrationCheck = await migrationService.checkMigrationNeeded();
      
      if (migrationCheck.needed) {
        updateState({
          showMigrationPrompt: true,
          migrationData: {
            localCount: migrationCheck.localDataCount,
            supabaseCount: migrationCheck.supabaseDataCount
          }
        });
      }
    } catch (error) {
      console.warn('Could not check migration needs:', error);
    }
  };

  const handleAutoMigration = async () => {
    updateState({
      stage: 'migrating',
      progress: 0,
      message: 'Migrating data to Supabase...',
      showMigrationPrompt: false
    });

    try {
      const migrationService = new DataMigrationService((progress) => {
        updateState({
          progress: progress.progress,
          message: progress.message
        });
      });

      const result = await migrationService.migrateData({
        includeRecipes: true,
        includeMealPlans: true,
        includeShoppingLists: true,
        clearLocalStorageAfter: false
      });

      if (result.success) {
        updateState({
          stage: 'ready',
          progress: 100,
          message: `Migration completed! ${result.details.recipesCount + result.details.mealPlansCount + result.details.shoppingListsCount} items migrated.`
        });
      } else {
        throw new Error(result.message);
      }
    } catch (error) {
      updateState({
        stage: 'error',
        progress: 0,
        message: 'Migration failed',
        error: error.message
      });
    }
  };

  const dismissMigrationPrompt = () => {
    updateState({ showMigrationPrompt: false });
  };

  const retryInitialization = () => {
    setState({
      stage: 'checking',
      progress: 0,
      message: 'Retrying initialization...'
    });
    initializeSupabase();
  };

  useEffect(() => {
    initializeSupabase();
  }, []);

  // Show initialization UI if not ready
  if (state.stage !== 'ready' || state.showMigrationPrompt) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
        <div className="max-w-md w-full space-y-4">
          <div className="text-center">
            <div className="flex justify-center mb-4">
              {state.stage === 'error' ? (
                <AlertTriangle className="h-12 w-12 text-red-500" />
              ) : state.stage === 'ready' ? (
                <CheckCircle className="h-12 w-12 text-green-500" />
              ) : (
                <Database className="h-12 w-12 text-blue-500 animate-pulse" />
              )}
            </div>
            
            <h1 className="text-2xl font-bold text-gray-900 mb-2">
              Angiday Kitchen
            </h1>
            
            <p className="text-gray-600 mb-4">
              {state.message}
            </p>

            {state.stage !== 'error' && state.stage !== 'ready' && (
              <div className="space-y-2">
                <Progress value={state.progress} className="w-full" />
                <p className="text-sm text-gray-500">
                  {state.progress}% complete
                </p>
              </div>
            )}
          </div>

          {state.stage === 'error' && (
            <Alert className="border-red-200 bg-red-50">
              <AlertTriangle className="h-4 w-4 text-red-600" />
              <AlertDescription>
                <div className="font-medium text-red-800 mb-2">
                  Initialization Failed
                </div>
                <div className="text-sm text-red-700 mb-3">
                  {state.error}
                </div>
                <Button 
                  onClick={retryInitialization}
                  size="sm"
                  className="w-full"
                >
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Retry
                </Button>
              </AlertDescription>
            </Alert>
          )}

          {state.showMigrationPrompt && (
            <Alert className="border-blue-200 bg-blue-50">
              <Database className="h-4 w-4 text-blue-600" />
              <AlertDescription>
                <div className="font-medium text-blue-800 mb-2">
                  Data Migration Available
                </div>
                <div className="text-sm text-blue-700 mb-3">
                  Found {state.migrationData?.localCount} items in localStorage. 
                  Would you like to migrate them to Supabase for better sync?
                </div>
                <div className="flex gap-2">
                  <Button 
                    onClick={handleAutoMigration}
                    size="sm"
                    className="flex-1"
                  >
                    <Cloud className="h-4 w-4 mr-2" />
                    Migrate Now
                  </Button>
                  <Button 
                    onClick={dismissMigrationPrompt}
                    variant="outline"
                    size="sm"
                    className="flex-1"
                  >
                    <HardDrive className="h-4 w-4 mr-2" />
                    Keep Local
                  </Button>
                </div>
              </AlertDescription>
            </Alert>
          )}

          <div className="text-center text-xs text-gray-500">
            Prioritizing Supabase for better data sync and backup
          </div>
        </div>
      </div>
    );
  }

  // Render children when ready
  return <>{children}</>;
};

export default SupabaseInitializer;

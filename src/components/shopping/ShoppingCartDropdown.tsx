import React from 'react';
import { Link } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { 
  ShoppingCart, 
  Trash2, 
  Plus, 
  Minus,
  ArrowRight,
  Package
} from 'lucide-react';
import { useShoppingCart } from '@/contexts/ShoppingCartContext';
import { formatVNDPrice } from '@/utils/vndPriceUtils';

interface ShoppingCartDropdownProps {
  children: React.ReactNode;
}

const ShoppingCartDropdown: React.FC<ShoppingCartDropdownProps> = ({ children }) => {
  const {
    sessions,
    items,
    totalItems,
    totalEstimatedCost,
    removeItem,
    updateItemQuantity,
    clearCart
  } = useShoppingCart();

  const getCategoryIcon = (category: string) => {
    const icons = {
      'Thịt & Hải sản': '🥩',
      '<PERSON>u củ quả': '🥕',
      'Gia vị & Condiments': '🧂',
      'Ngũ cốc & Tinh bột': '🌾',
      'Sữa & Trứng': '🥛',
      'Đồ khô & Hạt': '🥜',
      'Đồ uống': '🥤',
      'Khác': '📦'
    };
    return icons[category] || '📦';
  };

  if (totalItems === 0) {
    return (
      <Popover>
        <PopoverTrigger asChild>
          {children}
        </PopoverTrigger>
        <PopoverContent className="w-80" align="end">
          <div className="text-center py-8">
            <ShoppingCart className="h-12 w-12 text-gray-300 mx-auto mb-4" />
            <h3 className="font-semibold text-gray-900 mb-2">Giỏ hàng trống</h3>
            <p className="text-sm text-gray-600 mb-4">
              Thêm nguyên liệu từ công thức hoặc thực đơn
            </p>
            <Button asChild size="sm">
              <Link to="/recipes">
                Xem công thức
              </Link>
            </Button>
          </div>
        </PopoverContent>
      </Popover>
    );
  }

  return (
    <Popover>
      <PopoverTrigger asChild>
        {children}
      </PopoverTrigger>
      <PopoverContent className="w-96" align="end">
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="font-semibold text-gray-900 flex items-center">
              <ShoppingCart className="h-4 w-4 mr-2" />
              Giỏ hàng ({totalItems})
            </h3>
            <Button
              variant="ghost"
              size="sm"
              onClick={clearCart}
              className="text-red-500 hover:text-red-700 hover:bg-red-50"
            >
              <Trash2 className="h-3 w-3 mr-1" />
              Xóa tất cả
            </Button>
          </div>

          <Separator />

          <ScrollArea className="max-h-64">
            <div className="space-y-3">
              {sessions.slice(0, 3).map((session) => (
                <div key={session.id} className="border-l-2 border-orange-200 pl-3">
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="text-xs font-medium text-gray-700 truncate">
                      {session.name}
                    </h4>
                    <Badge variant="outline" className="text-xs">
                      {session.items.length}
                    </Badge>
                  </div>
                  {session.items.slice(0, 2).map((item) => (
                    <div key={item.id} className="flex items-center space-x-2 p-2 rounded-lg hover:bg-gray-50">
                      <span className="text-sm">{getCategoryIcon(item.category)}</span>
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-gray-900 truncate">
                          {item.name}
                        </p>
                        <div className="flex items-center space-x-2 mt-1">
                          <span className="text-xs text-gray-600">
                            {item.quantity} {item.unit}
                          </span>
                          <Badge variant="outline" className="text-xs">
                            {formatVNDPrice(item.estimatedPrice || 0)}
                          </Badge>
                        </div>
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-6 w-6 p-0 text-red-500 hover:text-red-700"
                        onClick={() => removeItem(item.id)}
                      >
                        <Trash2 className="h-3 w-3" />
                      </Button>
                    </div>
                  ))}
                  {session.items.length > 2 && (
                    <p className="text-xs text-gray-400 mt-1">
                      và {session.items.length - 2} món khác...
                    </p>
                  )}
                </div>
              ))}
              
              {sessions.length > 3 && (
                <div className="text-center py-2">
                  <p className="text-xs text-gray-500">
                    và {sessions.length - 3} nhóm khác...
                  </p>
                </div>
              )}
            </div>
          </ScrollArea>

          <Separator />

          <div className="space-y-2">
            <div className="flex items-center justify-between text-sm">
              <span className="text-gray-600">Tổng ước tính:</span>
              <span className="font-semibold text-gray-900">
                {formatVNDPrice(totalEstimatedCost)}
              </span>
            </div>
          </div>

          <div className="flex space-x-2">
            <Button variant="outline" size="sm" className="flex-1" asChild>
              <Link to="/shopping-cart">
                <Package className="h-3 w-3 mr-1" />
                Xem chi tiết
              </Link>
            </Button>
            <Button size="sm" className="flex-1">
              <ArrowRight className="h-3 w-3 mr-1" />
              Đi mua sắm
            </Button>
          </div>
        </div>
      </PopoverContent>
    </Popover>
  );
};

export default ShoppingCartDropdown;

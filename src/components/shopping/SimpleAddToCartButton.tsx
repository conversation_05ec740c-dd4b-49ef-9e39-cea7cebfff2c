import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { ShoppingCart, Plus } from 'lucide-react';
import { toast } from 'sonner';

interface SimpleAddToCartButtonProps {
  recipeName: string;
  ingredients?: string[];
  variant?: 'default' | 'outline' | 'ghost' | 'secondary';
  size?: 'sm' | 'default' | 'lg';
  className?: string;
  children?: React.ReactNode;
}

const SimpleAddToCartButton: React.FC<SimpleAddToCartButtonProps> = ({
  recipeName,
  ingredients = [],
  variant = 'outline',
  size = 'sm',
  className = '',
  children
}) => {
  const [isAdding, setIsAdding] = useState(false);

  const handleAddToCart = async () => {
    setIsAdding(true);
    
    try {
      // Simple localStorage implementation
      const existingCart = localStorage.getItem('simple_shopping_cart');
      const cart = existingCart ? JSON.parse(existingCart) : [];
      
      // Add recipe info to cart
      const cartItem = {
        id: Date.now().toString(),
        recipeName,
        ingredients,
        addedAt: new Date().toISOString()
      };
      
      cart.push(cartItem);
      localStorage.setItem('simple_shopping_cart', JSON.stringify(cart));

      // Trigger cart update event
      window.dispatchEvent(new Event('cartUpdated'));

      toast.success(`Đã thêm "${recipeName}" vào giỏ hàng`);
    } catch (error) {
      console.error('Error adding to cart:', error);
      toast.error('Không thể thêm vào giỏ hàng');
    } finally {
      setIsAdding(false);
    }
  };

  const buttonContent = children || (
    <>
      <ShoppingCart className="h-4 w-4 mr-2" />
      Thêm vào giỏ
    </>
  );

  return (
    <Button
      variant={variant}
      size={size}
      className={className}
      onClick={handleAddToCart}
      disabled={isAdding}
    >
      {isAdding ? (
        <>
          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-2" />
          Đang thêm...
        </>
      ) : (
        buttonContent
      )}
    </Button>
  );
};

export default SimpleAddToCartButton;

import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Trash2 } from 'lucide-react';
import { useShoppingCart } from '@/contexts/ShoppingCartContext';
import { toast } from 'sonner';

// Bulk Delete Management Component
interface BulkDeleteManagerProps {
  className?: string;
}

export const BulkDeleteManager: React.FC<BulkDeleteManagerProps> = ({ className }) => {
  const {
    sessions,
    items,
    removeSession,
    removeItem,
    clearCart
  } = useShoppingCart();

  const [selectedItems, setSelectedItems] = useState<Set<string>>(new Set());
  const [selectedSessions, setSelectedSessions] = useState<Set<string>>(new Set());
  const [deleteMode, setDeleteMode] = useState<'items' | 'sessions' | 'all'>('items');

  const handleSelectItem = (itemId: string, checked: boolean) => {
    const newSelected = new Set(selectedItems);
    if (checked) {
      newSelected.add(itemId);
    } else {
      newSelected.delete(itemId);
    }
    setSelectedItems(newSelected);
  };

  const handleSelectSession = (sessionId: string, checked: boolean) => {
    const newSelected = new Set(selectedSessions);
    if (checked) {
      newSelected.add(sessionId);
    } else {
      newSelected.delete(sessionId);
    }
    setSelectedSessions(newSelected);
  };

  const handleSelectAllItems = () => {
    setSelectedItems(new Set(items.map(item => item.id)));
  };

  const handleSelectAllSessions = () => {
    setSelectedSessions(new Set(sessions.map(session => session.id)));
  };

  const handleClearSelection = () => {
    setSelectedItems(new Set());
    setSelectedSessions(new Set());
  };

  const handleBulkDelete = () => {
    if (deleteMode === 'items' && selectedItems.size > 0) {
      if (confirm(`Bạn có chắc chắn muốn xóa ${selectedItems.size} món đã chọn?`)) {
        selectedItems.forEach(itemId => removeItem(itemId));
        setSelectedItems(new Set());
        toast.success(`Đã xóa ${selectedItems.size} món thành công`);
      }
    } else if (deleteMode === 'sessions' && selectedSessions.size > 0) {
      if (confirm(`Bạn có chắc chắn muốn xóa ${selectedSessions.size} nhóm đã chọn?`)) {
        selectedSessions.forEach(sessionId => removeSession(sessionId));
        setSelectedSessions(new Set());
        toast.success(`Đã xóa ${selectedSessions.size} nhóm thành công`);
      }
    } else if (deleteMode === 'all') {
      if (confirm('Bạn có chắc chắn muốn xóa toàn bộ giỏ hàng?')) {
        clearCart();
        setSelectedItems(new Set());
        setSelectedSessions(new Set());
        toast.success('Đã xóa toàn bộ giỏ hàng');
      }
    }
  };

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Trash2 className="h-5 w-5 text-red-500" />
          Quản Lý Xóa Hàng Loạt
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {/* Delete Mode Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Chế độ xóa
            </label>
            <Select value={deleteMode} onValueChange={(value: 'items' | 'sessions' | 'all') => setDeleteMode(value)}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="items">Xóa món ăn đã chọn</SelectItem>
                <SelectItem value="sessions">Xóa nhóm đã chọn</SelectItem>
                <SelectItem value="all">Xóa toàn bộ giỏ hàng</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Selection Controls */}
          {deleteMode !== 'all' && (
            <div className="flex gap-2">
              {deleteMode === 'items' && (
                <>
                  <Button variant="outline" size="sm" onClick={handleSelectAllItems}>
                    Chọn tất cả món ({items.length})
                  </Button>
                  <Button variant="outline" size="sm" onClick={handleClearSelection}>
                    Bỏ chọn tất cả
                  </Button>
                </>
              )}
              {deleteMode === 'sessions' && (
                <>
                  <Button variant="outline" size="sm" onClick={handleSelectAllSessions}>
                    Chọn tất cả nhóm ({sessions.length})
                  </Button>
                  <Button variant="outline" size="sm" onClick={handleClearSelection}>
                    Bỏ chọn tất cả
                  </Button>
                </>
              )}
            </div>
          )}

          {/* Selection Summary */}
          <div className="bg-gray-50 p-3 rounded-lg">
            {deleteMode === 'items' && (
              <p className="text-sm text-gray-600">
                Đã chọn: <span className="font-semibold">{selectedItems.size}</span> / {items.length} món
              </p>
            )}
            {deleteMode === 'sessions' && (
              <p className="text-sm text-gray-600">
                Đã chọn: <span className="font-semibold">{selectedSessions.size}</span> / {sessions.length} nhóm
              </p>
            )}
            {deleteMode === 'all' && (
              <p className="text-sm text-gray-600">
                Sẽ xóa toàn bộ: <span className="font-semibold">{sessions.length}</span> nhóm, <span className="font-semibold">{items.length}</span> món
              </p>
            )}
          </div>

          {/* Delete Button */}
          <Button
            variant="destructive"
            onClick={handleBulkDelete}
            disabled={
              (deleteMode === 'items' && selectedItems.size === 0) ||
              (deleteMode === 'sessions' && selectedSessions.size === 0) ||
              (deleteMode === 'all' && sessions.length === 0)
            }
            className="w-full"
          >
            <Trash2 className="h-4 w-4 mr-2" />
            {deleteMode === 'items' && `Xóa ${selectedItems.size} món đã chọn`}
            {deleteMode === 'sessions' && `Xóa ${selectedSessions.size} nhóm đã chọn`}
            {deleteMode === 'all' && 'Xóa toàn bộ giỏ hàng'}
          </Button>

          {/* Item/Session List for Selection */}
          {deleteMode !== 'all' && (
            <ScrollArea className="h-64 border rounded-lg p-2">
              {deleteMode === 'items' && (
                <div className="space-y-2">
                  {items.length === 0 ? (
                    <p className="text-center text-gray-500 py-8">Không có món nào trong giỏ hàng</p>
                  ) : (
                    items.map(item => (
                      <div key={item.id} className="flex items-center space-x-2 p-2 hover:bg-gray-50 rounded">
                        <Checkbox
                          checked={selectedItems.has(item.id)}
                          onCheckedChange={(checked) => handleSelectItem(item.id, checked as boolean)}
                        />
                        <div className="flex-1">
                          <div className="font-medium text-sm">{item.name}</div>
                          <div className="text-xs text-gray-500">{item.category} • {item.source.name}</div>
                        </div>
                        <Badge variant="outline" className="text-xs">
                          {item.quantity} {item.unit}
                        </Badge>
                        {item.estimatedPrice && (
                          <Badge variant="secondary" className="text-xs">
                            {item.estimatedPrice.toLocaleString('vi-VN')}₫
                          </Badge>
                        )}
                      </div>
                    ))
                  )}
                </div>
              )}
              {deleteMode === 'sessions' && (
                <div className="space-y-2">
                  {sessions.length === 0 ? (
                    <p className="text-center text-gray-500 py-8">Không có nhóm nào trong giỏ hàng</p>
                  ) : (
                    sessions.map(session => (
                      <div key={session.id} className="flex items-center space-x-2 p-2 hover:bg-gray-50 rounded">
                        <Checkbox
                          checked={selectedSessions.has(session.id)}
                          onCheckedChange={(checked) => handleSelectSession(session.id, checked as boolean)}
                        />
                        <div className="flex-1">
                          <div className="font-medium text-sm flex items-center gap-2">
                            <span>{session.icon}</span>
                            {session.name}
                          </div>
                          <div className="text-xs text-gray-500">
                            {session.items.length} món • {session.type}
                            {session.description && ` • ${session.description}`}
                          </div>
                        </div>
                        <Badge variant="outline" className="text-xs">
                          {session.totalEstimatedCost.toLocaleString('vi-VN')}₫
                        </Badge>
                      </div>
                    ))
                  )}
                </div>
              )}
            </ScrollArea>
          )}
        </div>
      </CardContent>
    </Card>
  );
};
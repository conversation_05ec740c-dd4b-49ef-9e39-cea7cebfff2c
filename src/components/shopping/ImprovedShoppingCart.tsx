import React, { useState, useMemo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import {
  ShoppingCart,
  Trash2,
  ChevronDown,
  ChevronRight,
  CheckCircle,
  Circle,
  Package,
  Plus,
  Minus,
  Eye,
  EyeOff,
  Download,
  Share2,
  Settings,
  Search,
  Filter,
  X,
  SortAsc,
  SortDesc,
  ChefHat
} from 'lucide-react';
import { useShoppingCart } from '@/contexts/ShoppingCartContext';
import { cn } from '@/lib/utils';
import { toast } from 'sonner';

interface ImprovedShoppingCartProps {
  className?: string;
}

interface FilterState {
  searchQuery: string;
  categoryFilter: string;
  sourceTypeFilter: string;
  statusFilter: string;
  priceRangeFilter: string;
  sortBy: string;
  sortOrder: 'asc' | 'desc';
}

// Compact Shopping Item Component
interface CompactShoppingItemProps {
  item: any;
  session: any;
  onToggleChecked: (id: string) => void;
  onUpdateQuantity: (id: string, quantity: number) => void;
  onRemove: (id: string) => void;
  getCategoryIcon: (category: string) => string;
  formatPrice: (price: number) => string;
}

const CompactShoppingItem: React.FC<CompactShoppingItemProps> = ({
  item,
  session,
  onToggleChecked,
  onUpdateQuantity,
  onRemove,
  getCategoryIcon,
  formatPrice
}) => {
  return (
    <div
      className={cn(
        'p-3 rounded-lg border transition-all duration-200 group',
        item.isChecked
          ? 'bg-green-50 border-green-200 shadow-sm'
          : 'bg-white border-gray-200 hover:border-gray-300 hover:shadow-sm'
      )}
    >
      <div className="flex items-center gap-3">
        {/* Checkbox & Icon */}
        <div className="flex items-center gap-2 flex-shrink-0">
          <Checkbox
            checked={item.isChecked}
            onCheckedChange={() => onToggleChecked(item.id)}
          />
          <span className="text-lg">{getCategoryIcon(item.category)}</span>
        </div>

        {/* Main Content */}
        <div className="flex-1 min-w-0">
          <div className="flex items-center justify-between mb-1">
            <h4 className={cn(
              'font-medium text-gray-900 truncate',
              item.isChecked && 'line-through text-gray-500'
            )}>
              {item.name}
            </h4>

            {/* Quantity Controls - Show on hover or always on mobile */}
            <div className="flex items-center gap-2 flex-shrink-0 opacity-0 group-hover:opacity-100 md:opacity-100 transition-opacity">
              <div className="flex items-center gap-1 bg-gray-50 rounded-md p-1">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => onUpdateQuantity(item.id, Math.max(1, item.quantity - 1))}
                  className="h-6 w-6 p-0 hover:bg-white"
                >
                  <Minus className="h-3 w-3" />
                </Button>
                <span className="min-w-[3rem] text-center text-sm font-medium bg-white rounded px-2">
                  {item.quantity} {item.unit}
                </span>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => onUpdateQuantity(item.id, item.quantity + 1)}
                  className="h-6 w-6 p-0 hover:bg-white"
                >
                  <Plus className="h-3 w-3" />
                </Button>
              </div>

              <Button
                variant="ghost"
                size="sm"
                onClick={() => onRemove(item.id)}
                className="text-red-500 hover:text-red-700 hover:bg-red-50 h-6 w-6 p-0"
              >
                <Trash2 className="h-3 w-3" />
              </Button>
            </div>
          </div>

          {/* Info Row - Price and Source */}
          <div className="flex items-center justify-between text-sm mt-1">
            <div className="flex items-center gap-2 text-gray-600">
              {/* Price info */}
              {(item.estimatedPrice || 0) > 0 ? (
                <div className="flex items-center gap-1">
                  <span className="font-semibold text-green-600">
                    {formatPrice(item.estimatedPrice || 0)}
                  </span>
                  <span className="text-xs text-gray-400">
                    ({formatPrice((item.estimatedPrice || 0) / item.quantity)}/{item.unit})
                  </span>
                </div>
              ) : (
                <span className="text-xs text-gray-400">Chưa có giá</span>
              )}
            </div>

            {/* Source Info - Only show if different from session type */}
            {item.source && item.source.type !== session.type && (
              <Badge variant="outline" className="text-xs">
                {item.source.type === 'recipe' ? '🍽️' :
                 item.source.type === 'meal' ? '🍱' :
                 item.source.type === 'menu' ? '📋' :
                 item.source.type === 'meal-package' ? '📦' : '✏️'}
                {item.source.name}
              </Badge>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

const ImprovedShoppingCart: React.FC<ImprovedShoppingCartProps> = ({ className }) => {
  const {
    sessions,
    totalItems,
    totalEstimatedCost,
    removeSession,
    toggleSessionCollapsed,
    toggleSessionSelected,
    selectAllSessions,
    deselectAllSessions,
    removeItem,
    updateItemQuantity,
    toggleItemChecked,
    generateConsolidatedShoppingList,
    clearCart
  } = useShoppingCart();

  const [viewMode, setViewMode] = useState<'sessions' | 'consolidated'>('sessions');
  const [showConsolidatedList, setShowConsolidatedList] = useState(false);
  const [showFilters, setShowFilters] = useState(false);
  const [filters, setFilters] = useState<FilterState>({
    searchQuery: '',
    categoryFilter: 'all',
    sourceTypeFilter: 'all',
    statusFilter: 'all',
    priceRangeFilter: 'all',
    sortBy: 'name',
    sortOrder: 'asc'
  });

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND'
    }).format(price);
  };

  // Get unique categories and source types for filter options
  const allItems = sessions.flatMap(session => session.items);
  const uniqueCategories = [...new Set(allItems.map(item => item.category))];
  const uniqueSourceTypes = [...new Set(allItems.map(item => item.source.type))];

  // Filter and sort sessions and items
  const filteredSessions = useMemo(() => {
    let filtered = sessions.map(session => {
      let filteredItems = session.items;

      // Apply search filter
      if (filters.searchQuery) {
        filteredItems = filteredItems.filter(item =>
          item.name.toLowerCase().includes(filters.searchQuery.toLowerCase()) ||
          item.category.toLowerCase().includes(filters.searchQuery.toLowerCase()) ||
          item.source.name.toLowerCase().includes(filters.searchQuery.toLowerCase())
        );
      }

      // Apply category filter
      if (filters.categoryFilter !== 'all') {
        filteredItems = filteredItems.filter(item => item.category === filters.categoryFilter);
      }

      // Apply source type filter
      if (filters.sourceTypeFilter !== 'all') {
        filteredItems = filteredItems.filter(item => item.source.type === filters.sourceTypeFilter);
      }

      // Apply status filter
      if (filters.statusFilter !== 'all') {
        if (filters.statusFilter === 'checked') {
          filteredItems = filteredItems.filter(item => item.isChecked);
        } else if (filters.statusFilter === 'unchecked') {
          filteredItems = filteredItems.filter(item => !item.isChecked);
        }
      }

      // Apply price range filter
      if (filters.priceRangeFilter !== 'all') {
        filteredItems = filteredItems.filter(item => {
          const price = item.estimatedPrice || 0;
          switch (filters.priceRangeFilter) {
            case 'under-50k':
              return price < 50000;
            case '50k-100k':
              return price >= 50000 && price < 100000;
            case '100k-200k':
              return price >= 100000 && price < 200000;
            case 'over-200k':
              return price >= 200000;
            default:
              return true;
          }
        });
      }

      // Sort items
      filteredItems.sort((a, b) => {
        let comparison = 0;
        switch (filters.sortBy) {
          case 'name':
            comparison = a.name.localeCompare(b.name);
            break;
          case 'category':
            comparison = a.category.localeCompare(b.category);
            break;
          case 'price':
            comparison = (a.estimatedPrice || 0) - (b.estimatedPrice || 0);
            break;
          case 'addedAt':
            comparison = new Date(a.addedAt).getTime() - new Date(b.addedAt).getTime();
            break;
          default:
            comparison = 0;
        }
        return filters.sortOrder === 'desc' ? -comparison : comparison;
      });

      return {
        ...session,
        items: filteredItems
      };
    });

    // Filter out sessions with no items after filtering
    filtered = filtered.filter(session => session.items.length > 0);

    return filtered;
  }, [sessions, filters]);

  const filteredTotalItems = filteredSessions.reduce((total, session) => total + session.items.length, 0);
  const filteredTotalCost = filteredSessions.reduce((total, session) =>
    total + session.items.reduce((sessionTotal, item) => sessionTotal + (item.estimatedPrice || 0), 0), 0
  );

  const getCategoryIcon = (category: string) => {
    const icons: { [key: string]: string } = {
      'Thịt': '🥩',
      'Cá': '🐟',
      'Rau củ': '🥬',
      'Trái cây': '🍎',
      'Gia vị': '🧂',
      'Ngũ cốc': '🌾',
      'Sữa': '🥛',
      'Khác': '📦'
    };
    return icons[category] || icons['Khác'];
  };

  const selectedSessions = sessions.filter(session => session.isSelected);
  const selectedItemsCount = selectedSessions.reduce((sum, session) => sum + session.items.length, 0);
  const selectedTotalCost = selectedSessions.reduce((sum, session) => sum + session.totalEstimatedCost, 0);

  // Calculate total quantity (not just item count) for selected sessions
  const selectedTotalQuantity = selectedSessions.reduce((sum, session) =>
    sum + session.items.reduce((itemSum, item) => itemSum + item.quantity, 0), 0
  );

  const handleGenerateShoppingList = () => {
    if (selectedSessions.length === 0) {
      toast.error('Vui lòng chọn ít nhất một nhóm để tạo danh sách mua sắm');
      return;
    }
    setShowConsolidatedList(true);
    setViewMode('consolidated');
  };

  // Filter handlers
  const handleFilterChange = (key: keyof FilterState, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  };

  const clearFilters = () => {
    setFilters({
      searchQuery: '',
      categoryFilter: 'all',
      sourceTypeFilter: 'all',
      statusFilter: 'all',
      priceRangeFilter: 'all',
      sortBy: 'name',
      sortOrder: 'asc'
    });
  };

  const toggleSortOrder = () => {
    setFilters(prev => ({
      ...prev,
      sortOrder: prev.sortOrder === 'asc' ? 'desc' : 'asc'
    }));
  };

  // Bulk delete handlers
  const handleDeleteSelectedSessions = () => {
    const selectedCount = selectedSessions.length;
    if (selectedCount === 0) {
      toast.error('Vui lòng chọn ít nhất một nhóm để xóa');
      return;
    }

    if (confirm(`Bạn có chắc chắn muốn xóa ${selectedCount} nhóm đã chọn?`)) {
      selectedSessions.forEach(session => {
        removeSession(session.id);
      });
      toast.success(`Đã xóa ${selectedCount} nhóm thành công`);
    }
  };

  const handleDeleteCheckedItems = () => {
    const checkedItems = allItems.filter(item => item.isChecked);
    if (checkedItems.length === 0) {
      toast.error('Không có món nào được đánh dấu để xóa');
      return;
    }

    if (confirm(`Bạn có chắc chắn muốn xóa ${checkedItems.length} món đã đánh dấu?`)) {
      checkedItems.forEach(item => {
        removeItem(item.id);
      });
      toast.success(`Đã xóa ${checkedItems.length} món thành công`);
    }
  };

  const handleClearAllCart = () => {
    if (sessions.length === 0) {
      toast.error('Giỏ hàng đã trống');
      return;
    }

    if (confirm('Bạn có chắc chắn muốn xóa toàn bộ giỏ hàng?')) {
      clearCart();
      toast.success('Đã xóa toàn bộ giỏ hàng');
    }
  };

  const consolidatedList = generateConsolidatedShoppingList();

  if (sessions.length === 0) {
    return (
      <Card className={className}>
        <CardContent className="p-12 text-center">
          <div className="bg-gradient-to-br from-blue-50 to-green-50 rounded-full p-6 w-24 h-24 mx-auto mb-6 flex items-center justify-center">
            <ShoppingCart className="h-12 w-12 text-blue-500" />
          </div>
          <h3 className="text-xl font-semibold text-gray-900 mb-2">
            Giỏ hàng trống
          </h3>
          <p className="text-gray-600 mb-6 max-w-md mx-auto">
            Bạn chưa thêm món nào vào giỏ hàng. Hãy bắt đầu bằng cách thêm món từ thực đơn hoặc công thức nấu ăn.
          </p>
          <div className="flex justify-center gap-3">
            <Button variant="outline" size="sm">
              <Plus className="h-4 w-4 mr-2" />
              Thêm từ thực đơn
            </Button>
            <Button size="sm">
              <Search className="h-4 w-4 mr-2" />
              Duyệt công thức
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className={cn('space-y-6', className)}>
      {/* Header Controls */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <ShoppingCart className="h-5 w-5" />
              Giỏ Hàng Thông Minh
            </CardTitle>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowFilters(!showFilters)}
              >
                <Filter className="h-4 w-4 mr-2" />
                Bộ lọc
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setViewMode(viewMode === 'sessions' ? 'consolidated' : 'sessions')}
              >
                {viewMode === 'sessions' ? <Eye className="h-4 w-4 mr-2" /> : <EyeOff className="h-4 w-4 mr-2" />}
                {viewMode === 'sessions' ? 'Xem tổng hợp' : 'Xem theo nhóm'}
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {/* Compact Stats */}
          <div className="bg-gradient-to-r from-blue-50 to-green-50 rounded-lg p-4 mb-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-6">
                <div className="text-center">
                  <div className="text-xl font-bold text-blue-600">{filteredSessions.length}</div>
                  <div className="text-xs text-gray-600">
                    Nhóm{filteredSessions.length !== sessions.length && ` (${sessions.length})`}
                  </div>
                </div>
                <div className="text-center">
                  <div className="text-xl font-bold text-purple-600">{filteredTotalItems}</div>
                  <div className="text-xs text-gray-600">
                    Món{filteredTotalItems !== totalItems && ` (${totalItems})`}
                  </div>
                </div>
                <div className="text-center">
                  <div className="text-xl font-bold text-green-600">{formatPrice(filteredTotalCost)}</div>
                  <div className="text-xs text-gray-600">Tổng tiền</div>
                </div>
              </div>
              <div className="text-right">
                <div className="text-sm text-gray-600">Đã chọn</div>
                <div className="text-lg font-semibold text-orange-600">
                  {selectedSessions.length} nhóm
                </div>
              </div>
            </div>
          </div>

          {/* Bulk Actions */}
          <div className="flex flex-wrap gap-2 mb-4">
            <Button
              variant="outline"
              size="sm"
              onClick={selectAllSessions}
              disabled={sessions.length === 0}
            >
              <CheckCircle className="h-4 w-4 mr-2" />
              Chọn tất cả nhóm
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={deselectAllSessions}
              disabled={selectedSessions.length === 0}
            >
              <Circle className="h-4 w-4 mr-2" />
              Bỏ chọn tất cả
            </Button>
            <Button
              variant="destructive"
              size="sm"
              onClick={handleDeleteSelectedSessions}
              disabled={selectedSessions.length === 0}
            >
              <Trash2 className="h-4 w-4 mr-2" />
              Xóa nhóm đã chọn ({selectedSessions.length})
            </Button>
            <Button
              variant="destructive"
              size="sm"
              onClick={handleDeleteCheckedItems}
              disabled={allItems.filter(item => item.isChecked).length === 0}
            >
              <Trash2 className="h-4 w-4 mr-2" />
              Xóa món đã đánh dấu ({allItems.filter(item => item.isChecked).length})
            </Button>
            <Button
              variant="destructive"
              size="sm"
              onClick={handleClearAllCart}
              disabled={sessions.length === 0}
            >
              <X className="h-4 w-4 mr-2" />
              Xóa toàn bộ giỏ hàng
            </Button>
          </div>

          {/* Filter Panel */}
          {showFilters && (
            <div className="border rounded-lg p-4 mb-4 bg-gray-50">
              <div className="flex items-center justify-between mb-4">
                <h3 className="font-semibold text-gray-900">Bộ lọc và sắp xếp</h3>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={clearFilters}
                >
                  <X className="h-4 w-4 mr-2" />
                  Xóa bộ lọc
                </Button>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {/* Search */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Tìm kiếm
                  </label>
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                    <Input
                      placeholder="Tìm theo tên, danh mục..."
                      value={filters.searchQuery}
                      onChange={(e) => handleFilterChange('searchQuery', e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </div>

                {/* Category Filter */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Danh mục
                  </label>
                  <Select
                    value={filters.categoryFilter}
                    onValueChange={(value) => handleFilterChange('categoryFilter', value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Chọn danh mục" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">Tất cả danh mục</SelectItem>
                      {uniqueCategories.map(category => (
                        <SelectItem key={category} value={category}>
                          {getCategoryIcon(category)} {category}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {/* Source Type Filter */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Nguồn
                  </label>
                  <Select
                    value={filters.sourceTypeFilter}
                    onValueChange={(value) => handleFilterChange('sourceTypeFilter', value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Chọn nguồn" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">Tất cả nguồn</SelectItem>
                      {uniqueSourceTypes.map(type => (
                        <SelectItem key={type} value={type}>
                          {type === 'recipe' ? '🍽️ Món ăn' :
                           type === 'meal' ? '🍱 Bữa ăn' :
                           type === 'menu' ? '📋 Thực đơn' :
                           type === 'meal-package' ? '📦 Gói bữa ăn' :
                           '✏️ Thêm thủ công'}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {/* Status Filter */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Trạng thái
                  </label>
                  <Select
                    value={filters.statusFilter}
                    onValueChange={(value) => handleFilterChange('statusFilter', value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Chọn trạng thái" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">Tất cả</SelectItem>
                      <SelectItem value="checked">✅ Đã đánh dấu</SelectItem>
                      <SelectItem value="unchecked">⭕ Chưa đánh dấu</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* Price Range Filter */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Khoảng giá
                  </label>
                  <Select
                    value={filters.priceRangeFilter}
                    onValueChange={(value) => handleFilterChange('priceRangeFilter', value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Chọn khoảng giá" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">Tất cả</SelectItem>
                      <SelectItem value="under-50k">Dưới 50k</SelectItem>
                      <SelectItem value="50k-100k">50k - 100k</SelectItem>
                      <SelectItem value="100k-200k">100k - 200k</SelectItem>
                      <SelectItem value="over-200k">Trên 200k</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* Sort */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Sắp xếp
                  </label>
                  <div className="flex gap-2">
                    <Select
                      value={filters.sortBy}
                      onValueChange={(value) => handleFilterChange('sortBy', value)}
                    >
                      <SelectTrigger className="flex-1">
                        <SelectValue placeholder="Sắp xếp theo" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="name">Tên</SelectItem>
                        <SelectItem value="category">Danh mục</SelectItem>
                        <SelectItem value="price">Giá</SelectItem>
                        <SelectItem value="addedAt">Thời gian thêm</SelectItem>
                      </SelectContent>
                    </Select>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={toggleSortOrder}
                    >
                      {filters.sortOrder === 'asc' ? <SortAsc className="h-4 w-4" /> : <SortDesc className="h-4 w-4" />}
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          )}

          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={selectAllSessions}
            >
              <CheckCircle className="h-4 w-4 mr-2" />
              Chọn tất cả
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={deselectAllSessions}
            >
              <Circle className="h-4 w-4 mr-2" />
              Bỏ chọn tất cả
            </Button>
            <Button
              onClick={handleGenerateShoppingList}
              disabled={selectedSessions.length === 0}
              className="bg-green-600 hover:bg-green-700 text-white"
            >
              <Package className="h-4 w-4 mr-2" />
              Tạo danh sách mua sắm ({selectedTotalQuantity} món)
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Sessions View */}
      {viewMode === 'sessions' && (
        <div className="space-y-4">
          {filteredSessions.length === 0 ? (
            <Card>
              <CardContent className="p-8 text-center">
                <Search className="h-16 w-16 text-gray-300 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  Không tìm thấy kết quả
                </h3>
                <p className="text-gray-600">
                  Thử điều chỉnh bộ lọc hoặc từ khóa tìm kiếm
                </p>
                <Button
                  variant="outline"
                  onClick={clearFilters}
                  className="mt-4"
                >
                  Xóa bộ lọc
                </Button>
              </CardContent>
            </Card>
          ) : (
            filteredSessions.map((session) => (
            <Card 
              key={session.id} 
              className={cn(
                'transition-all duration-200',
                session.isSelected ? 'ring-2 ring-green-500 ring-offset-2' : 'hover:shadow-md'
              )}
            >
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3 flex-1 min-w-0">
                    <Checkbox
                      checked={session.isSelected || false}
                      onCheckedChange={() => toggleSessionSelected(session.id)}
                      className="flex-shrink-0"
                    />
                    <div className="flex items-center gap-2 flex-1 min-w-0">
                      <span className="text-lg flex-shrink-0">{session.icon}</span>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2 mb-1">
                          <h3 className="font-semibold text-gray-900 truncate">{session.name}</h3>
                          <Badge
                            variant="outline"
                            style={{ borderColor: session.color }}
                            className="flex-shrink-0 text-xs"
                          >
                            {session.items.length} món
                          </Badge>
                        </div>
                        <div className="flex items-center gap-2 text-sm">
                          <span className="font-medium text-green-600">
                            {formatPrice(session.totalEstimatedCost)}
                          </span>
                          {session.description && (
                            <>
                              <span className="text-gray-400">•</span>
                              <span className="text-gray-600 truncate">{session.description}</span>
                            </>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center gap-1 flex-shrink-0">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => toggleSessionCollapsed(session.id)}
                      className="h-8 w-8 p-0"
                    >
                      {session.isCollapsed ? <ChevronRight className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => removeSession(session.id)}
                      className="text-red-500 hover:text-red-700 h-8 w-8 p-0"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardHeader>

              {!session.isCollapsed && (
                <CardContent className="pt-0">
                  <div className="space-y-2">
                    {session.items.map((item) => (
                      <CompactShoppingItem
                        key={item.id}
                        item={item}
                        session={session}
                        onToggleChecked={toggleItemChecked}
                        onUpdateQuantity={updateItemQuantity}
                        onRemove={removeItem}
                        getCategoryIcon={getCategoryIcon}
                        formatPrice={formatPrice}
                      />
                    ))}
                  </div>
                </CardContent>
              )}
            </Card>
          )))}
        </div>
      )}

      {/* Consolidated View */}
      {viewMode === 'consolidated' && showConsolidatedList && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Package className="h-5 w-5" />
              Danh Sách Mua Sắm Tổng Hợp
            </CardTitle>
            <div className="flex items-center gap-2">
              <Badge variant="outline">
                {consolidatedList.totalItems} món
              </Badge>
              <Badge variant="outline">
                {formatPrice(consolidatedList.totalEstimatedCost)}
              </Badge>
            </div>
          </CardHeader>
          <CardContent>
            <ScrollArea className="h-96">
              <div className="space-y-4">
                {Object.entries(consolidatedList.categories).map(([category, items]) => (
                  <div key={category}>
                    <h3 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
                      <span className="text-lg">{getCategoryIcon(category)}</span>
                      {category}
                      <Badge variant="secondary">{items.length}</Badge>
                    </h3>
                    <div className="space-y-2 ml-6">
                      {items.map((item, index) => (
                        <div key={index} className="flex items-center gap-3 p-3 rounded-lg border bg-gray-50">
                          <Checkbox className="flex-shrink-0" />
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center justify-between mb-1">
                              <h4 className="font-medium text-gray-900 truncate">{item.name}</h4>
                              <span className="font-medium text-green-600 flex-shrink-0">
                                {formatPrice(item.totalEstimatedPrice)}
                              </span>
                            </div>
                            <div className="flex items-center gap-2 text-sm text-gray-600">
                              <span className="font-medium text-blue-600">
                                {item.totalQuantity} {item.unit}
                              </span>
                              <span className="text-gray-400">•</span>
                              <span className="truncate">
                                Từ: {item.sources.map(s => s.sessionName).join(', ')}
                              </span>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </ScrollArea>
            
            <Separator className="my-4" />
            
            <div className="flex items-center justify-between">
              <div className="text-lg font-semibold">
                Tổng cộng: {formatPrice(consolidatedList.totalEstimatedCost)}
              </div>
              <div className="flex items-center gap-2">
                <Button variant="outline" size="sm">
                  <Download className="h-4 w-4 mr-2" />
                  Xuất PDF
                </Button>
                <Button variant="outline" size="sm">
                  <Share2 className="h-4 w-4 mr-2" />
                  Chia sẻ
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default ImprovedShoppingCart;

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { 
  Search, 
  BookOpen, 
  Calendar, 
  Utensils,
  Clock,
  Users,
  ChefHat,
  Plus,
  Check
} from 'lucide-react';
import { useShoppingCart } from '@/contexts/ShoppingCartContext';
import { useKitchen } from '@/contexts/KitchenContext';
import { useMealPlanning } from '@/contexts/MealPlanningContext';
import { Recipe } from '@/types/kitchen';
import { toast } from 'sonner';

interface AddFromSourceModalProps {
  isOpen: boolean;
  onClose: () => void;
  defaultTab?: 'recipes' | 'meals' | 'menus';
}

const AddFromSourceModal: React.FC<AddFromSourceModalProps> = ({
  isOpen,
  onClose,
  defaultTab = 'recipes'
}) => {
  const { addRecipeToCart, addMenuToCart } = useShoppingCart();
  const { recipes, dailyMenus, todayMeals } = useKitchen();
  const { availableRecipes } = useMealPlanning();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedItems, setSelectedItems] = useState<Set<string>>(new Set());
  const [isAdding, setIsAdding] = useState(false);

  // Reset state when modal opens/closes
  useEffect(() => {
    if (isOpen) {
      setSearchTerm('');
      setSelectedItems(new Set());
    }
  }, [isOpen]);

  // Filter recipes based on search
  const filteredRecipes = (recipes || availableRecipes || []).filter(recipe =>
    recipe.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    recipe.description?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Filter daily menus based on search
  const filteredMenus = (dailyMenus || []).filter(menu =>
    menu.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    menu.description?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleToggleItem = (id: string) => {
    const newSelected = new Set(selectedItems);
    if (newSelected.has(id)) {
      newSelected.delete(id);
    } else {
      newSelected.add(id);
    }
    setSelectedItems(newSelected);
  };

  const handleAddSelected = async (type: 'recipes' | 'meals' | 'menus') => {
    if (selectedItems.size === 0) {
      toast.error('Vui lòng chọn ít nhất một mục');
      return;
    }

    setIsAdding(true);
    try {
      if (type === 'recipes') {
        const selectedRecipes = filteredRecipes.filter(recipe => 
          selectedItems.has(recipe.id)
        );
        for (const recipe of selectedRecipes) {
          addRecipeToCart(recipe, 1);
        }
        toast.success(`Đã thêm ${selectedRecipes.length} công thức vào giỏ hàng`);
      } else if (type === 'menus') {
        const selectedMenus = filteredMenus.filter(menu => 
          selectedItems.has(menu.id)
        );
        for (const menu of selectedMenus) {
          // Convert menu meals to meal slots
          const menuMeals = Object.values(menu.meals).filter(meal => meal?.recipe);
          if (menuMeals.length > 0) {
            const mealSlots = menuMeals.map(meal => ({
              id: meal!.id,
              date: new Date().toISOString().split('T')[0],
              mealType: meal!.mealType as 'breakfast' | 'lunch' | 'dinner' | 'snack',
              recipe: meal!.recipe,
              notes: ''
            }));
            addMenuToCart(mealSlots);
          }
        }
        toast.success(`Đã thêm ${selectedMenus.length} thực đơn vào giỏ hàng`);
      }
      
      setSelectedItems(new Set());
      onClose();
    } catch (error) {
      console.error('Error adding items to cart:', error);
      toast.error('Không thể thêm vào giỏ hàng');
    } finally {
      setIsAdding(false);
    }
  };

  const RecipeCard = ({ recipe }: { recipe: Recipe }) => {
    const isSelected = selectedItems.has(recipe.id);
    const ingredientCount = recipe.ingredients ? 
      (Array.isArray(recipe.ingredients) 
        ? recipe.ingredients.length 
        : JSON.parse(recipe.ingredients || '[]').length
      ) : 0;

    return (
      <Card 
        className={`cursor-pointer transition-all ${
          isSelected ? 'ring-2 ring-orange-500 bg-orange-50' : 'hover:shadow-md'
        }`}
        onClick={() => handleToggleItem(recipe.id)}
      >
        <CardContent className="p-4">
          <div className="flex items-start space-x-3">
            {recipe.image && (
              <img
                src={recipe.image}
                alt={recipe.title}
                className="w-16 h-16 rounded-lg object-cover"
              />
            )}
            <div className="flex-1 min-w-0">
              <h3 className="font-semibold text-gray-900 truncate">
                {recipe.title}
              </h3>
              <p className="text-sm text-gray-600 line-clamp-2 mt-1">
                {recipe.description}
              </p>
              <div className="flex items-center space-x-3 mt-2 text-xs text-gray-500">
                {recipe.cooking_time && (
                  <div className="flex items-center">
                    <Clock className="h-3 w-3 mr-1" />
                    {recipe.cooking_time}
                  </div>
                )}
                {recipe.servings && (
                  <div className="flex items-center">
                    <Users className="h-3 w-3 mr-1" />
                    {recipe.servings} phần
                  </div>
                )}
                <Badge variant="secondary" className="text-xs">
                  {ingredientCount} nguyên liệu
                </Badge>
              </div>
            </div>
            <div className="flex-shrink-0">
              {isSelected ? (
                <div className="w-6 h-6 bg-orange-500 rounded-full flex items-center justify-center">
                  <Check className="h-4 w-4 text-white" />
                </div>
              ) : (
                <div className="w-6 h-6 border-2 border-gray-300 rounded-full" />
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    );
  };

  const MenuCard = ({ menu }: { menu: any }) => {
    const isSelected = selectedItems.has(menu.id);
    const mealCount = Object.values(menu.meals).filter(meal => meal?.recipe).length;

    return (
      <Card 
        className={`cursor-pointer transition-all ${
          isSelected ? 'ring-2 ring-orange-500 bg-orange-50' : 'hover:shadow-md'
        }`}
        onClick={() => handleToggleItem(menu.id)}
      >
        <CardContent className="p-4">
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <h3 className="font-semibold text-gray-900">
                {menu.name}
              </h3>
              <p className="text-sm text-gray-600 mt-1">
                {menu.description}
              </p>
              <div className="flex items-center space-x-3 mt-2 text-xs text-gray-500">
                <div className="flex items-center">
                  <Clock className="h-3 w-3 mr-1" />
                  {menu.prepTime}
                </div>
                <div className="flex items-center">
                  <Users className="h-3 w-3 mr-1" />
                  {menu.servings} phần
                </div>
                <Badge variant="secondary" className="text-xs">
                  {mealCount} bữa ăn
                </Badge>
              </div>
            </div>
            <div className="flex-shrink-0 ml-3">
              {isSelected ? (
                <div className="w-6 h-6 bg-orange-500 rounded-full flex items-center justify-center">
                  <Check className="h-4 w-4 text-white" />
                </div>
              ) : (
                <div className="w-6 h-6 border-2 border-gray-300 rounded-full" />
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    );
  };

  const TodayMealsCard = () => {
    if (!todayMeals || !todayMeals.meals) return null;

    const meals = Object.values(todayMeals.meals).filter(meal => meal?.recipe);
    if (meals.length === 0) return null;

    const isSelected = selectedItems.has('today-meals');

    return (
      <Card 
        className={`cursor-pointer transition-all ${
          isSelected ? 'ring-2 ring-orange-500 bg-orange-50' : 'hover:shadow-md'
        }`}
        onClick={() => handleToggleItem('today-meals')}
      >
        <CardContent className="p-4">
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <h3 className="font-semibold text-gray-900 flex items-center">
                <Utensils className="h-4 w-4 mr-2" />
                Bữa ăn hôm nay
              </h3>
              <p className="text-sm text-gray-600 mt-1">
                Tất cả bữa ăn đã lên kế hoạch cho hôm nay
              </p>
              <div className="flex items-center space-x-3 mt-2">
                <Badge variant="secondary" className="text-xs">
                  {meals.length} bữa ăn
                </Badge>
              </div>
            </div>
            <div className="flex-shrink-0 ml-3">
              {isSelected ? (
                <div className="w-6 h-6 bg-orange-500 rounded-full flex items-center justify-center">
                  <Check className="h-4 w-4 text-white" />
                </div>
              ) : (
                <div className="w-6 h-6 border-2 border-gray-300 rounded-full" />
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    );
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[80vh]">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            <Plus className="h-5 w-5 mr-2 text-orange-500" />
            Thêm vào giỏ hàng
          </DialogTitle>
          <DialogDescription>
            Chọn công thức, thực đơn hoặc bữa ăn để thêm nguyên liệu vào giỏ hàng
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Search */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Tìm kiếm..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>

          {/* Tabs */}
          <Tabs defaultValue={defaultTab} className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="recipes" className="flex items-center">
                <BookOpen className="h-4 w-4 mr-2" />
                Công thức
              </TabsTrigger>
              <TabsTrigger value="menus" className="flex items-center">
                <Calendar className="h-4 w-4 mr-2" />
                Thực đơn
              </TabsTrigger>
              <TabsTrigger value="meals" className="flex items-center">
                <Utensils className="h-4 w-4 mr-2" />
                Bữa ăn
              </TabsTrigger>
            </TabsList>

            <TabsContent value="recipes" className="space-y-4">
              <div className="flex items-center justify-between">
                <p className="text-sm text-gray-600">
                  {filteredRecipes.length} công thức • {selectedItems.size} đã chọn
                </p>
                <Button
                  onClick={() => handleAddSelected('recipes')}
                  disabled={selectedItems.size === 0 || isAdding}
                  size="sm"
                >
                  {isAdding ? 'Đang thêm...' : `Thêm ${selectedItems.size} công thức`}
                </Button>
              </div>
              <ScrollArea className="h-96">
                <div className="space-y-3">
                  {filteredRecipes.map((recipe) => (
                    <RecipeCard key={recipe.id} recipe={recipe} />
                  ))}
                </div>
              </ScrollArea>
            </TabsContent>

            <TabsContent value="menus" className="space-y-4">
              <div className="flex items-center justify-between">
                <p className="text-sm text-gray-600">
                  {filteredMenus.length} thực đơn • {selectedItems.size} đã chọn
                </p>
                <Button
                  onClick={() => handleAddSelected('menus')}
                  disabled={selectedItems.size === 0 || isAdding}
                  size="sm"
                >
                  {isAdding ? 'Đang thêm...' : `Thêm ${selectedItems.size} thực đơn`}
                </Button>
              </div>
              <ScrollArea className="h-96">
                <div className="space-y-3">
                  {filteredMenus.map((menu) => (
                    <MenuCard key={menu.id} menu={menu} />
                  ))}
                </div>
              </ScrollArea>
            </TabsContent>

            <TabsContent value="meals" className="space-y-4">
              <div className="flex items-center justify-between">
                <p className="text-sm text-gray-600">
                  Bữa ăn hôm nay • {selectedItems.size} đã chọn
                </p>
                <Button
                  onClick={() => {
                    if (selectedItems.has('today-meals') && todayMeals) {
                      const meals = Object.values(todayMeals.meals).filter(meal => meal?.recipe);
                      const mealSlots = meals.map(meal => ({
                        id: meal!.id,
                        date: new Date().toISOString().split('T')[0],
                        mealType: meal!.mealType as 'breakfast' | 'lunch' | 'dinner' | 'snack',
                        recipe: meal!.recipe,
                        notes: ''
                      }));
                      addMenuToCart(mealSlots);
                      toast.success('Đã thêm bữa ăn hôm nay vào giỏ hàng');
                      onClose();
                    }
                  }}
                  disabled={!selectedItems.has('today-meals') || isAdding}
                  size="sm"
                >
                  {isAdding ? 'Đang thêm...' : 'Thêm bữa ăn'}
                </Button>
              </div>
              <ScrollArea className="h-96">
                <div className="space-y-3">
                  <TodayMealsCard />
                </div>
              </ScrollArea>
            </TabsContent>
          </Tabs>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default AddFromSourceModal;

import React from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  CheckCircle, 
  ShoppingCart, 
  Calculator, 
  TrendingUp,
  Users,
  Package,
  ArrowRight,
  AlertCircle
} from 'lucide-react';
import { useNavigate } from 'react-router-dom';

interface DynamicCartSummaryProps {
  className?: string;
}

const DynamicCartSummary: React.FC<DynamicCartSummaryProps> = ({ className }) => {
  const navigate = useNavigate();

  return (
    <Card className={`${className} border-green-200 bg-green-50`}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-green-800">
          <CheckCircle className="h-5 w-5" />
          ✅ Sửa Lỗi: Hiển Thị Đúng <PERSON> Từ Sessions Đã Chọn
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Problem Description */}
        <div className="bg-red-50 rounded-lg p-4 border border-red-200">
          <h3 className="font-semibold text-red-800 mb-3 flex items-center gap-2">
            <AlertCircle className="h-4 w-4" />
            Vấn Đề Trước Đây
          </h3>
          <div className="text-red-700 text-sm space-y-2">
            <p>
              <strong>Hiển thị sai số liệu:</strong> Giỏ hàng hiển thị tổng số món và tổng tiền của 
              <em> tất cả sessions</em> thay vì chỉ tính từ <em>sessions đã được chọn</em>.
            </p>
            <div className="bg-red-100 rounded p-3 mt-2">
              <p className="font-medium">Ví dụ lỗi:</p>
              <ul className="list-disc list-inside mt-1 space-y-1">
                <li>Có 3 nhóm, chỉ chọn 1 nhóm</li>
                <li>Nhưng hiển thị: "5667 món • 714.074.000 đ" (tổng của cả 3 nhóm)</li>
                <li>Thay vì: "50 món • 25.000.000 đ" (chỉ nhóm đã chọn)</li>
              </ul>
            </div>
          </div>
        </div>

        {/* Solution Implemented */}
        <div className="bg-green-50 rounded-lg p-4 border border-green-200">
          <h3 className="font-semibold text-green-800 mb-3 flex items-center gap-2">
            <Calculator className="h-4 w-4" />
            Giải Pháp Đã Triển Khai
          </h3>
          <div className="grid md:grid-cols-2 gap-4 text-sm">
            <div className="space-y-3">
              <div>
                <Badge variant="outline" className="text-xs mb-2">ImprovedShoppingCart.tsx</Badge>
                <ul className="space-y-1 text-green-700">
                  <li>• Tính <code>selectedTotalQuantity</code> từ sessions đã chọn</li>
                  <li>• Hiển thị "Tổng nhóm" vs "Nhóm đã chọn"</li>
                  <li>• Cập nhật nút "Tạo danh sách mua sắm"</li>
                  <li>• Layout 4 cột: Tổng nhóm | Đã chọn | Số món | Tổng tiền</li>
                </ul>
              </div>
            </div>
            <div className="space-y-3">
              <div>
                <Badge variant="outline" className="text-xs mb-2">QuickCartWidget.tsx</Badge>
                <ul className="space-y-1 text-green-700">
                  <li>• Tính <code>selectedTotalQuantity</code> động</li>
                  <li>• Cập nhật SheetDescription với thông tin rõ ràng</li>
                  <li>• Summary card hiển thị 4 metrics riêng biệt</li>
                  <li>• Nút "Đi Chợ Ngay" hiển thị số món + giá</li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        {/* Technical Details */}
        <div className="space-y-4">
          <h3 className="font-semibold text-gray-900 flex items-center gap-2">
            <TrendingUp className="h-4 w-4 text-blue-500" />
            Chi Tiết Kỹ Thuật
          </h3>
          <div className="bg-white rounded-lg p-4 border border-gray-200">
            <div className="grid md:grid-cols-2 gap-6 text-sm">
              <div>
                <h4 className="font-medium text-gray-800 mb-3">Tính Toán Mới:</h4>
                <div className="space-y-2 font-mono text-xs bg-gray-50 p-3 rounded">
                  <div><span className="text-blue-600">selectedSessions</span> = sessions.filter(s {'=>'} s.isSelected)</div>
                  <div><span className="text-green-600">selectedTotalQuantity</span> = selectedSessions.reduce(</div>
                  <div className="ml-4">(sum, session) {'=>'} sum + session.items.reduce(</div>
                  <div className="ml-8">(itemSum, item) {'=>'} itemSum + item.quantity, 0</div>
                  <div className="ml-4">), 0</div>
                  <div>)</div>
                </div>
              </div>
              <div>
                <h4 className="font-medium text-gray-800 mb-3">Hiển Thị Cải Tiến:</h4>
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <div className="w-3 h-3 bg-gray-400 rounded"></div>
                    <span>Tổng nhóm: {'{sessions.length}'}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-3 h-3 bg-orange-400 rounded"></div>
                    <span>Nhóm đã chọn: {'{selectedSessions.length}'}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-3 h-3 bg-blue-400 rounded"></div>
                    <span>Số món: {'{selectedTotalQuantity}'}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-3 h-3 bg-green-400 rounded"></div>
                    <span>Tổng tiền: {'{selectedTotalCost}'}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Before vs After */}
        <div className="space-y-4">
          <h3 className="font-semibold text-gray-900 flex items-center gap-2">
            <Users className="h-4 w-4 text-purple-500" />
            So Sánh Trước & Sau
          </h3>
          <div className="grid md:grid-cols-2 gap-4">
            <div className="bg-red-50 rounded-lg p-4 border border-red-200">
              <h4 className="font-semibold text-red-800 mb-3">❌ Trước (Sai)</h4>
              <div className="space-y-2 text-sm text-red-700">
                <div className="bg-white rounded p-2 border">
                  <div className="font-medium">Tổng số món: <span className="text-red-600">5667</span></div>
                  <div className="font-medium">Tổng tiền dự kiến: <span className="text-red-600">714.074.000 đ</span></div>
                  <div className="text-xs text-red-500 mt-1">
                    ↑ Tính từ TẤT CẢ sessions (kể cả chưa chọn)
                  </div>
                </div>
              </div>
            </div>
            <div className="bg-green-50 rounded-lg p-4 border border-green-200">
              <h4 className="font-semibold text-green-800 mb-3">✅ Sau (Đúng)</h4>
              <div className="space-y-2 text-sm text-green-700">
                <div className="bg-white rounded p-2 border">
                  <div className="font-medium">Tổng số món: <span className="text-green-600">45</span></div>
                  <div className="font-medium">Tổng tiền dự kiến: <span className="text-green-600">1.250.000 đ</span></div>
                  <div className="text-xs text-green-500 mt-1">
                    ↑ Chỉ tính từ sessions ĐÃ CHỌN
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* User Experience Impact */}
        <div className="bg-blue-50 rounded-lg p-4 border border-blue-200">
          <h4 className="font-semibold text-blue-800 mb-3 flex items-center gap-2">
            <Package className="h-4 w-4" />
            Tác Động Đến Trải Nghiệm Người Dùng
          </h4>
          <div className="grid md:grid-cols-2 gap-4 text-sm text-blue-700">
            <div>
              <h5 className="font-medium mb-2">Lợi ích:</h5>
              <ul className="space-y-1">
                <li>• <strong>Chính xác:</strong> Hiển thị đúng số liệu cần mua</li>
                <li>• <strong>Rõ ràng:</strong> Phân biệt tổng vs đã chọn</li>
                <li>• <strong>Tin cậy:</strong> Người dùng tin tưởng vào số liệu</li>
                <li>• <strong>Tiết kiệm:</strong> Biết chính xác số tiền cần chuẩn bị</li>
              </ul>
            </div>
            <div>
              <h5 className="font-medium mb-2">Workflow cải tiến:</h5>
              <ol className="space-y-1 list-decimal list-inside">
                <li>Thêm nhiều nhóm vào giỏ</li>
                <li>Chọn nhóm cần mua theo ngân sách</li>
                <li>Xem số liệu chính xác của nhóm đã chọn</li>
                <li>Tạo danh sách mua sắm tối ưu</li>
                <li>Đi chợ với số tiền đúng</li>
              </ol>
            </div>
          </div>
        </div>

        {/* Test Instructions */}
        <div className="bg-yellow-50 rounded-lg p-4 border border-yellow-200">
          <h4 className="font-semibold text-yellow-800 mb-3 flex items-center gap-2">
            <ShoppingCart className="h-4 w-4" />
            Hướng Dẫn Test
          </h4>
          <div className="grid md:grid-cols-2 gap-4 text-sm text-yellow-700">
            <div>
              <h5 className="font-medium mb-2">Cách test:</h5>
              <ol className="space-y-1 list-decimal list-inside">
                <li>Thêm nhiều món/bữa/thực đơn vào giỏ</li>
                <li>Vào trang giỏ hàng (/improved-shopping-cart)</li>
                <li>Bỏ chọn một số nhóm (uncheck)</li>
                <li>Quan sát số liệu thay đổi theo lựa chọn</li>
                <li>Kiểm tra QuickCartWidget cũng cập nhật</li>
              </ol>
            </div>
            <div>
              <h5 className="font-medium mb-2">Kết quả mong đợi:</h5>
              <ul className="space-y-1">
                <li>• Số liệu thay đổi khi chọn/bỏ chọn nhóm</li>
                <li>• "Tổng số món" chỉ tính nhóm đã chọn</li>
                <li>• "Tổng tiền" chỉ tính nhóm đã chọn</li>
                <li>• Nút "Tạo danh sách" hiển thị số đúng</li>
                <li>• QuickCartWidget sync với main cart</li>
              </ul>
            </div>
          </div>
        </div>

        {/* Demo Links */}
        <div className="flex items-center justify-center gap-4">
          <Button
            onClick={() => navigate('/improved-shopping-cart')}
            className="bg-green-600 hover:bg-green-700 text-white"
          >
            <ShoppingCart className="h-4 w-4 mr-2" />
            Test Giỏ Hàng Mới
          </Button>
          <Button
            onClick={() => navigate('/')}
            variant="outline"
          >
            <ArrowRight className="h-4 w-4 mr-2" />
            Thêm Món Vào Giỏ
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

export default DynamicCartSummary;

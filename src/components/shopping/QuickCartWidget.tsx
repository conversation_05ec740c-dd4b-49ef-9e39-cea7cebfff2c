import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from '@/components/ui/sheet';
import { 
  ShoppingCart, 
  X, 
  Plus, 
  Minus, 
  Trash2,
  DollarSign,
  Package,
  ArrowRight
} from 'lucide-react';
import { useShoppingCart } from '@/contexts/ShoppingCartContext';
import { cn } from '@/lib/utils';
import { useNavigate } from 'react-router-dom';
import { toast } from 'sonner';

interface QuickCartWidgetProps {
  className?: string;
  position?: 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left';
}

const QuickCartWidget: React.FC<QuickCartWidgetProps> = ({
  className,
  position = 'bottom-right'
}) => {
  const {
    sessions,
    items,
    totalItems,
    totalEstimatedCost,
    removeItem,
    updateItemQuantity,
    toggleItemChecked,
    toggleSessionSelected,
    selectAllSessions,
    deselectAllSessions,
    generateConsolidatedShoppingList,
    clearCart
  } = useShoppingCart();
  
  const [isOpen, setIsOpen] = useState(false);
  const navigate = useNavigate();

  const positionClasses = {
    'bottom-right': 'bottom-6 right-6',
    'bottom-left': 'bottom-6 left-6',
    'top-right': 'top-6 right-6',
    'top-left': 'top-6 left-6'
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND'
    }).format(price);
  };

  const handleViewFullCart = () => {
    setIsOpen(false);
    navigate('/shopping-cart');
  };

  const handleQuickCheckout = () => {
    setIsOpen(false);
    navigate('/checkout');
  };

  const selectedSessions = sessions.filter(session => session.isSelected);
  const selectedItemsCount = selectedSessions.reduce((sum, session) => sum + session.items.length, 0);
  const selectedTotalCost = selectedSessions.reduce((sum, session) => sum + session.totalEstimatedCost, 0);

  // Calculate total quantity for selected sessions
  const selectedTotalQuantity = selectedSessions.reduce((sum, session) =>
    sum + session.items.reduce((itemSum, item) => itemSum + item.quantity, 0), 0
  );

  if (totalItems === 0) {
    return null; // Don't show widget when cart is empty
  }

  return (
    <>
      {/* Floating Cart Button */}
      <div className={cn(
        'fixed z-50 transition-all duration-300',
        positionClasses[position],
        className
      )}>
        <Sheet open={isOpen} onOpenChange={setIsOpen}>
          <SheetTrigger asChild>
            <Button
              size="lg"
              className="relative bg-orange-600 hover:bg-orange-700 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-300 p-4"
            >
              <ShoppingCart className="h-6 w-6" />
              {totalItems > 0 && (
                <Badge 
                  className="absolute -top-2 -right-2 bg-red-500 text-white text-xs min-w-[20px] h-5 flex items-center justify-center rounded-full"
                >
                  {totalItems > 99 ? '99+' : totalItems}
                </Badge>
              )}
            </Button>
          </SheetTrigger>

          <SheetContent side="right" className="w-full sm:max-w-lg">
            <SheetHeader>
              <SheetTitle className="flex items-center gap-2">
                <ShoppingCart className="h-5 w-5" />
                Giỏ Hàng Của Bạn
              </SheetTitle>
              <SheetDescription>
                {totalItems} món trong {sessions.length} nhóm • {formatPrice(totalEstimatedCost)}
                {selectedSessions.length > 0 && (
                  <span className="block text-green-600 text-sm mt-1">
                    ✅ Đã chọn: {selectedTotalQuantity} món từ {selectedSessions.length} nhóm • {formatPrice(selectedTotalCost)}
                  </span>
                )}
              </SheetDescription>
            </SheetHeader>

            <div className="mt-6 space-y-4">
              {/* Cart Summary */}
              <Card>
                <CardContent className="p-4">
                  <div className="space-y-2 text-sm">
                    <div className="flex items-center justify-between">
                      <span className="text-gray-600">Tổng nhóm:</span>
                      <span className="font-medium">{sessions.length}</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-gray-600">Nhóm đã chọn:</span>
                      <span className="font-medium text-orange-600">{selectedSessions.length}</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-gray-600">Số món đã chọn:</span>
                      <span className="font-medium text-blue-600">{selectedTotalQuantity}</span>
                    </div>
                    <div className="flex items-center justify-between border-t pt-2">
                      <span className="text-gray-600">Tổng tiền dự kiến:</span>
                      <span className="font-bold text-green-600">{formatPrice(selectedTotalCost)}</span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Cart Items */}
              <ScrollArea className="h-[500px]">
                <div className="space-y-3">
                  {sessions.map((session) => (
                    <Card
                      key={session.id}
                      className={`border-l-4 transition-all ${
                        session.isSelected
                          ? 'border-l-green-500 bg-green-50'
                          : 'border-l-gray-300'
                      }`}
                    >
                      <CardHeader className="pb-3">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            <input
                              type="checkbox"
                              checked={session.isSelected || false}
                              onChange={() => toggleSessionSelected(session.id)}
                              className="rounded"
                            />
                            <span className="text-sm">{session.icon}</span>
                            <div className="flex-1 min-w-0">
                              <CardTitle className="text-sm font-medium text-gray-700 truncate">
                                {session.name}
                              </CardTitle>
                              {session.description && (
                                <p className="text-xs text-gray-500 truncate">{session.description}</p>
                              )}
                            </div>
                          </div>
                          <Badge variant="outline" className="text-xs">
                            {session.items.length} món
                          </Badge>
                        </div>
                      </CardHeader>
                      <CardContent className="pt-0">
                        <div className="space-y-3">
                          {session.items.map((item, index) => (
                            <div key={item.id} className="border rounded-lg p-3 bg-white">
                              <div className="flex items-start justify-between gap-3">
                                <div className="flex-1 min-w-0">
                                  <div className="flex items-center gap-2 mb-1">
                                    <input
                                      type="checkbox"
                                      checked={item.isChecked || false}
                                      onChange={() => toggleItemChecked(item.id)}
                                      className="rounded text-green-600"
                                    />
                                    <p className="font-medium text-gray-900 text-sm leading-tight">
                                      {item.name}
                                    </p>
                                  </div>

                                  <div className="flex items-center gap-4 text-xs text-gray-600 mb-2">
                                    <div className="flex items-center gap-1">
                                      <span className="font-medium text-blue-600">
                                        {item.quantity}
                                      </span>
                                      <span className="text-gray-500">{item.unit}</span>
                                    </div>
                                    <div className="flex items-center gap-1">
                                      <DollarSign className="h-3 w-3" />
                                      <span className="font-medium text-green-600">
                                        {formatPrice(item.estimatedPrice || 0)}
                                      </span>
                                    </div>
                                    {item.category && (
                                      <Badge variant="secondary" className="text-xs px-1 py-0">
                                        {item.category}
                                      </Badge>
                                    )}
                                  </div>

                                  {item.source && (
                                    <div className="text-xs text-gray-400">
                                      Từ: {item.source.name}
                                    </div>
                                  )}
                                </div>

                                <div className="flex flex-col items-end gap-2">
                                  <div className="flex items-center gap-1">
                                    <Button
                                      size="sm"
                                      variant="ghost"
                                      className="h-7 w-7 p-0 hover:bg-gray-100"
                                      onClick={() => updateItemQuantity(item.id, Math.max(0, item.quantity - 1))}
                                    >
                                      <Minus className="h-3 w-3" />
                                    </Button>
                                    <span className="text-sm font-medium w-8 text-center bg-gray-50 rounded px-1">
                                      {item.quantity}
                                    </span>
                                    <Button
                                      size="sm"
                                      variant="ghost"
                                      className="h-7 w-7 p-0 hover:bg-gray-100"
                                      onClick={() => updateItemQuantity(item.id, item.quantity + 1)}
                                    >
                                      <Plus className="h-3 w-3" />
                                    </Button>
                                  </div>
                                  <Button
                                    size="sm"
                                    variant="ghost"
                                    className="h-7 w-7 p-0 text-red-500 hover:text-red-700 hover:bg-red-50"
                                    onClick={() => removeItem(item.id)}
                                  >
                                    <X className="h-3 w-3" />
                                  </Button>
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </ScrollArea>

              {/* Quick Actions */}
              <div className="space-y-2 pt-4 border-t">
                <div className="flex items-center gap-2 mb-3">
                  <Button
                    onClick={selectAllSessions}
                    variant="outline"
                    size="sm"
                    className="flex-1"
                  >
                    Chọn tất cả
                  </Button>
                  <Button
                    onClick={deselectAllSessions}
                    variant="outline"
                    size="sm"
                    className="flex-1"
                  >
                    Bỏ chọn
                  </Button>
                </div>

                <Button
                  onClick={handleViewFullCart}
                  variant="outline"
                  className="w-full"
                >
                  <Package className="h-4 w-4 mr-2" />
                  Xem Giỏ Hàng Đầy Đủ
                </Button>

                <Button
                  onClick={handleQuickCheckout}
                  disabled={selectedSessions.length === 0}
                  className="w-full bg-green-600 hover:bg-green-700 text-white disabled:bg-gray-400"
                >
                  <ArrowRight className="h-4 w-4 mr-2" />
                  {selectedSessions.length > 0
                    ? `Đi Chợ Ngay (${selectedTotalQuantity} món • ${formatPrice(selectedTotalCost)})`
                    : 'Chọn nhóm để đi chợ'
                  }
                </Button>

                <Button
                  onClick={() => {
                    clearCart();
                    toast.success('Đã xóa toàn bộ giỏ hàng');
                    setIsOpen(false);
                  }}
                  variant="ghost"
                  size="sm"
                  className="w-full text-red-500 hover:text-red-700 hover:bg-red-50"
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  Xóa Toàn Bộ
                </Button>
              </div>
            </div>
          </SheetContent>
        </Sheet>
      </div>
    </>
  );
};

export default QuickCartWidget;

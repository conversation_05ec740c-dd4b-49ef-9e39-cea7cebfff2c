import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { 
  ShoppingCart, 
  Plus, 
  Minus, 
  Users, 
  Clock,
  ChefHat,
  Check
} from 'lucide-react';
import { useShoppingCart } from '@/contexts/ShoppingCartContext';
import { Recipe } from '@/types/kitchen';
import { MealSlot } from '@/contexts/MealPlanningContext';
import { toast } from 'sonner';

interface AddToCartButtonProps {
  recipe?: Recipe;
  meal?: MealSlot;
  variant?: 'default' | 'outline' | 'ghost' | 'secondary';
  size?: 'sm' | 'default' | 'lg';
  className?: string;
  showServingsDialog?: boolean;
  defaultServings?: number;
  children?: React.ReactNode;
}

const AddToCartButton: React.FC<AddToCartButtonProps> = ({
  recipe,
  meal,
  variant = 'default',
  size = 'default',
  className = '',
  showServingsDialog = true,
  defaultServings = 1,
  children
}) => {
  const { addRecipeToCart, addMealToCart } = useShoppingCart();
  const [servings, setServings] = useState(defaultServings);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isAdding, setIsAdding] = useState(false);

  // Get the recipe from either prop or meal
  const targetRecipe = recipe || meal?.recipe;

  if (!targetRecipe) {
    return null;
  }

  const handleQuickAdd = async () => {
    if (!showServingsDialog) {
      await handleAddToCart(defaultServings);
      return;
    }
    setIsDialogOpen(true);
  };

  const handleAddToCart = async (servingCount: number) => {
    setIsAdding(true);
    try {
      if (meal) {
        addMealToCart(meal);
      } else if (recipe) {
        addRecipeToCart(recipe, servingCount);
      }
      setIsDialogOpen(false);
    } catch (error) {
      console.error('Error adding to cart:', error);
      toast.error('Không thể thêm vào giỏ hàng');
    } finally {
      setIsAdding(false);
    }
  };

  const incrementServings = () => {
    setServings(prev => Math.min(prev + 1, 20));
  };

  const decrementServings = () => {
    setServings(prev => Math.max(prev - 1, 1));
  };

  const getIngredientCount = () => {
    if (!targetRecipe.ingredients) return 0;
    const ingredients = Array.isArray(targetRecipe.ingredients) 
      ? targetRecipe.ingredients 
      : JSON.parse(targetRecipe.ingredients || '[]');
    return ingredients.length;
  };

  const buttonContent = children || (
    <>
      <ShoppingCart className="h-4 w-4 mr-2" />
      Thêm vào giỏ
    </>
  );

  if (!showServingsDialog) {
    return (
      <Button
        variant={variant}
        size={size}
        className={className}
        onClick={handleQuickAdd}
        disabled={isAdding}
      >
        {buttonContent}
      </Button>
    );
  }

  return (
    <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
      <DialogTrigger asChild>
        <Button
          variant={variant}
          size={size}
          className={className}
          disabled={isAdding}
        >
          {buttonContent}
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            <ShoppingCart className="h-5 w-5 mr-2 text-orange-500" />
            Thêm vào giỏ hàng
          </DialogTitle>
          <DialogDescription>
            Chọn số khẩu phần để thêm nguyên liệu vào giỏ hàng
          </DialogDescription>
        </DialogHeader>

        {/* Recipe Info */}
        <div className="bg-gray-50 rounded-lg p-4 space-y-3">
          <div className="flex items-start space-x-3">
            {targetRecipe.image && (
              <img
                src={targetRecipe.image}
                alt={targetRecipe.title}
                className="w-16 h-16 rounded-lg object-cover"
              />
            )}
            <div className="flex-1">
              <h3 className="font-semibold text-gray-900">{targetRecipe.title}</h3>
              <div className="flex items-center space-x-4 mt-2 text-sm text-gray-600">
                {targetRecipe.cooking_time && (
                  <div className="flex items-center">
                    <Clock className="h-3 w-3 mr-1" />
                    {targetRecipe.cooking_time}
                  </div>
                )}
                {targetRecipe.servings && (
                  <div className="flex items-center">
                    <Users className="h-3 w-3 mr-1" />
                    {targetRecipe.servings} khẩu phần
                  </div>
                )}
                {targetRecipe.difficulty && (
                  <div className="flex items-center">
                    <ChefHat className="h-3 w-3 mr-1" />
                    {targetRecipe.difficulty}
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Ingredients Count */}
          <div className="flex items-center justify-between pt-2 border-t border-gray-200">
            <span className="text-sm text-gray-600">Nguyên liệu:</span>
            <Badge variant="secondary">
              {getIngredientCount()} món
            </Badge>
          </div>
        </div>

        {/* Servings Selector */}
        <div className="space-y-4">
          <Label htmlFor="servings" className="text-sm font-medium">
            Số khẩu phần
          </Label>
          <div className="flex items-center space-x-4">
            <Button
              variant="outline"
              size="sm"
              onClick={decrementServings}
              disabled={servings <= 1}
            >
              <Minus className="h-3 w-3" />
            </Button>
            <div className="flex-1">
              <Input
                id="servings"
                type="number"
                value={servings}
                onChange={(e) => {
                  const value = parseInt(e.target.value);
                  if (!isNaN(value) && value >= 1 && value <= 20) {
                    setServings(value);
                  }
                }}
                min={1}
                max={20}
                className="text-center"
              />
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={incrementServings}
              disabled={servings >= 20}
            >
              <Plus className="h-3 w-3" />
            </Button>
          </div>
          <p className="text-xs text-gray-500 text-center">
            Nguyên liệu sẽ được tính theo số khẩu phần đã chọn
          </p>
        </div>

        <DialogFooter className="flex space-x-2">
          <Button
            variant="outline"
            onClick={() => setIsDialogOpen(false)}
            disabled={isAdding}
          >
            Hủy
          </Button>
          <Button
            onClick={() => handleAddToCart(servings)}
            disabled={isAdding}
            className="flex-1"
          >
            {isAdding ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                Đang thêm...
              </>
            ) : (
              <>
                <Check className="h-4 w-4 mr-2" />
                Thêm {getIngredientCount()} nguyên liệu
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default AddToCartButton;

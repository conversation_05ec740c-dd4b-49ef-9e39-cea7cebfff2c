import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { 
  ShoppingCart, 
  Plus, 
  Check,
  Clock,
  Users,
  ChefHat
} from 'lucide-react';
import { useShoppingCart } from '@/contexts/ShoppingCartContext';
import { TodayMeals } from '@/types/kitchen';
import { MealSlot } from '@/contexts/MealPlanningContext';
import { toast } from 'sonner';

interface AddAllMealsToCartButtonProps {
  todayMeals?: TodayMeals;
  mealSlots?: MealSlot[];
  variant?: 'default' | 'outline' | 'ghost' | 'secondary';
  size?: 'sm' | 'default' | 'lg';
  className?: string;
  showConfirmDialog?: boolean;
  children?: React.ReactNode;
}

const AddAllMealsToCartButton: React.FC<AddAllMealsToCartButtonProps> = ({
  todayMeals,
  mealSlots,
  variant = 'default',
  size = 'default',
  className = '',
  showConfirmDialog = true,
  children
}) => {
  const { addMenuToCart } = useShoppingCart();
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isAdding, setIsAdding] = useState(false);

  // Get meals from either todayMeals or mealSlots
  const meals = React.useMemo(() => {
    if (mealSlots) {
      return mealSlots.filter(slot => slot.recipe);
    }
    
    if (todayMeals) {
      const mealList: MealSlot[] = [];
      Object.entries(todayMeals.meals).forEach(([mealType, meal]) => {
        if (meal && meal.recipe) {
          mealList.push({
            id: meal.id,
            date: new Date().toISOString().split('T')[0],
            mealType: mealType as 'breakfast' | 'lunch' | 'dinner' | 'snack',
            recipe: meal.recipe,
            notes: ''
          });
        }
      });
      return mealList;
    }
    
    return [];
  }, [todayMeals, mealSlots]);

  const handleQuickAdd = async () => {
    if (!showConfirmDialog) {
      await handleAddAllToCart();
      return;
    }
    setIsDialogOpen(true);
  };

  const handleAddAllToCart = async () => {
    if (meals.length === 0) {
      toast.error('Không có bữa ăn nào để thêm vào giỏ hàng');
      return;
    }

    setIsAdding(true);
    try {
      addMenuToCart(meals);
      setIsDialogOpen(false);
      toast.success(`Đã thêm nguyên liệu từ ${meals.length} bữa ăn vào giỏ hàng`);
    } catch (error) {
      console.error('Error adding all meals to cart:', error);
      toast.error('Không thể thêm vào giỏ hàng');
    } finally {
      setIsAdding(false);
    }
  };

  const getTotalIngredients = () => {
    return meals.reduce((total, meal) => {
      if (!meal.recipe?.ingredients) return total;
      const ingredients = Array.isArray(meal.recipe.ingredients) 
        ? meal.recipe.ingredients 
        : JSON.parse(meal.recipe.ingredients || '[]');
      return total + ingredients.length;
    }, 0);
  };

  const getMealTypeLabel = (type: string) => {
    const labels = {
      breakfast: 'Bữa sáng',
      lunch: 'Bữa trưa',
      dinner: 'Bữa tối',
      snack: 'Bữa phụ'
    };
    return labels[type as keyof typeof labels] || type;
  };

  const getMealTypeIcon = (type: string) => {
    const icons = {
      breakfast: '🌅',
      lunch: '☀️',
      dinner: '🌙',
      snack: '🍎'
    };
    return icons[type as keyof typeof icons] || '🍽️';
  };

  if (meals.length === 0) {
    return null;
  }

  const buttonContent = children || (
    <>
      <ShoppingCart className="h-4 w-4 mr-2" />
      Thêm tất cả
    </>
  );

  if (!showConfirmDialog) {
    return (
      <Button
        variant={variant}
        size={size}
        className={className}
        onClick={handleQuickAdd}
        disabled={isAdding}
      >
        {buttonContent}
      </Button>
    );
  }

  return (
    <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
      <DialogTrigger asChild>
        <Button
          variant={variant}
          size={size}
          className={className}
          disabled={isAdding}
        >
          {buttonContent}
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-lg">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            <ShoppingCart className="h-5 w-5 mr-2 text-orange-500" />
            Thêm tất cả vào giỏ hàng
          </DialogTitle>
          <DialogDescription>
            Thêm nguyên liệu từ tất cả bữa ăn hôm nay vào giỏ hàng
          </DialogDescription>
        </DialogHeader>

        {/* Meals Summary */}
        <div className="space-y-4">
          <div className="bg-gray-50 rounded-lg p-4">
            <h3 className="font-semibold text-gray-900 mb-3">Bữa ăn hôm nay</h3>
            <div className="space-y-3">
              {meals.map((meal) => (
                <div key={meal.id} className="flex items-center space-x-3">
                  <span className="text-lg">{getMealTypeIcon(meal.mealType)}</span>
                  <div className="flex-1">
                    <div className="flex items-center justify-between">
                      <span className="font-medium text-sm">
                        {getMealTypeLabel(meal.mealType)}
                      </span>
                      <Badge variant="secondary" className="text-xs">
                        {meal.recipe?.ingredients ? 
                          (Array.isArray(meal.recipe.ingredients) 
                            ? meal.recipe.ingredients.length 
                            : JSON.parse(meal.recipe.ingredients || '[]').length
                          ) : 0
                        } nguyên liệu
                      </Badge>
                    </div>
                    <p className="text-sm text-gray-600 truncate">
                      {meal.recipe?.title || meal.recipe?.name || 'Không có tên'}
                    </p>
                    {meal.recipe && (
                      <div className="flex items-center space-x-3 mt-1 text-xs text-gray-500">
                        {meal.recipe.cooking_time && (
                          <div className="flex items-center">
                            <Clock className="h-3 w-3 mr-1" />
                            {meal.recipe.cooking_time}
                          </div>
                        )}
                        {meal.recipe.servings && (
                          <div className="flex items-center">
                            <Users className="h-3 w-3 mr-1" />
                            {meal.recipe.servings} khẩu phần
                          </div>
                        )}
                        {meal.recipe.difficulty && (
                          <div className="flex items-center">
                            <ChefHat className="h-3 w-3 mr-1" />
                            {meal.recipe.difficulty}
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Summary Stats */}
          <div className="flex items-center justify-between pt-2 border-t border-gray-200">
            <span className="text-sm text-gray-600">Tổng nguyên liệu:</span>
            <Badge variant="secondary">
              {getTotalIngredients()} món
            </Badge>
          </div>
        </div>

        <DialogFooter className="flex space-x-2">
          <Button
            variant="outline"
            onClick={() => setIsDialogOpen(false)}
            disabled={isAdding}
          >
            Hủy
          </Button>
          <Button
            onClick={handleAddAllToCart}
            disabled={isAdding}
            className="flex-1"
          >
            {isAdding ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                Đang thêm...
              </>
            ) : (
              <>
                <Check className="h-4 w-4 mr-2" />
                Thêm {getTotalIngredients()} nguyên liệu
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default AddAllMealsToCartButton;

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { 
  Plus, 
  Edit, 
  Trash2, 
  Save, 
  X,
  Search,
  DollarSign,
  Package,
  Scale,
  TrendingUp
} from 'lucide-react';
import { IngredientPricingService, Ingredient } from '@/services/IngredientPricingService';
import { UnitConversionService, Unit } from '@/services/UnitConversionService';
import { toast } from 'sonner';

interface IngredientPricingAdminProps {
  className?: string;
}

const IngredientPricingAdmin: React.FC<IngredientPricingAdminProps> = ({ className }) => {
  const [pricingService] = useState(() => IngredientPricingService.getInstance());
  const [unitService] = useState(() => UnitConversionService.getInstance());
  
  const [ingredients, setIngredients] = useState<Ingredient[]>([]);
  const [units, setUnits] = useState<Unit[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [editingPrice, setEditingPrice] = useState<{
    ingredientId: string;
    unitId: string;
    price: number;
  } | null>(null);

  // Test data for demonstration
  const [testIngredient, setTestIngredient] = useState('200g thịt bò');
  const [testResult, setTestResult] = useState<any>(null);

  useEffect(() => {
    loadData();
  }, []);

  const loadData = () => {
    setIngredients(pricingService.getAllIngredients());
    setUnits([
      ...unitService.getUnitsByType('weight'),
      ...unitService.getUnitsByType('volume'),
      ...unitService.getUnitsByType('count')
    ]);
  };

  const categories = [
    { id: 'all', name: 'Tất cả' },
    { id: 'meat_seafood', name: 'Thịt & Hải sản' },
    { id: 'vegetables', name: 'Rau củ quả' },
    { id: 'grains_cereals', name: 'Ngũ cốc & Tinh bột' },
    { id: 'dairy_eggs', name: 'Sữa & Trứng' },
    { id: 'seasonings', name: 'Gia vị & Nước chấm' },
    { id: 'oils_fats', name: 'Dầu ăn & Chất béo' },
  ];

  const filteredIngredients = ingredients.filter(ingredient => {
    const matchesSearch = ingredient.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         ingredient.displayName.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = selectedCategory === 'all' || ingredient.categoryId === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND'
    }).format(price);
  };

  const handleUpdatePrice = (ingredientId: string, unitId: string, newPrice: number) => {
    const success = pricingService.updatePrice(ingredientId, unitId, newPrice);
    if (success) {
      toast.success('Đã cập nhật giá thành công');
      setEditingPrice(null);
    } else {
      toast.error('Không thể cập nhật giá');
    }
  };

  const handleTestPricing = () => {
    const result = pricingService.calculatePriceFromString(testIngredient);
    setTestResult(result);
  };

  const getUnitsByType = (type: 'weight' | 'volume' | 'count') => {
    return units.filter(unit => unit.type === type);
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <DollarSign className="h-5 w-5" />
            Quản Lý Giá Cả Nguyên Liệu
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-4 mb-4">
            <div className="flex-1">
              <Label htmlFor="search">Tìm kiếm nguyên liệu</Label>
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <Input
                  id="search"
                  placeholder="Nhập tên nguyên liệu..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <div>
              <Label htmlFor="category">Danh mục</Label>
              <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                <SelectTrigger className="w-48">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {categories.map(category => (
                    <SelectItem key={category.id} value={category.id}>
                      {category.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      <Tabs defaultValue="ingredients" className="space-y-4">
        <TabsList>
          <TabsTrigger value="ingredients">Nguyên liệu & Giá cả</TabsTrigger>
          <TabsTrigger value="units">Đơn vị đo lường</TabsTrigger>
          <TabsTrigger value="test">Test tính giá</TabsTrigger>
        </TabsList>

        {/* Ingredients Tab */}
        <TabsContent value="ingredients">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Package className="h-4 w-4" />
                Danh sách nguyên liệu ({filteredIngredients.length})
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ScrollArea className="h-96">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Nguyên liệu</TableHead>
                      <TableHead>Danh mục</TableHead>
                      <TableHead>Đơn vị cơ sở</TableHead>
                      <TableHead>Giá cơ sở</TableHead>
                      <TableHead>Thao tác</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredIngredients.map((ingredient) => (
                      <TableRow key={ingredient.id}>
                        <TableCell>
                          <div>
                            <div className="font-medium">{ingredient.displayName}</div>
                            <div className="text-sm text-gray-500">{ingredient.name}</div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge variant="outline">
                            {categories.find(c => c.id === ingredient.categoryId)?.name || ingredient.categoryId}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          {units.find(u => u.id === ingredient.baseUnitId)?.displayName || ingredient.baseUnitId}
                        </TableCell>
                        <TableCell>
                          {editingPrice?.ingredientId === ingredient.id ? (
                            <div className="flex items-center gap-2">
                              <Input
                                type="number"
                                value={editingPrice.price}
                                onChange={(e) => setEditingPrice({
                                  ...editingPrice,
                                  price: parseFloat(e.target.value) || 0
                                })}
                                className="w-24"
                              />
                              <Button
                                size="sm"
                                onClick={() => handleUpdatePrice(
                                  editingPrice.ingredientId,
                                  editingPrice.unitId,
                                  editingPrice.price
                                )}
                              >
                                <Save className="h-3 w-3" />
                              </Button>
                              <Button
                                size="sm"
                                variant="ghost"
                                onClick={() => setEditingPrice(null)}
                              >
                                <X className="h-3 w-3" />
                              </Button>
                            </div>
                          ) : (
                            <div className="flex items-center gap-2">
                              <span>{formatPrice(ingredient.basePricePerUnit)}</span>
                              <Button
                                size="sm"
                                variant="ghost"
                                onClick={() => setEditingPrice({
                                  ingredientId: ingredient.id,
                                  unitId: ingredient.baseUnitId,
                                  price: ingredient.basePricePerUnit
                                })}
                              >
                                <Edit className="h-3 w-3" />
                              </Button>
                            </div>
                          )}
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <Button size="sm" variant="outline">
                              <Edit className="h-3 w-3 mr-1" />
                              Sửa
                            </Button>
                            <Button size="sm" variant="outline" className="text-red-600">
                              <Trash2 className="h-3 w-3 mr-1" />
                              Xóa
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </ScrollArea>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Units Tab */}
        <TabsContent value="units">
          <div className="grid md:grid-cols-3 gap-4">
            {['weight', 'volume', 'count'].map((type) => (
              <Card key={type}>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Scale className="h-4 w-4" />
                    {type === 'weight' ? 'Khối lượng' : type === 'volume' ? 'Thể tích' : 'Đếm'}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <ScrollArea className="h-64">
                    <div className="space-y-2">
                      {getUnitsByType(type as any).map((unit) => (
                        <div key={unit.id} className="flex items-center justify-between p-2 border rounded">
                          <div>
                            <div className="font-medium">{unit.displayName}</div>
                            <div className="text-sm text-gray-500">
                              {unit.isBaseUnit ? 'Đơn vị cơ sở' : `× ${unit.conversionFactor}`}
                            </div>
                          </div>
                          {unit.isBaseUnit && (
                            <Badge variant="default">Base</Badge>
                          )}
                        </div>
                      ))}
                    </div>
                  </ScrollArea>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        {/* Test Tab */}
        <TabsContent value="test">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="h-4 w-4" />
                Test tính giá nguyên liệu
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="test-ingredient">Chuỗi nguyên liệu</Label>
                <div className="flex items-center gap-2">
                  <Input
                    id="test-ingredient"
                    placeholder="Ví dụ: 200g thịt bò, 1 củ hành, 2 muỗng canh nước mắm"
                    value={testIngredient}
                    onChange={(e) => setTestIngredient(e.target.value)}
                    className="flex-1"
                  />
                  <Button onClick={handleTestPricing}>
                    Tính giá
                  </Button>
                </div>
              </div>

              {testResult && (
                <Card className={testResult.success ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'}>
                  <CardContent className="p-4">
                    {testResult.success ? (
                      <div className="space-y-2">
                        <div className="flex items-center justify-between">
                          <span className="font-medium">Kết quả:</span>
                          <Badge className="bg-green-100 text-green-800">Thành công</Badge>
                        </div>
                        <div className="grid md:grid-cols-2 gap-4 text-sm">
                          <div>
                            <div><strong>Nguyên liệu:</strong> {testResult.ingredient?.displayName}</div>
                            <div><strong>Đơn vị sử dụng:</strong> {testResult.unitUsed}</div>
                            <div><strong>Khu vực:</strong> {testResult.region}</div>
                          </div>
                          <div>
                            <div><strong>Giá per đơn vị:</strong> {formatPrice(testResult.pricePerUnit)}</div>
                            <div><strong>Tổng giá:</strong> <span className="text-lg font-bold text-green-600">{formatPrice(testResult.totalPrice)}</span></div>
                          </div>
                        </div>
                        {testResult.breakdown && (
                          <div className="mt-3 p-3 bg-white rounded border">
                            <div className="text-sm font-medium mb-2">Chi tiết chuyển đổi:</div>
                            <div className="text-xs space-y-1">
                              <div>Số lượng gốc: {testResult.breakdown.originalQuantity} {testResult.breakdown.originalUnit}</div>
                              <div>Chuyển đổi: {testResult.breakdown.convertedQuantity} {testResult.breakdown.convertedUnit}</div>
                              <div>Giá per {testResult.breakdown.convertedUnit}: {formatPrice(testResult.breakdown.pricePerConvertedUnit)}</div>
                            </div>
                          </div>
                        )}
                      </div>
                    ) : (
                      <div className="space-y-2">
                        <div className="flex items-center justify-between">
                          <span className="font-medium">Kết quả:</span>
                          <Badge className="bg-red-100 text-red-800">Lỗi</Badge>
                        </div>
                        <div className="text-red-700">{testResult.error}</div>
                      </div>
                    )}
                  </CardContent>
                </Card>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default IngredientPricingAdmin;

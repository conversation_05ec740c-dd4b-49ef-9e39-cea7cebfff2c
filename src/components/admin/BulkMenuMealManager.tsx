import React, { useState } from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Trash2,
  Search,
  Filter,
  X,
  ChefHat,
  Menu,
  Calendar,
  Utensils,
  AlertTriangle
} from 'lucide-react';
import { useKitchen } from '@/contexts/KitchenContext';
import { useMealPlanning } from '@/contexts/MealPlanningContext';
import { toast } from 'sonner';

interface BulkMenuMealManagerProps {
  className?: string;
}

export const BulkMenuMealManager: React.FC<BulkMenuMealManagerProps> = ({ className }) => {
  const {
    recipes,
    removeMealFromSlot,
    todayMeals
  } = useKitchen();

  const {
    weekPlans,
    deleteWeekPlan,
    deleteDayPlan
  } = useMealPlanning();

  // State for selections
  const [selectedRecipes, setSelectedRecipes] = useState<Set<string>>(new Set());
  const [selectedMeals, setSelectedMeals] = useState<Set<string>>(new Set());
  const [selectedMenus, setSelectedMenus] = useState<Set<string>>(new Set());
  const [selectedWeekPlans, setSelectedWeekPlans] = useState<Set<string>>(new Set());

  // State for filters
  const [searchQuery, setSearchQuery] = useState('');
  const [categoryFilter, setCategoryFilter] = useState('all');
  const [difficultyFilter, setDifficultyFilter] = useState('all');

  // Mock data for menus (in real app, this would come from context/service)
  const [menus] = useState([
    {
      id: '1',
      name: 'Thực đơn gia đình tuần 1',
      description: 'Thực đơn cân bằng dinh dưỡng cho gia đình',
      category: 'family',
      meals: 21,
      createdAt: '2024-01-15'
    },
    {
      id: '2',
      name: 'Thực đơn giảm cân',
      description: 'Thực đơn low-carb cho người muốn giảm cân',
      category: 'diet',
      meals: 14,
      createdAt: '2024-01-10'
    }
  ]);

  // Filter functions
  const filteredRecipes = recipes.filter(recipe => {
    const matchesSearch = recipe.name.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesCategory = categoryFilter === 'all' || recipe.tags?.includes(categoryFilter);
    const matchesDifficulty = difficultyFilter === 'all' || recipe.difficulty === difficultyFilter;
    return matchesSearch && matchesCategory && matchesDifficulty;
  });

  const filteredMeals = todayMeals.filter(meal =>
    meal.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const filteredMenus = menus.filter(menu =>
    menu.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    menu.description.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const filteredWeekPlans = weekPlans.filter(plan =>
    plan.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // Selection handlers
  const handleSelectRecipe = (recipeId: string, checked: boolean) => {
    const newSelected = new Set(selectedRecipes);
    if (checked) {
      newSelected.add(recipeId);
    } else {
      newSelected.delete(recipeId);
    }
    setSelectedRecipes(newSelected);
  };

  const handleSelectMeal = (mealId: string, checked: boolean) => {
    const newSelected = new Set(selectedMeals);
    if (checked) {
      newSelected.add(mealId);
    } else {
      newSelected.delete(mealId);
    }
    setSelectedMeals(newSelected);
  };

  const handleSelectMenu = (menuId: string, checked: boolean) => {
    const newSelected = new Set(selectedMenus);
    if (checked) {
      newSelected.add(menuId);
    } else {
      newSelected.delete(menuId);
    }
    setSelectedMenus(newSelected);
  };

  const handleSelectWeekPlan = (planId: string, checked: boolean) => {
    const newSelected = new Set(selectedWeekPlans);
    if (checked) {
      newSelected.add(planId);
    } else {
      newSelected.delete(planId);
    }
    setSelectedWeekPlans(newSelected);
  };

  // Bulk selection handlers
  const handleSelectAllRecipes = () => {
    setSelectedRecipes(new Set(filteredRecipes.map(recipe => recipe.id)));
  };

  const handleSelectAllMeals = () => {
    setSelectedMeals(new Set(filteredMeals.map(meal => meal.id)));
  };

  const handleSelectAllMenus = () => {
    setSelectedMenus(new Set(filteredMenus.map(menu => menu.id)));
  };

  const handleSelectAllWeekPlans = () => {
    setSelectedWeekPlans(new Set(filteredWeekPlans.map(plan => plan.id)));
  };

  const clearAllSelections = () => {
    setSelectedRecipes(new Set());
    setSelectedMeals(new Set());
    setSelectedMenus(new Set());
    setSelectedWeekPlans(new Set());
  };

  // Delete handlers
  const handleDeleteSelectedRecipes = async () => {
    if (selectedRecipes.size === 0) {
      toast.error('Vui lòng chọn ít nhất một món ăn để xóa');
      return;
    }

    if (confirm(`Bạn có chắc chắn muốn xóa ${selectedRecipes.size} món ăn đã chọn?`)) {
      try {
        // In real app, call recipe deletion service
        // await Promise.all([...selectedRecipes].map(id => recipeService.deleteRecipe(id)));
        setSelectedRecipes(new Set());
        toast.success(`Đã xóa ${selectedRecipes.size} món ăn thành công`);
      } catch (error) {
        toast.error('Có lỗi xảy ra khi xóa món ăn');
      }
    }
  };

  const handleDeleteSelectedMeals = async () => {
    if (selectedMeals.size === 0) {
      toast.error('Vui lòng chọn ít nhất một bữa ăn để xóa');
      return;
    }

    if (confirm(`Bạn có chắc chắn muốn xóa ${selectedMeals.size} bữa ăn đã chọn?`)) {
      try {
        await Promise.all([...selectedMeals].map(id => removeMealFromSlot(id)));
        setSelectedMeals(new Set());
        toast.success(`Đã xóa ${selectedMeals.size} bữa ăn thành công`);
      } catch (error) {
        toast.error('Có lỗi xảy ra khi xóa bữa ăn');
      }
    }
  };

  const handleDeleteSelectedMenus = async () => {
    if (selectedMenus.size === 0) {
      toast.error('Vui lòng chọn ít nhất một thực đơn để xóa');
      return;
    }

    if (confirm(`Bạn có chắc chắn muốn xóa ${selectedMenus.size} thực đơn đã chọn?`)) {
      try {
        // In real app, call menu deletion service
        // await Promise.all([...selectedMenus].map(id => menuService.deleteMenu(id)));
        setSelectedMenus(new Set());
        toast.success(`Đã xóa ${selectedMenus.size} thực đơn thành công`);
      } catch (error) {
        toast.error('Có lỗi xảy ra khi xóa thực đơn');
      }
    }
  };

  const handleDeleteSelectedWeekPlans = async () => {
    if (selectedWeekPlans.size === 0) {
      toast.error('Vui lòng chọn ít nhất một kế hoạch tuần để xóa');
      return;
    }

    if (confirm(`Bạn có chắc chắn muốn xóa ${selectedWeekPlans.size} kế hoạch tuần đã chọn?`)) {
      try {
        await Promise.all([...selectedWeekPlans].map(id => deleteWeekPlan(id)));
        setSelectedWeekPlans(new Set());
        toast.success(`Đã xóa ${selectedWeekPlans.size} kế hoạch tuần thành công`);
      } catch (error) {
        toast.error('Có lỗi xảy ra khi xóa kế hoạch tuần');
      }
    }
  };

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Trash2 className="h-5 w-5 text-red-500" />
          Quản Lý Xóa Thực Đơn & Bữa Ăn
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {/* Search and Filters */}
          <div className="flex gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Tìm kiếm..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Select value={categoryFilter} onValueChange={setCategoryFilter}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="Danh mục" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Tất cả danh mục</SelectItem>
                <SelectItem value="family">Gia đình</SelectItem>
                <SelectItem value="diet">Ăn kiêng</SelectItem>
                <SelectItem value="vegetarian">Chay</SelectItem>
              </SelectContent>
            </Select>
            <Select value={difficultyFilter} onValueChange={setDifficultyFilter}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="Độ khó" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Tất cả</SelectItem>
                <SelectItem value="easy">Dễ</SelectItem>
                <SelectItem value="medium">Trung bình</SelectItem>
                <SelectItem value="hard">Khó</SelectItem>
              </SelectContent>
            </Select>
            <Button variant="outline" onClick={() => {
              setSearchQuery('');
              setCategoryFilter('all');
              setDifficultyFilter('all');
            }}>
              <X className="h-4 w-4 mr-2" />
              Xóa bộ lọc
            </Button>
          </div>

          {/* Global Actions */}
          <div className="flex gap-2">
            <Button variant="outline" size="sm" onClick={clearAllSelections}>
              <X className="h-4 w-4 mr-2" />
              Bỏ chọn tất cả
            </Button>
          </div>

          {/* Tabs for different content types */}
          <Tabs defaultValue="recipes" className="w-full">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="recipes" className="flex items-center gap-2">
                <ChefHat className="h-4 w-4" />
                Món ăn ({selectedRecipes.size})
              </TabsTrigger>
              <TabsTrigger value="meals" className="flex items-center gap-2">
                <Utensils className="h-4 w-4" />
                Bữa ăn ({selectedMeals.size})
              </TabsTrigger>
              <TabsTrigger value="menus" className="flex items-center gap-2">
                <Menu className="h-4 w-4" />
                Thực đơn ({selectedMenus.size})
              </TabsTrigger>
              <TabsTrigger value="weekplans" className="flex items-center gap-2">
                <Calendar className="h-4 w-4" />
                Kế hoạch tuần ({selectedWeekPlans.size})
              </TabsTrigger>
            </TabsList>

            {/* Recipes Tab */}
            <TabsContent value="recipes" className="space-y-4">
              <div className="flex justify-between items-center">
                <div className="flex gap-2">
                  <Button variant="outline" size="sm" onClick={handleSelectAllRecipes}>
                    Chọn tất cả ({filteredRecipes.length})
                  </Button>
                  <Button variant="outline" size="sm" onClick={() => setSelectedRecipes(new Set())}>
                    Bỏ chọn tất cả
                  </Button>
                </div>
                <Button
                  variant="destructive"
                  size="sm"
                  onClick={handleDeleteSelectedRecipes}
                  disabled={selectedRecipes.size === 0}
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  Xóa đã chọn ({selectedRecipes.size})
                </Button>
              </div>

              <ScrollArea className="h-96 border rounded-lg p-4">
                <div className="space-y-2">
                  {filteredRecipes.length === 0 ? (
                    <p className="text-center text-gray-500 py-8">Không tìm thấy món ăn nào</p>
                  ) : (
                    filteredRecipes.map(recipe => (
                      <div key={recipe.id} className="flex items-center space-x-3 p-3 hover:bg-gray-50 rounded-lg border">
                        <Checkbox
                          checked={selectedRecipes.has(recipe.id)}
                          onCheckedChange={(checked) => handleSelectRecipe(recipe.id, checked as boolean)}
                        />
                        <div className="flex-1">
                          <div className="font-medium">{recipe.name}</div>
                          <div className="text-sm text-gray-500">
                            {recipe.difficulty} • {recipe.prepTime + recipe.cookTime} phút • {recipe.servings} khẩu phần
                          </div>
                          {recipe.tags && (
                            <div className="flex gap-1 mt-1">
                              {recipe.tags.slice(0, 3).map(tag => (
                                <Badge key={tag} variant="secondary" className="text-xs">
                                  {tag}
                                </Badge>
                              ))}
                            </div>
                          )}
                        </div>
                        <Badge variant="outline">
                          {recipe.rating}/5 ⭐
                        </Badge>
                      </div>
                    ))
                  )}
                </div>
              </ScrollArea>
            </TabsContent>

            {/* Meals Tab */}
            <TabsContent value="meals" className="space-y-4">
              <div className="flex justify-between items-center">
                <div className="flex gap-2">
                  <Button variant="outline" size="sm" onClick={handleSelectAllMeals}>
                    Chọn tất cả ({filteredMeals.length})
                  </Button>
                  <Button variant="outline" size="sm" onClick={() => setSelectedMeals(new Set())}>
                    Bỏ chọn tất cả
                  </Button>
                </div>
                <Button
                  variant="destructive"
                  size="sm"
                  onClick={handleDeleteSelectedMeals}
                  disabled={selectedMeals.size === 0}
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  Xóa đã chọn ({selectedMeals.size})
                </Button>
              </div>

              <ScrollArea className="h-96 border rounded-lg p-4">
                <div className="space-y-2">
                  {filteredMeals.length === 0 ? (
                    <p className="text-center text-gray-500 py-8">Không có bữa ăn nào</p>
                  ) : (
                    filteredMeals.map(meal => (
                      <div key={meal.id} className="flex items-center space-x-3 p-3 hover:bg-gray-50 rounded-lg border">
                        <Checkbox
                          checked={selectedMeals.has(meal.id)}
                          onCheckedChange={(checked) => handleSelectMeal(meal.id, checked as boolean)}
                        />
                        <div className="flex-1">
                          <div className="font-medium">{meal.name}</div>
                          <div className="text-sm text-gray-500">
                            {meal.mealType} • {meal.scheduledTime}
                          </div>
                          <div className="text-xs text-gray-400">
                            {meal.description}
                          </div>
                        </div>
                        <Badge variant={meal.isCompleted ? "default" : "secondary"}>
                          {meal.isCompleted ? "Hoàn thành" : "Chưa hoàn thành"}
                        </Badge>
                      </div>
                    ))
                  )}
                </div>
              </ScrollArea>
            </TabsContent>

            {/* Menus Tab */}
            <TabsContent value="menus" className="space-y-4">
              <div className="flex justify-between items-center">
                <div className="flex gap-2">
                  <Button variant="outline" size="sm" onClick={handleSelectAllMenus}>
                    Chọn tất cả ({filteredMenus.length})
                  </Button>
                  <Button variant="outline" size="sm" onClick={() => setSelectedMenus(new Set())}>
                    Bỏ chọn tất cả
                  </Button>
                </div>
                <Button
                  variant="destructive"
                  size="sm"
                  onClick={handleDeleteSelectedMenus}
                  disabled={selectedMenus.size === 0}
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  Xóa đã chọn ({selectedMenus.size})
                </Button>
              </div>

              <ScrollArea className="h-96 border rounded-lg p-4">
                <div className="space-y-2">
                  {filteredMenus.length === 0 ? (
                    <p className="text-center text-gray-500 py-8">Không tìm thấy thực đơn nào</p>
                  ) : (
                    filteredMenus.map(menu => (
                      <div key={menu.id} className="flex items-center space-x-3 p-3 hover:bg-gray-50 rounded-lg border">
                        <Checkbox
                          checked={selectedMenus.has(menu.id)}
                          onCheckedChange={(checked) => handleSelectMenu(menu.id, checked as boolean)}
                        />
                        <div className="flex-1">
                          <div className="font-medium">{menu.name}</div>
                          <div className="text-sm text-gray-500">{menu.description}</div>
                          <div className="text-xs text-gray-400">
                            {menu.meals} bữa ăn • Tạo ngày {new Date(menu.createdAt).toLocaleDateString('vi-VN')}
                          </div>
                        </div>
                        <Badge variant="outline">
                          {menu.category}
                        </Badge>
                      </div>
                    ))
                  )}
                </div>
              </ScrollArea>
            </TabsContent>

            {/* Week Plans Tab */}
            <TabsContent value="weekplans" className="space-y-4">
              <div className="flex justify-between items-center">
                <div className="flex gap-2">
                  <Button variant="outline" size="sm" onClick={handleSelectAllWeekPlans}>
                    Chọn tất cả ({filteredWeekPlans.length})
                  </Button>
                  <Button variant="outline" size="sm" onClick={() => setSelectedWeekPlans(new Set())}>
                    Bỏ chọn tất cả
                  </Button>
                </div>
                <Button
                  variant="destructive"
                  size="sm"
                  onClick={handleDeleteSelectedWeekPlans}
                  disabled={selectedWeekPlans.size === 0}
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  Xóa đã chọn ({selectedWeekPlans.size})
                </Button>
              </div>

              <ScrollArea className="h-96 border rounded-lg p-4">
                <div className="space-y-2">
                  {filteredWeekPlans.length === 0 ? (
                    <p className="text-center text-gray-500 py-8">Không có kế hoạch tuần nào</p>
                  ) : (
                    filteredWeekPlans.map(plan => (
                      <div key={plan.id} className="flex items-center space-x-3 p-3 hover:bg-gray-50 rounded-lg border">
                        <Checkbox
                          checked={selectedWeekPlans.has(plan.id)}
                          onCheckedChange={(checked) => handleSelectWeekPlan(plan.id, checked as boolean)}
                        />
                        <div className="flex-1">
                          <div className="font-medium">{plan.name}</div>
                          <div className="text-sm text-gray-500">
                            {plan.startDate} - {plan.endDate}
                          </div>
                          <div className="text-xs text-gray-400">
                            {plan.days.length} ngày • {plan.totalCalories} calories/ngày
                          </div>
                        </div>
                        <Badge variant={plan.isActive ? "default" : "secondary"}>
                          {plan.isActive ? "Đang hoạt động" : "Không hoạt động"}
                        </Badge>
                      </div>
                    ))
                  )}
                </div>
              </ScrollArea>
            </TabsContent>
          </Tabs>

          {/* Warning Message */}
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <div className="flex items-start gap-3">
              <AlertTriangle className="h-5 w-5 text-yellow-600 mt-0.5" />
              <div>
                <h4 className="font-medium text-yellow-800">Cảnh báo</h4>
                <p className="text-sm text-yellow-700 mt-1">
                  Việc xóa sẽ không thể hoàn tác. Hãy chắc chắn rằng bạn muốn xóa các mục đã chọn.
                  Xóa thực đơn hoặc kế hoạch tuần có thể ảnh hưởng đến lịch trình bữa ăn hiện tại.
                </p>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
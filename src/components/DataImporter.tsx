import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  Database, 
  Upload, 
  CheckCircle, 
  AlertCircle, 
  RefreshCw,
  Trash2
} from 'lucide-react';
import { kitchenService, vietnameseRecipes } from '@/services/kitchenService';
import { useAuth } from '@/contexts/AuthContext';
import { toast } from 'sonner';

interface ImportProgress {
  total: number;
  current: number;
  status: 'idle' | 'importing' | 'success' | 'error';
  message: string;
}

const DataImporter: React.FC = () => {
  const { user } = useAuth();
  const [progress, setProgress] = useState<ImportProgress>({
    total: 0,
    current: 0,
    status: 'idle',
    message: ''
  });
  const [existingRecipes, setExistingRecipes] = useState<number>(0);

  const checkExistingData = async () => {
    try {
      const recipes = await kitchenService.getRecipes();
      setExistingRecipes(recipes.length);
      console.log(`Found ${recipes.length} existing recipes in database`);
    } catch (error) {
      console.error('Error checking existing data:', error);
      setExistingRecipes(0);
    }
  };

  const importRecipes = async () => {
    if (!user) {
      toast.error('Bạn cần đăng nhập để import dữ liệu');
      return;
    }

    setProgress({
      total: vietnameseRecipes.length,
      current: 0,
      status: 'importing',
      message: 'Bắt đầu import recipes...'
    });

    try {
      for (let i = 0; i < vietnameseRecipes.length; i++) {
        const recipe = vietnameseRecipes[i];
        
        setProgress(prev => ({
          ...prev,
          current: i + 1,
          message: `Đang import: ${recipe.name}`
        }));

        // Convert recipe to match database schema
        const recipeToImport = {
          ...recipe,
          title: recipe.name || recipe.title, // Map name to title
          name: undefined, // Remove name field to avoid conflicts
          userId: user.id,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        };

        await kitchenService.createRecipe(recipeToImport);
        
        // Small delay to avoid overwhelming the database
        await new Promise(resolve => setTimeout(resolve, 100));
      }

      setProgress(prev => ({
        ...prev,
        status: 'success',
        message: `Đã import thành công ${vietnameseRecipes.length} recipes!`
      }));

      toast.success(`Import thành công ${vietnameseRecipes.length} recipes!`);
      await checkExistingData();

    } catch (error) {
      console.error('Error importing recipes:', error);
      setProgress(prev => ({
        ...prev,
        status: 'error',
        message: `Lỗi import: ${error.message}`
      }));
      toast.error('Có lỗi xảy ra khi import dữ liệu');
    }
  };

  const clearAllRecipes = async () => {
    if (!user) {
      toast.error('Bạn cần đăng nhập để xóa dữ liệu');
      return;
    }

    if (!confirm('Bạn có chắc chắn muốn xóa tất cả recipes? Hành động này không thể hoàn tác!')) {
      return;
    }

    try {
      const recipes = await kitchenService.getRecipes();
      
      setProgress({
        total: recipes.length,
        current: 0,
        status: 'importing',
        message: 'Đang xóa recipes...'
      });

      for (let i = 0; i < recipes.length; i++) {
        const recipe = recipes[i];
        
        setProgress(prev => ({
          ...prev,
          current: i + 1,
          message: `Đang xóa: ${recipe.title || recipe.name}`
        }));

        await kitchenService.deleteRecipe(recipe.id);
        await new Promise(resolve => setTimeout(resolve, 50));
      }

      setProgress(prev => ({
        ...prev,
        status: 'success',
        message: `Đã xóa thành công ${recipes.length} recipes!`
      }));

      toast.success(`Đã xóa ${recipes.length} recipes!`);
      await checkExistingData();

    } catch (error) {
      console.error('Error clearing recipes:', error);
      setProgress(prev => ({
        ...prev,
        status: 'error',
        message: `Lỗi xóa: ${error.message}`
      }));
      toast.error('Có lỗi xảy ra khi xóa dữ liệu');
    }
  };

  React.useEffect(() => {
    checkExistingData();
  }, []);

  const progressPercentage = progress.total > 0 ? (progress.current / progress.total) * 100 : 0;

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Database className="h-5 w-5" />
          Data Importer - Supabase
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Status */}
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm text-gray-600">Recipes hiện có trong database:</p>
            <Badge variant="outline" className="mt-1">
              {existingRecipes} recipes
            </Badge>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={checkExistingData}
            className="flex items-center gap-2"
          >
            <RefreshCw className="h-4 w-4" />
            Refresh
          </Button>
        </div>

        {/* Progress */}
        {progress.status !== 'idle' && (
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">{progress.message}</span>
              <span className="text-sm text-gray-500">
                {progress.current}/{progress.total}
              </span>
            </div>
            <Progress value={progressPercentage} className="w-full" />
          </div>
        )}

        {/* Status Icon */}
        {progress.status === 'success' && (
          <div className="flex items-center gap-2 text-green-600">
            <CheckCircle className="h-5 w-5" />
            <span className="text-sm">{progress.message}</span>
          </div>
        )}

        {progress.status === 'error' && (
          <div className="flex items-center gap-2 text-red-600">
            <AlertCircle className="h-5 w-5" />
            <span className="text-sm">{progress.message}</span>
          </div>
        )}

        {/* Actions */}
        <div className="flex gap-3">
          <Button
            onClick={importRecipes}
            disabled={progress.status === 'importing' || !user}
            className="flex items-center gap-2"
          >
            <Upload className="h-4 w-4" />
            Import {vietnameseRecipes.length} Vietnamese Recipes
          </Button>

          <Button
            variant="destructive"
            onClick={clearAllRecipes}
            disabled={progress.status === 'importing' || !user || existingRecipes === 0}
            className="flex items-center gap-2"
          >
            <Trash2 className="h-4 w-4" />
            Clear All Recipes
          </Button>
        </div>

        {!user && (
          <p className="text-sm text-amber-600 bg-amber-50 p-3 rounded-lg">
            ⚠️ Bạn cần đăng nhập để sử dụng chức năng import/export dữ liệu
          </p>
        )}

        {/* Info */}
        <div className="text-xs text-gray-500 bg-gray-50 p-3 rounded-lg">
          <p><strong>Lưu ý:</strong></p>
          <ul className="list-disc list-inside mt-1 space-y-1">
            <li>Import sẽ thêm {vietnameseRecipes.length} recipes Việt Nam vào Supabase</li>
            <li>Clear sẽ xóa tất cả recipes hiện có</li>
            <li>Dữ liệu sẽ được lưu với user ID của bạn</li>
          </ul>
        </div>
      </CardContent>
    </Card>
  );
};

export default DataImporter;

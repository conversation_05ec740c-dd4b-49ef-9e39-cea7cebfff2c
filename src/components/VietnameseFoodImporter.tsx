import React, { useState, useEffect } from 'react';
import { vietnameseFoodImportService } from '../services/VietnameseFoodImportService';
import { vietnameseFoodScrapingService } from '../services/VietnameseFoodScrapingService';

interface ImportStats {
  totalRecipes: number;
  byCategory: { [key: string]: number };
  byRegion: { [key: string]: number };
  byMealType: { [key: string]: number };
}

interface ImportProgress {
  isRunning: boolean;
  currentStep: string;
  progress: number;
  result?: any;
}

export const VietnameseFoodImporter: React.FC = () => {
  const [stats, setStats] = useState<ImportStats | null>(null);
  const [progress, setProgress] = useState<ImportProgress>({
    isRunning: false,
    currentStep: '',
    progress: 0
  });
  const [options, setOptions] = useState({
    source: 'local' as 'local' | 'scraping' | 'mixed',
    validateData: true,
    skipDuplicates: true,
    updateExisting: false,
    clearExisting: false,
    selectedSources: [] as string[]
  });

  const supportedSources = vietnameseFoodScrapingService.getSupportedSources();

  useEffect(() => {
    loadStats();
  }, []);

  const loadStats = async () => {
    try {
      const currentStats = await vietnameseFoodImportService.getImportStats();
      setStats(currentStats);
    } catch (error) {
      console.error('Error loading stats:', error);
    }
  };

  const handleImport = async () => {
    setProgress({
      isRunning: true,
      currentStep: 'Bắt đầu import...',
      progress: 0
    });

    try {
      // Clear existing data if requested
      if (options.clearExisting) {
        setProgress(prev => ({
          ...prev,
          currentStep: 'Xóa dữ liệu cũ...',
          progress: 10
        }));

        await vietnameseFoodImportService.clearImportedData();
      }

      setProgress(prev => ({
        ...prev,
        currentStep: 'Đang import dữ liệu...',
        progress: 30
      }));

      let result;
      switch (options.source) {
        case 'local':
          result = await vietnameseFoodImportService.importLocalData({
            validateData: options.validateData,
            skipDuplicates: options.skipDuplicates,
            updateExisting: options.updateExisting,
            batchSize: 10
          });
          break;

        case 'scraping':
          result = await vietnameseFoodImportService.importScrapedData(
            options.selectedSources,
            {
              validateData: options.validateData,
              skipDuplicates: options.skipDuplicates,
              updateExisting: options.updateExisting,
              batchSize: 5
            }
          );
          break;

        case 'mixed':
          // Import local first
          setProgress(prev => ({
            ...prev,
            currentStep: 'Import dữ liệu local...',
            progress: 40
          }));

          const localResult = await vietnameseFoodImportService.importLocalData({
            validateData: options.validateData,
            skipDuplicates: options.skipDuplicates,
            updateExisting: options.updateExisting,
            batchSize: 10
          });

          setProgress(prev => ({
            ...prev,
            currentStep: 'Import dữ liệu scraped...',
            progress: 70
          }));

          const scrapedResult = await vietnameseFoodImportService.importScrapedData(
            options.selectedSources,
            {
              validateData: options.validateData,
              skipDuplicates: options.skipDuplicates,
              updateExisting: options.updateExisting,
              batchSize: 5
            }
          );

          result = {
            success: localResult.success && scrapedResult.success,
            totalProcessed: localResult.totalProcessed + scrapedResult.totalProcessed,
            totalImported: localResult.totalImported + scrapedResult.totalImported,
            totalSkipped: localResult.totalSkipped + scrapedResult.totalSkipped,
            totalErrors: localResult.totalErrors + scrapedResult.totalErrors,
            errors: [...localResult.errors, ...scrapedResult.errors],
            duration: localResult.duration + scrapedResult.duration
          };
          break;
      }

      setProgress({
        isRunning: false,
        currentStep: 'Hoàn thành!',
        progress: 100,
        result
      });

      // Reload stats
      await loadStats();

    } catch (error) {
      setProgress({
        isRunning: false,
        currentStep: 'Lỗi!',
        progress: 0,
        result: { success: false, error: error.toString() }
      });
    }
  };

  const handleClearData = async () => {
    if (!confirm('Bạn có chắc chắn muốn xóa tất cả dữ liệu đã import?')) {
      return;
    }

    try {
      setProgress({
        isRunning: true,
        currentStep: 'Đang xóa dữ liệu...',
        progress: 50
      });

      const result = await vietnameseFoodImportService.clearImportedData();

      setProgress({
        isRunning: false,
        currentStep: 'Đã xóa xong!',
        progress: 100,
        result: { deleted: result.deleted, errors: result.errors }
      });

      await loadStats();
    } catch (error) {
      setProgress({
        isRunning: false,
        currentStep: 'Lỗi khi xóa!',
        progress: 0,
        result: { success: false, error: error.toString() }
      });
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6 bg-white rounded-lg shadow-lg">
      <h2 className="text-2xl font-bold text-gray-800 mb-6">
        🍜 Import Món Ăn Việt Nam
      </h2>

      {/* Current Stats */}
      <div className="mb-8">
        <h3 className="text-lg font-semibold text-gray-700 mb-4">📊 Thống Kê Hiện Tại</h3>
        {stats ? (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="bg-blue-50 p-4 rounded-lg">
              <div className="text-2xl font-bold text-blue-600">{stats.totalRecipes}</div>
              <div className="text-sm text-gray-600">Tổng số món ăn</div>
            </div>

            <div className="bg-green-50 p-4 rounded-lg">
              <div className="text-lg font-semibold text-green-600">Theo vùng miền</div>
              <div className="text-sm text-gray-600">
                {Object.entries(stats.byRegion).map(([region, count]) => (
                  <div key={region}>{region}: {count}</div>
                ))}
              </div>
            </div>

            <div className="bg-purple-50 p-4 rounded-lg">
              <div className="text-lg font-semibold text-purple-600">Theo bữa ăn</div>
              <div className="text-sm text-gray-600">
                {Object.entries(stats.byMealType).map(([mealType, count]) => (
                  <div key={mealType}>{mealType}: {count}</div>
                ))}
              </div>
            </div>
          </div>
        ) : (
          <div className="text-gray-500">Đang tải thống kê...</div>
        )}
      </div>

      {/* Import Options */}
      <div className="mb-8">
        <h3 className="text-lg font-semibold text-gray-700 mb-4">⚙️ Tùy Chọn Import</h3>

        <div className="space-y-4">
          {/* Source Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Nguồn dữ liệu
            </label>
            <select
              value={options.source}
              onChange={(e) => setOptions(prev => ({
                ...prev,
                source: e.target.value as 'local' | 'scraping' | 'mixed'
              }))}
              className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500"
              disabled={progress.isRunning}
            >
              <option value="local">Dữ liệu local (nhanh, ổn định)</option>
              <option value="scraping">Cào từ web (chậm, có thể lỗi)</option>
              <option value="mixed">Kết hợp cả hai</option>
            </select>
          </div>

          {/* Scraping Sources */}
          {(options.source === 'scraping' || options.source === 'mixed') && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Chọn nguồn cào dữ liệu
              </label>
              <div className="space-y-2">
                {supportedSources.map(source => (
                  <label key={source} className="flex items-center">
                    <input
                      type="checkbox"
                      checked={options.selectedSources.includes(source)}
                      onChange={(e) => {
                        if (e.target.checked) {
                          setOptions(prev => ({
                            ...prev,
                            selectedSources: [...prev.selectedSources, source]
                          }));
                        } else {
                          setOptions(prev => ({
                            ...prev,
                            selectedSources: prev.selectedSources.filter(s => s !== source)
                          }));
                        }
                      }}
                      className="mr-2"
                      disabled={progress.isRunning}
                    />
                    <span className="text-sm text-gray-700">{source}</span>
                  </label>
                ))}
              </div>
            </div>
          )}

          {/* Other Options */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={options.validateData}
                onChange={(e) => setOptions(prev => ({ ...prev, validateData: e.target.checked }))}
                className="mr-2"
                disabled={progress.isRunning}
              />
              <span className="text-sm text-gray-700">Validate dữ liệu</span>
            </label>

            <label className="flex items-center">
              <input
                type="checkbox"
                checked={options.skipDuplicates}
                onChange={(e) => setOptions(prev => ({ ...prev, skipDuplicates: e.target.checked }))}
                className="mr-2"
                disabled={progress.isRunning}
              />
              <span className="text-sm text-gray-700">Bỏ qua trùng lặp</span>
            </label>

            <label className="flex items-center">
              <input
                type="checkbox"
                checked={options.updateExisting}
                onChange={(e) => setOptions(prev => ({ ...prev, updateExisting: e.target.checked }))}
                className="mr-2"
                disabled={progress.isRunning}
              />
              <span className="text-sm text-gray-700">Cập nhật món đã có</span>
            </label>

            <label className="flex items-center">
              <input
                type="checkbox"
                checked={options.clearExisting}
                onChange={(e) => setOptions(prev => ({ ...prev, clearExisting: e.target.checked }))}
                className="mr-2"
                disabled={progress.isRunning}
              />
              <span className="text-sm text-gray-700 text-red-600">Xóa dữ liệu cũ trước</span>
            </label>
          </div>
        </div>
      </div>

      {/* Progress */}
      {progress.isRunning && (
        <div className="mb-6">
          <div className="text-sm text-gray-600 mb-2">{progress.currentStep}</div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div
              className="bg-blue-600 h-2 rounded-full transition-all duration-300"
              style={{ width: `${progress.progress}%` }}
            ></div>
          </div>
        </div>
      )}

      {/* Results */}
      {progress.result && (
        <div className="mb-6">
          <h3 className="text-lg font-semibold text-gray-700 mb-2">📈 Kết Quả</h3>
          <div className={`p-4 rounded-lg ${progress.result.success ? 'bg-green-50' : 'bg-red-50'}`}>
            {progress.result.success ? (
              <div className="text-green-800">
                <div>✅ Import thành công!</div>
                <div className="text-sm mt-2">
                  <div>Đã xử lý: {progress.result.totalProcessed}</div>
                  <div>Đã import: {progress.result.totalImported}</div>
                  <div>Đã bỏ qua: {progress.result.totalSkipped}</div>
                  <div>Lỗi: {progress.result.totalErrors}</div>
                  <div>Thời gian: {(progress.result.duration / 1000).toFixed(2)}s</div>
                </div>
              </div>
            ) : (
              <div className="text-red-800">
                <div>❌ Import thất bại!</div>
                <div className="text-sm mt-2">{progress.result.error}</div>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Action Buttons */}
      <div className="flex space-x-4">
        <button
          onClick={handleImport}
          disabled={progress.isRunning}
          className="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed"
        >
          {progress.isRunning ? 'Đang import...' : '🚀 Bắt đầu Import'}
        </button>

        <button
          onClick={handleClearData}
          disabled={progress.isRunning}
          className="px-6 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 disabled:bg-gray-400 disabled:cursor-not-allowed"
        >
          🗑️ Xóa tất cả dữ liệu
        </button>

        <button
          onClick={loadStats}
          disabled={progress.isRunning}
          className="px-6 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 disabled:bg-gray-400 disabled:cursor-not-allowed"
        >
          🔄 Refresh thống kê
        </button>
      </div>
    </div>
  );
};
import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Database, 
  Upload, 
  Download, 
  CheckCircle, 
  AlertTriangle, 
  RefreshCw,
  HardDrive,
  Cloud,
  ArrowRight,
  Trash2
} from 'lucide-react';
import { 
  DataMigrationService, 
  type MigrationResult, 
  type MigrationProgress 
} from '@/services/DataMigrationService';

const DataMigrationPanel: React.FC = () => {
  const [migrationService] = useState(() => new DataMigrationService());
  const [migrationStatus, setMigrationStatus] = useState<{
    needed: boolean;
    localDataCount: number;
    supabaseDataCount: number;
    details: any;
  } | null>(null);
  const [isChecking, setIsChecking] = useState(false);
  const [isMigrating, setIsMigrating] = useState(false);
  const [migrationProgress, setMigrationProgress] = useState<MigrationProgress | null>(null);
  const [migrationResult, setMigrationResult] = useState<MigrationResult | null>(null);

  const checkMigrationStatus = async () => {
    setIsChecking(true);
    try {
      const status = await migrationService.checkMigrationNeeded();
      setMigrationStatus(status);
    } catch (error) {
      console.error('Error checking migration status:', error);
    } finally {
      setIsChecking(false);
    }
  };

  const handleMigration = async (clearLocalStorage: boolean = false) => {
    setIsMigrating(true);
    setMigrationResult(null);
    setMigrationProgress(null);

    // Create new service instance with progress callback
    const serviceWithProgress = new DataMigrationService((progress) => {
      setMigrationProgress(progress);
    });

    try {
      const result = await serviceWithProgress.migrateData({
        includeRecipes: true,
        includeMealPlans: true,
        includeShoppingLists: true,
        clearLocalStorageAfter: clearLocalStorage,
        userId: 'default'
      });
      
      setMigrationResult(result);
      
      // Refresh status after migration
      if (result.success) {
        await checkMigrationStatus();
      }
    } catch (error) {
      console.error('Migration error:', error);
      setMigrationResult({
        success: false,
        message: `Migration failed: ${error.message}`,
        details: {
          recipesCount: 0,
          mealPlansCount: 0,
          shoppingListsCount: 0,
          errors: [error.message]
        }
      });
    } finally {
      setIsMigrating(false);
    }
  };

  const createBackup = async () => {
    try {
      const backup = await migrationService.createBackup();
      if (backup.success) {
        alert('Backup created successfully! Data saved to localStorage with timestamp.');
      } else {
        alert(`Backup failed: ${backup.error}`);
      }
    } catch (error) {
      alert(`Backup error: ${error.message}`);
    }
  };

  useEffect(() => {
    checkMigrationStatus();
  }, []);

  const getProgressColor = (stage: string) => {
    switch (stage) {
      case 'complete': return 'bg-green-500';
      case 'error': return 'bg-red-500';
      default: return 'bg-blue-500';
    }
  };

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Database className="h-5 w-5" />
          Data Migration Manager
        </CardTitle>
        <p className="text-sm text-gray-600">
          Migrate your data from localStorage to Supabase for better sync and backup
        </p>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* Status Check */}
        <div>
          <div className="flex items-center justify-between mb-3">
            <h3 className="text-lg font-semibold">Migration Status</h3>
            <Button 
              onClick={checkMigrationStatus}
              disabled={isChecking}
              variant="outline"
              size="sm"
            >
              {isChecking ? (
                <RefreshCw className="h-4 w-4 animate-spin mr-2" />
              ) : (
                <RefreshCw className="h-4 w-4 mr-2" />
              )}
              Check Status
            </Button>
          </div>

          {migrationStatus && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Local Storage Data */}
              <div className="p-4 border rounded-lg">
                <div className="flex items-center gap-2 mb-3">
                  <HardDrive className="h-5 w-5 text-gray-600" />
                  <span className="font-medium">Local Storage</span>
                  <Badge variant={migrationStatus.localDataCount > 0 ? "default" : "secondary"}>
                    {migrationStatus.localDataCount} items
                  </Badge>
                </div>
                <div className="space-y-1 text-sm text-gray-600">
                  <div>Recipes: {migrationStatus.details.localRecipes}</div>
                  <div>Meal Plans: {migrationStatus.details.localMealPlans}</div>
                  <div>Shopping Lists: {migrationStatus.details.localShoppingLists}</div>
                </div>
              </div>

              {/* Supabase Data */}
              <div className="p-4 border rounded-lg">
                <div className="flex items-center gap-2 mb-3">
                  <Cloud className="h-5 w-5 text-blue-600" />
                  <span className="font-medium">Supabase</span>
                  <Badge variant={migrationStatus.supabaseDataCount > 0 ? "default" : "secondary"}>
                    {migrationStatus.supabaseDataCount} items
                  </Badge>
                </div>
                <div className="space-y-1 text-sm text-gray-600">
                  <div>Recipes: {migrationStatus.details.supabaseRecipes}</div>
                  <div>Meal Plans: {migrationStatus.details.supabaseMealPlans}</div>
                  <div>Shopping Lists: {migrationStatus.details.supabaseShoppingLists}</div>
                </div>
              </div>
            </div>
          )}

          {migrationStatus?.needed && (
            <Alert className="mt-4">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                You have data in localStorage but not in Supabase. Migration is recommended for better data sync and backup.
              </AlertDescription>
            </Alert>
          )}

          {migrationStatus && !migrationStatus.needed && migrationStatus.supabaseDataCount > 0 && (
            <Alert className="mt-4">
              <CheckCircle className="h-4 w-4" />
              <AlertDescription>
                Your data is already in Supabase! No migration needed.
              </AlertDescription>
            </Alert>
          )}
        </div>

        <Separator />

        {/* Migration Progress */}
        {migrationProgress && (
          <div>
            <h3 className="text-lg font-semibold mb-3">Migration Progress</h3>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">
                  {migrationProgress.stage.charAt(0).toUpperCase() + migrationProgress.stage.slice(1)}
                </span>
                <span className="text-sm text-gray-600">
                  {migrationProgress.progress}%
                </span>
              </div>
              
              <Progress 
                value={migrationProgress.progress} 
                className={`h-2 ${getProgressColor(migrationProgress.stage)}`}
              />
              
              <div className="text-sm text-gray-600">
                {migrationProgress.message}
                {migrationProgress.currentItem && (
                  <div className="text-xs text-gray-500 mt-1">
                    Current: {migrationProgress.currentItem}
                  </div>
                )}
              </div>
            </div>
          </div>
        )}

        {/* Migration Result */}
        {migrationResult && (
          <div>
            <h3 className="text-lg font-semibold mb-3">Migration Result</h3>
            <Alert className={migrationResult.success ? "border-green-200 bg-green-50" : "border-red-200 bg-red-50"}>
              {migrationResult.success ? (
                <CheckCircle className="h-4 w-4 text-green-600" />
              ) : (
                <AlertTriangle className="h-4 w-4 text-red-600" />
              )}
              <AlertDescription>
                <div className="font-medium mb-2">{migrationResult.message}</div>
                {migrationResult.success && (
                  <div className="text-sm space-y-1">
                    <div>✅ Recipes migrated: {migrationResult.details.recipesCount}</div>
                    <div>✅ Meal plans migrated: {migrationResult.details.mealPlansCount}</div>
                    <div>✅ Shopping lists migrated: {migrationResult.details.shoppingListsCount}</div>
                  </div>
                )}
                {migrationResult.details.errors.length > 0 && (
                  <div className="text-sm mt-2">
                    <div className="font-medium text-red-600">Errors:</div>
                    <ul className="list-disc list-inside space-y-1">
                      {migrationResult.details.errors.map((error, index) => (
                        <li key={index} className="text-red-600">{error}</li>
                      ))}
                    </ul>
                  </div>
                )}
              </AlertDescription>
            </Alert>
          </div>
        )}

        <Separator />

        {/* Action Buttons */}
        <div>
          <h3 className="text-lg font-semibold mb-3">Actions</h3>
          <div className="flex flex-wrap gap-3">
            {/* Create Backup */}
            <Button 
              onClick={createBackup}
              variant="outline"
              className="flex items-center gap-2"
            >
              <Download className="h-4 w-4" />
              Create Backup
            </Button>

            {/* Migrate Data */}
            {migrationStatus?.needed && (
              <>
                <Button 
                  onClick={() => handleMigration(false)}
                  disabled={isMigrating}
                  className="flex items-center gap-2"
                >
                  {isMigrating ? (
                    <RefreshCw className="h-4 w-4 animate-spin" />
                  ) : (
                    <>
                      <Upload className="h-4 w-4" />
                      <ArrowRight className="h-4 w-4" />
                      <Cloud className="h-4 w-4" />
                    </>
                  )}
                  Migrate to Supabase
                </Button>

                <Button 
                  onClick={() => handleMigration(true)}
                  disabled={isMigrating}
                  variant="destructive"
                  className="flex items-center gap-2"
                >
                  {isMigrating ? (
                    <RefreshCw className="h-4 w-4 animate-spin" />
                  ) : (
                    <>
                      <Upload className="h-4 w-4" />
                      <Trash2 className="h-4 w-4" />
                    </>
                  )}
                  Migrate & Clear Local
                </Button>
              </>
            )}
          </div>
        </div>

        {/* Instructions */}
        <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <h4 className="font-medium text-blue-800 mb-2">Migration Instructions</h4>
          <div className="text-sm text-blue-700 space-y-1">
            <p>• <strong>Create Backup:</strong> Save current localStorage data as backup</p>
            <p>• <strong>Migrate to Supabase:</strong> Copy data to Supabase, keep localStorage intact</p>
            <p>• <strong>Migrate & Clear Local:</strong> Copy data to Supabase and remove from localStorage</p>
            <p>• Migration will automatically setup Supabase database if needed</p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default DataMigrationPanel;

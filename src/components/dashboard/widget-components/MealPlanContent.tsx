import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Plus, ShoppingCart } from 'lucide-react';
import { MealPlanContentProps, MEAL_TYPE_CONFIGS } from '@/types/today-meal-widget';
import MealCard from './MealCard';
import { useShoppingCart } from '@/contexts/ShoppingCartContext';
import { toast } from 'sonner';

const MealPlanContent: React.FC<MealPlanContentProps> = ({
  todayPlan,
  onViewRecipe,
  onAddMeal,
  onRemoveMeal
}) => {
  const { addMealToCart } = useShoppingCart();
  const [addingMealToCart, setAddingMealToCart] = useState<string | null>(null);

  // Thứ tự hiển thị các bữa ăn
  const mealOrder = ['breakfast', 'lunch', 'dinner', 'snack'] as const;

  // Defensive programming: đảm bảo todayPlan và meals luôn tồn tại
  if (!todayPlan || !todayPlan.meals) {
    return (
      <div className="text-center py-8">
        <p className="text-neutral-500"><PERSON><PERSON> tải kế hoạch bữa ăn...</p>
      </div>
    );
  }

  const handleAddMeal = (mealType: string) => {
    onAddMeal(mealType);
  };

  const handleAddMealToCart = async (mealType: string, e: React.MouseEvent) => {
    e.stopPropagation();
    setAddingMealToCart(mealType);

    try {
      const mealSlot = todayPlan.meals[mealType];
      if (!mealSlot || !mealSlot.meals || mealSlot.meals.length === 0) {
        toast.error('Bữa ăn này chưa có món nào để thêm vào giỏ');
        return;
      }

      // Convert meals to format expected by addMealToCart
      for (const meal of mealSlot.meals) {
        const mealForCart = {
          id: meal.id,
          mealType: mealType,
          recipe: meal.recipe || {
            id: meal.id,
            title: meal.name,
            ingredients: meal.ingredients || [],
            servings: meal.servings || 1,
            cookingTime: meal.cookingTime || '30 phút',
            difficulty: 'Trung bình'
          }
        };

        await addMealToCart(mealForCart, 1);
      }

      const config = MEAL_TYPE_CONFIGS[mealType];
      toast.success(`Đã thêm "${config.label}" vào giỏ hàng`);
    } catch (error) {
      console.error('Error adding meal to cart:', error);
      toast.error('Không thể thêm bữa ăn vào giỏ hàng');
    } finally {
      setAddingMealToCart(null);
    }
  };

  const handleRemoveMeal = (mealType: string, mealId: string) => {
    console.log('🗑️ MealPlanContent.handleRemoveMeal called:', { mealType, mealId });

    // Debug: Kiểm tra dữ liệu trước khi gọi onRemoveMeal
    console.log('🔍 DEBUG - MealPlanContent.handleRemoveMeal:');
    console.log('- mealType:', mealType);
    console.log('- mealId:', mealId);
    console.log('- typeof mealId:', typeof mealId);
    console.log('- todayPlan structure:', {
      id: todayPlan.id,
      date: todayPlan.date,
      mealsKeys: Object.keys(todayPlan.meals),
      mealsForThisType: todayPlan.meals[mealType]?.meals?.length || 0
    });

    // Kiểm tra meals trong mealType này
    const mealsInSlot = todayPlan.meals[mealType]?.meals || [];
    console.log(`- Meals in ${mealType} slot:`, mealsInSlot.map(m => ({ id: m.id, name: m.name })));

    try {
      console.log('🚀 Calling onRemoveMeal from MealPlanContent');
      onRemoveMeal(mealType, mealId);
      console.log('✅ MealPlanContent.handleRemoveMeal completed successfully');
    } catch (error) {
      console.error('❌ MealPlanContent.handleRemoveMeal error:', error);
      console.error('Error stack:', error.stack);
    }
  };

  return (
    <div className="space-y-6">
      {mealOrder.map((mealType) => {
        const mealSlot = todayPlan.meals[mealType];
        const config = MEAL_TYPE_CONFIGS[mealType];

        // Defensive programming: đảm bảo mealSlot và meals tồn tại
        if (!mealSlot) {
          return null;
        }

        // Đảm bảo meals là array
        const meals = Array.isArray(mealSlot.meals) ? mealSlot.meals : [];
        
        return (
          <div key={mealType} className="space-y-3">
            {/* Header của bữa ăn */}
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <span className="text-2xl">{config.emoji}</span>
                <h3 className="text-lg font-semibold text-neutral-900">
                  {config.label}
                </h3>
                {meals.length > 0 && (
                  <span className="text-sm text-neutral-500">
                    ({meals.length} món)
                  </span>
                )}
              </div>

              {/* Action buttons */}
              <div className="flex items-center gap-2">
                {/* Nút thêm bữa vào giỏ - chỉ hiện khi có món */}
                {meals.length > 0 && (
                  <Button
                    variant="outline"
                    size="sm"
                    className="h-8 px-3 text-xs border-green-300 text-green-600 hover:bg-green-50 hover:border-green-400"
                    onClick={(e) => handleAddMealToCart(mealType, e)}
                    disabled={addingMealToCart === mealType}
                  >
                    {addingMealToCart === mealType ? (
                      <>
                        <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-green-600 mr-1"></div>
                        Đang thêm...
                      </>
                    ) : (
                      <>
                        <ShoppingCart className="h-3 w-3 mr-1" />
                        Thêm bữa vào giỏ
                      </>
                    )}
                  </Button>
                )}

                {/* Nút thêm món */}
                <Button
                  variant="outline"
                  size="sm"
                  className="h-8 px-3 text-xs border-dashed border-primary-300 text-primary-600 hover:bg-primary-50 hover:border-primary-400"
                  onClick={() => handleAddMeal(mealType)}
                >
                  <Plus className="h-3 w-3 mr-1" />
                  Thêm món
                </Button>
              </div>
            </div>

            {/* Danh sách món ăn */}
            {meals.length > 0 ? (
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                {meals.map((meal) => (
                  <MealCard
                    key={meal.id}
                    meal={meal}
                    onViewRecipe={onViewRecipe}
                    onRemoveMeal={(mealId) => handleRemoveMeal(mealType, mealId)}
                  />
                ))}
              </div>
            ) : (
              // Trạng thái trống
              <div className="border-2 border-dashed border-neutral-200 rounded-lg p-6 text-center">
                <div className="text-4xl mb-2">{config.emoji}</div>
                <p className="text-sm text-neutral-500 mb-3">
                  Chưa có món nào cho {config.label.toLowerCase()}
                </p>
                <Button
                  variant="outline"
                  size="sm"
                  className="border-dashed border-primary-300 text-primary-600 hover:bg-primary-50"
                  onClick={() => handleAddMeal(mealType)}
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Thêm món đầu tiên
                </Button>
              </div>
            )}

            {/* Ghi chú bữa ăn */}
            {mealSlot.notes && (
              <div className="bg-neutral-50 rounded-lg p-3 border border-neutral-200">
                <p className="text-sm text-neutral-600">
                  <span className="font-medium">Ghi chú:</span> {mealSlot.notes}
                </p>
              </div>
            )}
          </div>
        );
      })}
    </div>
  );
};

export default MealPlanContent;

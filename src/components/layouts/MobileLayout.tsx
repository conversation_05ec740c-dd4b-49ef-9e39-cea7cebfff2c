import React, { useState, useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import { ArrowLeft, Menu, Search, MoreVertical } from 'lucide-react';
import { cn } from '@/lib/utils';
import { MobileBottomNav, MobileHeader, MobileSideMenu } from '@/components/ui/mobile-navigation';
import { TouchButton } from '@/components/ui/mobile-touch';
import { useAuth } from '@/contexts/AuthContext';

interface MobileLayoutProps {
  children: React.ReactNode;
  title?: string;
  showBackButton?: boolean;
  showBottomNav?: boolean;
  showHeader?: boolean;
  headerVariant?: 'default' | 'transparent' | 'elevated';
  className?: string;
  onBack?: () => void;
  rightHeaderAction?: React.ReactNode;
}

export const MobileLayout: React.FC<MobileLayoutProps> = ({
  children,
  title,
  showBackButton = false,
  showBottomNav = true,
  showHeader = true,
  headerVariant = 'default',
  className,
  onBack,
  rightHeaderAction,
}) => {
  const [isSideMenuOpen, setIsSideMenuOpen] = useState(false);
  const location = useLocation();
  const { isAuthenticated, user } = useAuth();

  // Auto-generate title from route if not provided
  const getPageTitle = () => {
    if (title) return title;
    
    const routeTitles: Record<string, string> = {
      '/': 'Angiday',
      '/recipes': 'Công thức',
      '/meal-planner': 'Kế hoạch bữa ăn',
      '/shopping-cart': 'Giỏ hàng',
      '/profile': 'Cá nhân',
      '/dashboard': 'Bảng điều khiển',
      '/cooking-mode': 'Chế độ nấu ăn',
      '/meal-plans': 'Thực đơn',
      '/vietnamese-food': 'Món Việt',
    };
    
    return routeTitles[location.pathname] || 'Angiday';
  };

  // Handle back navigation
  const handleBack = () => {
    if (onBack) {
      onBack();
    } else {
      window.history.back();
    }
  };

  // Close side menu when route changes
  useEffect(() => {
    setIsSideMenuOpen(false);
  }, [location.pathname]);

  // Prevent scroll when side menu is open
  useEffect(() => {
    if (isSideMenuOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isSideMenuOpen]);

  const leftHeaderAction = showBackButton ? (
    <TouchButton
      variant="ghost"
      size="sm"
      onClick={handleBack}
      className="p-2"
    >
      <ArrowLeft className="w-5 h-5" />
    </TouchButton>
  ) : (
    <TouchButton
      variant="ghost"
      size="sm"
      onClick={() => setIsSideMenuOpen(true)}
      className="p-2"
    >
      <Menu className="w-5 h-5" />
    </TouchButton>
  );

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col">
      {/* Mobile Header */}
      {showHeader && (
        <MobileHeader
          title={getPageTitle()}
          leftAction={leftHeaderAction}
          rightAction={rightHeaderAction}
          variant={headerVariant}
        />
      )}

      {/* Main Content */}
      <main className={cn(
        'flex-1 overflow-auto',
        showBottomNav && 'pb-16', // Add padding for bottom nav
        className
      )}>
        {children}
      </main>

      {/* Bottom Navigation */}
      {showBottomNav && <MobileBottomNav />}

      {/* Side Menu */}
      <MobileSideMenu
        isOpen={isSideMenuOpen}
        onClose={() => setIsSideMenuOpen(false)}
      >
        <div className="p-4 space-y-6">
          {/* User Profile Section */}
          {isAuthenticated && user ? (
            <div className="flex items-center space-x-3 p-3 bg-orange-50 rounded-lg">
              <div className="w-12 h-12 bg-orange-200 rounded-full flex items-center justify-center">
                <span className="text-orange-700 font-semibold text-lg">
                  {user.email?.[0]?.toUpperCase() || 'U'}
                </span>
              </div>
              <div className="flex-1 min-w-0">
                <p className="font-medium text-gray-900 truncate">
                  {user.email || 'Người dùng'}
                </p>
                <p className="text-sm text-gray-600">Thành viên</p>
              </div>
            </div>
          ) : (
            <div className="p-3 bg-gray-50 rounded-lg text-center">
              <p className="text-gray-600 mb-3">Chưa đăng nhập</p>
              <TouchButton
                variant="primary"
                size="sm"
                onClick={() => {
                  setIsSideMenuOpen(false);
                  // Navigate to login
                }}
                className="w-full"
              >
                Đăng nhập
              </TouchButton>
            </div>
          )}

          {/* Navigation Links */}
          <nav className="space-y-2">
            <SideMenuLink href="/" icon="🏠" label="Trang chủ" />
            <SideMenuLink href="/recipes" icon="📖" label="Công thức" />
            <SideMenuLink href="/meal-planner" icon="📅" label="Kế hoạch bữa ăn" />
            <SideMenuLink href="/vietnamese-food" icon="🇻🇳" label="Món Việt" />
            <SideMenuLink href="/cooking-mode" icon="👨‍🍳" label="Chế độ nấu ăn" />
            <SideMenuLink href="/shopping-cart" icon="🛒" label="Giỏ hàng" />
          </nav>

          {/* Quick Actions */}
          <div className="border-t border-gray-200 pt-4">
            <h3 className="text-sm font-medium text-gray-900 mb-3">Thao tác nhanh</h3>
            <div className="space-y-2">
              <SideMenuLink href="/recipes/create" icon="➕" label="Tạo công thức" />
              <SideMenuLink href="/meal-planner/quick" icon="⚡" label="Lập kế hoạch nhanh" />
              <SideMenuLink href="/favorites" icon="❤️" label="Yêu thích" />
            </div>
          </div>

          {/* Settings */}
          <div className="border-t border-gray-200 pt-4">
            <SideMenuLink href="/settings" icon="⚙️" label="Cài đặt" />
            <SideMenuLink href="/help" icon="❓" label="Trợ giúp" />
          </div>
        </div>
      </MobileSideMenu>
    </div>
  );
};

// Side menu link component
interface SideMenuLinkProps {
  href: string;
  icon: string;
  label: string;
  badge?: number;
}

const SideMenuLink: React.FC<SideMenuLinkProps> = ({ href, icon, label, badge }) => {
  const location = useLocation();
  const isActive = location.pathname === href;

  return (
    <a
      href={href}
      className={cn(
        'flex items-center space-x-3 p-3 rounded-lg transition-colors',
        'touch-manipulation',
        isActive 
          ? 'bg-orange-100 text-orange-700' 
          : 'text-gray-700 hover:bg-gray-100'
      )}
    >
      <span className="text-lg">{icon}</span>
      <span className="flex-1 font-medium">{label}</span>
      {badge && badge > 0 && (
        <span className="bg-red-500 text-white text-xs rounded-full min-w-[20px] h-5 flex items-center justify-center">
          {badge > 99 ? '99+' : badge}
        </span>
      )}
    </a>
  );
};

export default MobileLayout;

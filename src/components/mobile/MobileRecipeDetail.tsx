import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { 
  Clock, 
  Users, 
  Star, 
  Heart, 
  Share2, 
  ShoppingCart, 
  ChefHat,
  ArrowLeft,
  Play,
  Bookmark,
  Eye
} from 'lucide-react';
import { MobileLayout } from '@/components/layouts/MobileLayout';
import { TouchButton } from '@/components/ui/mobile-touch';
import { MobileTabs } from '@/components/ui/mobile-navigation';
import { OptimizedImage } from '@/components/ui/optimized-image';
import { FloatingActionButton } from '@/components/ui/mobile-navigation';
import { cn } from '@/lib/utils';
import { recipeManagementService, Recipe } from '@/services/recipeManagementService';
import { toast } from 'sonner';

interface MobileRecipeDetailProps {
  recipeId?: string;
}

export const MobileRecipeDetail: React.FC<MobileRecipeDetailProps> = ({ recipeId }) => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [recipe, setRecipe] = useState<Recipe | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isFavorite, setIsFavorite] = useState(false);
  const [activeTab, setActiveTab] = useState('ingredients');
  const [servings, setServings] = useState(4);

  const currentId = recipeId || id;

  useEffect(() => {
    if (currentId) {
      loadRecipe();
    }
  }, [currentId]);

  const loadRecipe = async () => {
    if (!currentId) return;

    setIsLoading(true);
    try {
      const recipeData = await recipeManagementService.getRecipe(currentId);
      if (recipeData) {
        setRecipe(recipeData);
        setServings(recipeData.servings || 4);
        await recipeManagementService.incrementViews(currentId);
      }
    } catch (error) {
      console.error('Error loading recipe:', error);
      toast.error('Có lỗi xảy ra khi tải công thức');
    } finally {
      setIsLoading(false);
    }
  };

  const handleShare = async () => {
    if (!recipe) return;
    
    if (navigator.share) {
      try {
        await navigator.share({
          title: recipe.name,
          text: recipe.description,
          url: window.location.href,
        });
      } catch (error) {
        console.log('Share cancelled');
      }
    } else {
      // Fallback: copy to clipboard
      navigator.clipboard.writeText(window.location.href);
      toast.success('Đã sao chép link công thức');
    }
  };

  const handleFavoriteToggle = () => {
    setIsFavorite(!isFavorite);
    toast.success(isFavorite ? 'Đã bỏ yêu thích' : 'Đã thêm vào yêu thích');
  };

  const handleAddToCart = () => {
    if (!recipe) return;
    
    // Add ingredients to shopping cart
    const cartItems = recipe.ingredients?.map(ingredient => ({
      id: `${recipe.id}-${ingredient.name}`,
      name: ingredient.name,
      quantity: ingredient.quantity,
      unit: ingredient.unit || '',
      category: 'Nguyên liệu',
    })) || [];

    const existingCart = JSON.parse(localStorage.getItem('simple_shopping_cart') || '[]');
    const updatedCart = [...existingCart, ...cartItems];
    localStorage.setItem('simple_shopping_cart', JSON.stringify(updatedCart));
    
    toast.success('Đã thêm nguyên liệu vào giỏ hàng');
  };

  const handleStartCooking = () => {
    navigate(`/cooking-mode?recipe=${currentId}`);
  };

  const adjustServings = (newServings: number) => {
    if (newServings < 1) return;
    setServings(newServings);
  };

  const getAdjustedQuantity = (originalQuantity: string, originalServings: number) => {
    const ratio = servings / originalServings;
    const numericQuantity = parseFloat(originalQuantity);
    
    if (isNaN(numericQuantity)) return originalQuantity;
    
    const adjusted = numericQuantity * ratio;
    return adjusted % 1 === 0 ? adjusted.toString() : adjusted.toFixed(1);
  };

  if (isLoading) {
    return (
      <MobileLayout title="Đang tải..." showBackButton>
        <div className="flex items-center justify-center min-h-[50vh]">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-500"></div>
        </div>
      </MobileLayout>
    );
  }

  if (!recipe) {
    return (
      <MobileLayout title="Không tìm thấy" showBackButton>
        <div className="flex flex-col items-center justify-center min-h-[50vh] px-4">
          <div className="text-6xl mb-4">😕</div>
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Không tìm thấy công thức</h2>
          <p className="text-gray-600 text-center mb-6">Công thức này có thể đã bị xóa hoặc không tồn tại.</p>
          <TouchButton variant="primary" onClick={() => navigate('/recipes')}>
            Xem công thức khác
          </TouchButton>
        </div>
      </MobileLayout>
    );
  }

  const tabs = [
    { id: 'ingredients', label: 'Nguyên liệu', icon: <ShoppingCart className="w-4 h-4" /> },
    { id: 'instructions', label: 'Cách làm', icon: <ChefHat className="w-4 h-4" /> },
    { id: 'info', label: 'Thông tin', icon: <Eye className="w-4 h-4" /> },
  ];

  return (
    <MobileLayout 
      showBackButton 
      showBottomNav={false}
      rightHeaderAction={
        <div className="flex gap-2">
          <TouchButton variant="ghost" size="sm" onClick={handleShare}>
            <Share2 className="w-5 h-5" />
          </TouchButton>
          <TouchButton 
            variant="ghost" 
            size="sm" 
            onClick={handleFavoriteToggle}
            className={isFavorite ? 'text-red-500' : ''}
          >
            <Heart className={cn('w-5 h-5', isFavorite && 'fill-current')} />
          </TouchButton>
        </div>
      }
    >
      {/* Hero Image */}
      <div className="relative aspect-video">
        <OptimizedImage
          src={recipe.image || '/placeholder.svg'}
          alt={recipe.name}
          aspectRatio="video"
          className="w-full h-full object-cover"
          priority
        />
        
        {/* Gradient overlay */}
        <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent" />
        
        {/* Recipe info overlay */}
        <div className="absolute bottom-4 left-4 right-4 text-white">
          <h1 className="text-2xl font-bold mb-2 line-clamp-2">{recipe.name}</h1>
          <div className="flex items-center gap-4 text-sm">
            {recipe.cookTime && (
              <div className="flex items-center gap-1">
                <Clock className="w-4 h-4" />
                <span>{recipe.cookTime} phút</span>
              </div>
            )}
            <div className="flex items-center gap-1">
              <Users className="w-4 h-4" />
              <span>{servings} người</span>
            </div>
            {recipe.rating && (
              <div className="flex items-center gap-1">
                <Star className="w-4 h-4 fill-current text-yellow-400" />
                <span>{recipe.rating.toFixed(1)}</span>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="p-4 space-y-6">
        {/* Description */}
        {recipe.description && (
          <p className="text-gray-700 leading-relaxed">{recipe.description}</p>
        )}

        {/* Servings Adjuster */}
        <div className="flex items-center justify-between bg-gray-50 rounded-lg p-4">
          <span className="font-medium text-gray-900">Số người ăn:</span>
          <div className="flex items-center gap-3">
            <TouchButton
              variant="ghost"
              size="sm"
              onClick={() => adjustServings(servings - 1)}
              className="w-8 h-8 rounded-full"
            >
              -
            </TouchButton>
            <span className="font-semibold text-lg min-w-[2rem] text-center">{servings}</span>
            <TouchButton
              variant="ghost"
              size="sm"
              onClick={() => adjustServings(servings + 1)}
              className="w-8 h-8 rounded-full"
            >
              +
            </TouchButton>
          </div>
        </div>

        {/* Tabs */}
        <MobileTabs
          tabs={tabs}
          activeTab={activeTab}
          onTabChange={setActiveTab}
        />

        {/* Tab Content */}
        <div className="min-h-[300px]">
          {activeTab === 'ingredients' && (
            <div className="space-y-3">
              {recipe.ingredients?.map((ingredient, index) => (
                <div key={index} className="flex items-center justify-between py-3 border-b border-gray-100 last:border-0">
                  <span className="font-medium text-gray-900">{ingredient.name}</span>
                  <span className="text-gray-600">
                    {getAdjustedQuantity(ingredient.quantity, recipe.servings || 4)} {ingredient.unit}
                  </span>
                </div>
              )) || <p className="text-gray-500">Chưa có thông tin nguyên liệu</p>}
            </div>
          )}

          {activeTab === 'instructions' && (
            <div className="space-y-4">
              {recipe.instructions?.map((instruction, index) => (
                <div key={index} className="flex gap-4">
                  <div className="w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                    <span className="text-orange-600 font-semibold text-sm">{index + 1}</span>
                  </div>
                  <p className="text-gray-700 leading-relaxed flex-1">{instruction}</p>
                </div>
              )) || <p className="text-gray-500">Chưa có hướng dẫn nấu ăn</p>}
            </div>
          )}

          {activeTab === 'info' && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="bg-gray-50 rounded-lg p-4 text-center">
                  <div className="text-2xl font-bold text-orange-600">{recipe.cookTime || 0}</div>
                  <div className="text-sm text-gray-600">Phút nấu</div>
                </div>
                <div className="bg-gray-50 rounded-lg p-4 text-center">
                  <div className="text-2xl font-bold text-orange-600">{recipe.difficulty || 'Dễ'}</div>
                  <div className="text-sm text-gray-600">Độ khó</div>
                </div>
              </div>
              
              {recipe.tags && recipe.tags.length > 0 && (
                <div>
                  <h3 className="font-semibold text-gray-900 mb-3">Thẻ tag</h3>
                  <div className="flex flex-wrap gap-2">
                    {recipe.tags.map((tag, index) => (
                      <span key={index} className="px-3 py-1 bg-orange-100 text-orange-700 rounded-full text-sm">
                        {tag}
                      </span>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}
        </div>

        {/* Action Buttons */}
        <div className="flex gap-3 pt-4">
          <TouchButton
            variant="secondary"
            onClick={handleAddToCart}
            className="flex-1"
          >
            <ShoppingCart className="w-4 h-4 mr-2" />
            Thêm vào giỏ
          </TouchButton>
          <TouchButton
            variant="primary"
            onClick={handleStartCooking}
            className="flex-1"
          >
            <Play className="w-4 h-4 mr-2" />
            Bắt đầu nấu
          </TouchButton>
        </div>
      </div>

      {/* Floating Action Button for quick cooking */}
      <FloatingActionButton
        icon={<ChefHat className="w-6 h-6" />}
        onClick={handleStartCooking}
      />
    </MobileLayout>
  );
};

export default MobileRecipeDetail;

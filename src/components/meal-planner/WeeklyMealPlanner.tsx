import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Calendar,
  Plus,
  Clock,
  DollarSign,
  ChefHat,
  Coffee,
  Utensils,
  Soup,
  Cookie,
  MoreVertical,
  Trash2,
  Edit,
  Copy,
  ShoppingCart
} from 'lucide-react';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  WeeklyMealPlan, 
  DayMealPlan, 
  MealSlot,
  createEmptyWeekPlan,
  calculateWeekPlanSummary,
  applyTemplateToWeekPlan,
  getCurrentWeekPlan
} from '@/data/weeklyMealPlan';
import { vietnameseFoodDataService } from '@/services/VietnameseFoodDataService';
import { MealTemplate } from '@/data/vietnameseMealTemplates';
import { VietnameseDish } from '@/data/vietnameseFoodCategories';
import { MealPlanShoppingList } from './MealPlanShoppingList';
import { toast } from 'sonner';

interface WeeklyMealPlannerProps {
  initialTemplate?: MealTemplate;
  className?: string;
}

export const WeeklyMealPlanner: React.FC<WeeklyMealPlannerProps> = ({ 
  initialTemplate,
  className 
}) => {
  const [weekPlan, setWeekPlan] = useState<WeeklyMealPlan>(getCurrentWeekPlan());
  const [availableDishes, setAvailableDishes] = useState<VietnameseDish[]>([]);
  const [loading, setLoading] = useState(true);
  const [draggedDish, setDraggedDish] = useState<VietnameseDish | null>(null);

  // Load available dishes
  useEffect(() => {
    const loadDishes = async () => {
      try {
        const dishes = await vietnameseFoodDataService.getAllDishes();
        setAvailableDishes(dishes);
      } catch (error) {
        console.error('Error loading dishes:', error);
        toast.error('Lỗi khi tải danh sách món ăn');
      } finally {
        setLoading(false);
      }
    };

    loadDishes();
  }, []);

  // Apply initial template if provided
  useEffect(() => {
    if (initialTemplate && availableDishes.length > 0) {
      const updatedPlan = applyTemplateToWeekPlan(weekPlan, initialTemplate, availableDishes);
      setWeekPlan(updatedPlan);
      toast.success(`Đã áp dụng thực đơn "${initialTemplate.name}"`);
    }
  }, [initialTemplate, availableDishes]);

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND'
    }).format(price);
  };

  const getMealIcon = (mealType: string) => {
    const icons = {
      breakfast: <Coffee className="h-4 w-4" />,
      lunch: <Utensils className="h-4 w-4" />,
      dinner: <Soup className="h-4 w-4" />,
      snack: <Cookie className="h-4 w-4" />
    };
    return icons[mealType as keyof typeof icons] || <Utensils className="h-4 w-4" />;
  };

  const getMealLabel = (mealType: string) => {
    const labels = {
      breakfast: 'Sáng',
      lunch: 'Trưa', 
      dinner: 'Tối',
      snack: 'Ăn vặt'
    };
    return labels[mealType as keyof typeof labels] || mealType;
  };

  const handleDragStart = (dish: VietnameseDish) => {
    setDraggedDish(dish);
  };

  const handleDragEnd = () => {
    setDraggedDish(null);
  };

  const handleDrop = (dayIndex: number, mealType: string) => {
    if (!draggedDish) return;

    const updatedPlan = { ...weekPlan };
    const day = updatedPlan.days[dayIndex];
    
    // Create new meal slot
    const newMealSlot: MealSlot = {
      id: `${mealType}-${dayIndex}-${Date.now()}`,
      dishId: draggedDish.id,
      dishName: draggedDish.name,
      estimatedCost: draggedDish.cost,
      cookingTime: draggedDish.cookingTime,
      servings: draggedDish.servings,
      isFromTemplate: false
    };

    // Assign to the correct meal slot
    (day as any)[mealType] = newMealSlot;
    
    updatedPlan.updatedAt = new Date().toISOString();
    setWeekPlan(updatedPlan);
    
    toast.success(`Đã thêm "${draggedDish.name}" vào ${getMealLabel(mealType)} ${day.dayOfWeek}`);
    setDraggedDish(null);
  };

  const handleRemoveMeal = (dayIndex: number, mealType: string) => {
    const updatedPlan = { ...weekPlan };
    const day = updatedPlan.days[dayIndex];
    
    // Reset meal slot
    (day as any)[mealType] = { id: `${mealType}-${dayIndex}` };
    
    updatedPlan.updatedAt = new Date().toISOString();
    setWeekPlan(updatedPlan);
    
    toast.success('Đã xóa món ăn');
  };

  const calculateSummary = () => {
    return calculateWeekPlanSummary(weekPlan, availableDishes);
  };

  const summary = calculateSummary();

  if (loading) {
    return (
      <Card className={className}>
        <CardContent className="p-8 text-center">
          <ChefHat className="h-8 w-8 animate-pulse text-blue-500 mx-auto mb-4" />
          <p className="text-gray-600">Đang tải kế hoạch bữa ăn...</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className={className}>
      {/* Main Tabs */}
      <Tabs defaultValue="planner" className="space-y-6">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="planner" className="flex items-center gap-2">
            <Calendar className="h-4 w-4" />
            Lập Kế Hoạch
          </TabsTrigger>
          <TabsTrigger value="shopping" className="flex items-center gap-2">
            <ShoppingCart className="h-4 w-4" />
            Danh Sách Mua Sắm
          </TabsTrigger>
        </TabsList>

        <TabsContent value="planner">
          {/* Header */}
          <Card className="mb-6">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Calendar className="h-6 w-6 text-blue-500" />
                {weekPlan.name}
              </CardTitle>
              <p className="text-gray-600 mt-1">
                {new Date(weekPlan.startDate).toLocaleDateString('vi-VN')} - {new Date(weekPlan.endDate).toLocaleDateString('vi-VN')}
              </p>
            </div>
            <div className="flex items-center gap-2">
              {weekPlan.appliedTemplateId && (
                <Badge variant="outline" className="bg-green-50 text-green-700">
                  Từ thực đơn mẫu
                </Badge>
              )}
              <Button variant="outline" size="sm">
                <Copy className="h-4 w-4 mr-2" />
                Sao chép tuần
              </Button>
            </div>
          </div>
        </CardHeader>
        
        <CardContent>
          {/* Summary Stats */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center p-3 bg-blue-50 rounded-lg">
              <div className="text-lg font-bold text-blue-600">{summary.totalDishes}</div>
              <div className="text-sm text-gray-600">Món ăn</div>
            </div>
            <div className="text-center p-3 bg-green-50 rounded-lg">
              <div className="text-lg font-bold text-green-600">{formatPrice(summary.totalCost)}</div>
              <div className="text-sm text-gray-600">Chi phí</div>
            </div>
            <div className="text-center p-3 bg-orange-50 rounded-lg">
              <div className="text-lg font-bold text-orange-600">{summary.totalCookingTime}p</div>
              <div className="text-sm text-gray-600">Thời gian nấu</div>
            </div>
            <div className="text-center p-3 bg-purple-50 rounded-lg">
              <div className="text-lg font-bold text-purple-600">{summary.missingSlots.length}</div>
              <div className="text-sm text-gray-600">Chưa lên lịch</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Weekly Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-7 gap-4 mb-6">
        {weekPlan.days.map((day, dayIndex) => (
          <Card key={day.date} className="min-h-[400px]">
            <CardHeader className="pb-3">
              <CardTitle className="text-sm font-medium text-center">
                <div className="font-semibold">{day.dayOfWeek}</div>
                <div className="text-xs text-gray-500 mt-1">
                  {new Date(day.date).toLocaleDateString('vi-VN', { day: '2-digit', month: '2-digit' })}
                </div>
              </CardTitle>
            </CardHeader>
            
            <CardContent className="pt-0 space-y-3">
              {['breakfast', 'lunch', 'dinner', 'snack'].map((mealType) => {
                const meal = (day as any)[mealType] as MealSlot;
                
                return (
                  <div
                    key={mealType}
                    className={`p-3 border-2 border-dashed rounded-lg min-h-[80px] transition-colors ${
                      meal?.dishId 
                        ? 'border-green-200 bg-green-50' 
                        : 'border-gray-200 bg-gray-50 hover:border-blue-300 hover:bg-blue-50'
                    }`}
                    onDragOver={(e) => e.preventDefault()}
                    onDrop={(e) => {
                      e.preventDefault();
                      handleDrop(dayIndex, mealType);
                    }}
                  >
                    <div className="flex items-center gap-2 mb-2">
                      {getMealIcon(mealType)}
                      <span className="text-xs font-medium text-gray-700">
                        {getMealLabel(mealType)}
                      </span>
                    </div>
                    
                    {meal?.dishId ? (
                      <div className="space-y-1">
                        <div className="font-medium text-sm text-gray-900">
                          {meal.dishName}
                        </div>
                        <div className="flex items-center justify-between text-xs text-gray-600">
                          <span className="flex items-center gap-1">
                            <Clock className="h-3 w-3" />
                            {meal.cookingTime}
                          </span>
                          <span className="flex items-center gap-1">
                            <DollarSign className="h-3 w-3" />
                            {formatPrice(meal.estimatedCost || 0)}
                          </span>
                        </div>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="w-full h-6 text-xs text-red-600 hover:text-red-700 hover:bg-red-50"
                          onClick={() => handleRemoveMeal(dayIndex, mealType)}
                        >
                          <Trash2 className="h-3 w-3 mr-1" />
                          Xóa
                        </Button>
                      </div>
                    ) : (
                      <div className="text-center text-gray-400 text-xs">
                        Kéo món ăn vào đây
                      </div>
                    )}
                  </div>
                );
              })}
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Available Dishes */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <ChefHat className="h-5 w-5 text-orange-500" />
            Món Ăn Có Sẵn
          </CardTitle>
          <p className="text-sm text-gray-600">
            Kéo thả món ăn vào lịch trình bên trên
          </p>
        </CardHeader>
        
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-3">
            {availableDishes.map((dish) => (
              <div
                key={dish.id}
                draggable
                onDragStart={() => handleDragStart(dish)}
                onDragEnd={handleDragEnd}
                className="p-3 border rounded-lg cursor-move hover:shadow-md transition-shadow bg-white"
              >
                <div className="text-sm font-medium text-gray-900 mb-1">
                  {dish.name}
                </div>
                <div className="text-xs text-gray-600 mb-2">
                  {dish.cookingTime} • {formatPrice(dish.cost)}
                </div>
                <Badge variant="outline" className="text-xs">
                  {dish.mealType === 'breakfast' ? 'Sáng' :
                   dish.mealType === 'lunch' ? 'Trưa' :
                   dish.mealType === 'dinner' ? 'Tối' : 'Ăn vặt'}
                </Badge>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
        </TabsContent>

        <TabsContent value="shopping">
          <MealPlanShoppingList
            weekPlan={weekPlan}
            dishesData={availableDishes}
          />
        </TabsContent>
      </Tabs>
    </div>
  );
};

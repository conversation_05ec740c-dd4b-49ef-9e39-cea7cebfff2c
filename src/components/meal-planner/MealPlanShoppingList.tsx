import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  ShoppingCart, 
  Clock, 
  DollarSign, 
  Package,
  CheckCircle,
  Circle,
  Download,
  Share2,
  Settings,
  Lightbulb,
  TrendingUp,
  Users
} from 'lucide-react';
import { WeeklyMealPlan } from '@/data/weeklyMealPlan';
import { VietnameseDish } from '@/data/vietnameseFoodCategories';
import { 
  mealPlanToShoppingService, 
  MealPlanShoppingOptions,
  GeneratedShoppingList 
} from '@/services/MealPlanToShoppingService';
import { useShoppingCart } from '@/contexts/ShoppingCartContext';
import { toast } from 'sonner';

interface MealPlanShoppingListProps {
  weekPlan: WeeklyMealPlan;
  dishesData: VietnameseDish[];
  className?: string;
}

export const MealPlanShoppingList: React.FC<MealPlanShoppingListProps> = ({
  weekPlan,
  dishesData,
  className
}) => {
  const { addSession } = useShoppingCart();
  const [shoppingList, setShoppingList] = useState<GeneratedShoppingList | null>(null);
  const [loading, setLoading] = useState(false);
  const [options, setOptions] = useState<MealPlanShoppingOptions>({
    consolidateIngredients: true,
    includeOptionalItems: false,
    adjustForServings: true,
    targetServings: 4,
    onlySelectedMeals: false
  });

  // Generate shopping list when options change
  useEffect(() => {
    generateShoppingList();
  }, [weekPlan, options]);

  const generateShoppingList = async () => {
    setLoading(true);
    try {
      const generated = await mealPlanToShoppingService.generateShoppingList(
        weekPlan,
        dishesData,
        options
      );
      setShoppingList(generated);
    } catch (error) {
      console.error('Error generating shopping list:', error);
      toast.error('Lỗi khi tạo danh sách mua sắm');
    } finally {
      setLoading(false);
    }
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND'
    }).format(price);
  };

  const handleAddToCart = () => {
    if (!shoppingList) return;

    try {
      shoppingList.sessions.forEach(session => {
        addSession(session);
      });
      
      toast.success(`Đã thêm ${shoppingList.sessions.length} nhóm mua sắm vào giỏ hàng!`);
    } catch (error) {
      console.error('Error adding to cart:', error);
      toast.error('Lỗi khi thêm vào giỏ hàng');
    }
  };

  if (loading) {
    return (
      <Card className={className}>
        <CardContent className="p-8 text-center">
          <ShoppingCart className="h-8 w-8 animate-pulse text-blue-500 mx-auto mb-4" />
          <p className="text-gray-600">Đang tạo danh sách mua sắm...</p>
        </CardContent>
      </Card>
    );
  }

  if (!shoppingList) {
    return (
      <Card className={className}>
        <CardContent className="p-8 text-center">
          <ShoppingCart className="h-16 w-16 text-gray-300 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            Chưa có danh sách mua sắm
          </h3>
          <p className="text-gray-600 mb-4">
            Thêm món ăn vào kế hoạch tuần để tạo danh sách mua sắm
          </p>
          <Button onClick={generateShoppingList}>
            Tạo danh sách mua sắm
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className={className}>
      {/* Header */}
      <Card className="mb-6">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <ShoppingCart className="h-6 w-6 text-blue-500" />
                Danh Sách Mua Sắm Thông Minh
              </CardTitle>
              <p className="text-gray-600 mt-1">
                Được tạo từ kế hoạch bữa ăn tuần của bạn
              </p>
            </div>
            <div className="flex items-center gap-2">
              <Button variant="outline" size="sm">
                <Download className="h-4 w-4 mr-2" />
                Xuất PDF
              </Button>
              <Button variant="outline" size="sm">
                <Share2 className="h-4 w-4 mr-2" />
                Chia sẻ
              </Button>
              <Button onClick={handleAddToCart}>
                <ShoppingCart className="h-4 w-4 mr-2" />
                Thêm vào giỏ hàng
              </Button>
            </div>
          </div>
        </CardHeader>
        
        <CardContent>
          {/* Summary Stats */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
            <div className="text-center p-3 bg-blue-50 rounded-lg">
              <div className="text-lg font-bold text-blue-600">{shoppingList.totalItems}</div>
              <div className="text-sm text-gray-600">Nguyên liệu</div>
            </div>
            <div className="text-center p-3 bg-green-50 rounded-lg">
              <div className="text-lg font-bold text-green-600">{formatPrice(shoppingList.totalEstimatedCost)}</div>
              <div className="text-sm text-gray-600">Tổng chi phí</div>
            </div>
            <div className="text-center p-3 bg-orange-50 rounded-lg">
              <div className="text-lg font-bold text-orange-600">{shoppingList.summary.estimatedShoppingTime}p</div>
              <div className="text-sm text-gray-600">Thời gian mua</div>
            </div>
            <div className="text-center p-3 bg-purple-50 rounded-lg">
              <div className="text-lg font-bold text-purple-600">{shoppingList.summary.uniqueIngredients}</div>
              <div className="text-sm text-gray-600">Loại nguyên liệu</div>
            </div>
          </div>

          {/* Options */}
          <div className="bg-gray-50 rounded-lg p-4">
            <h4 className="font-medium text-gray-900 mb-3 flex items-center gap-2">
              <Settings className="h-4 w-4" />
              Tùy chọn danh sách
            </h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-700">Gộp nguyên liệu giống nhau</span>
                <Switch
                  checked={options.consolidateIngredients}
                  onCheckedChange={(checked) => 
                    setOptions(prev => ({ ...prev, consolidateIngredients: checked }))
                  }
                />
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-700">Điều chỉnh theo khẩu phần</span>
                <Switch
                  checked={options.adjustForServings}
                  onCheckedChange={(checked) => 
                    setOptions(prev => ({ ...prev, adjustForServings: checked }))
                  }
                />
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Shopping List Content */}
      <Tabs defaultValue="by-category" className="space-y-6">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="by-category">Theo danh mục</TabsTrigger>
          <TabsTrigger value="by-meal">Theo bữa ăn</TabsTrigger>
          <TabsTrigger value="tips">Mẹo mua sắm</TabsTrigger>
        </TabsList>

        <TabsContent value="by-category">
          <div className="space-y-4">
            {shoppingList.sessions.map((session) => (
              <Card key={session.id}>
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <span className="text-2xl">{session.icon}</span>
                      <div>
                        <h3 className="font-semibold text-gray-900">{session.name}</h3>
                        <p className="text-sm text-gray-600">{session.description}</p>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge variant="outline">
                        {session.items.length} món
                      </Badge>
                      <Badge variant="outline">
                        {formatPrice(session.totalEstimatedCost)}
                      </Badge>
                    </div>
                  </div>
                </CardHeader>
                
                <CardContent>
                  <div className="space-y-2">
                    {session.items.map((item) => (
                      <div key={item.id} className="flex items-center justify-between p-2 rounded border">
                        <div className="flex items-center gap-3">
                          <Circle className="h-4 w-4 text-gray-400" />
                          <div>
                            <span className="font-medium">{item.name}</span>
                            <div className="text-sm text-gray-600">
                              {item.quantity} {item.unit} • {formatPrice(item.estimatedPrice || 0)}
                            </div>
                            {item.notes && (
                              <div className="text-xs text-gray-500">{item.notes}</div>
                            )}
                          </div>
                        </div>
                        <Badge variant="outline" className="text-xs">
                          {item.category}
                        </Badge>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="by-meal">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Phân bố theo bữa ăn</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="flex items-center gap-2">
                      ☀️ Bữa sáng
                    </span>
                    <Badge variant="outline">{shoppingList.summary.breakfastItems} món</Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="flex items-center gap-2">
                      🍽️ Bữa trưa
                    </span>
                    <Badge variant="outline">{shoppingList.summary.lunchItems} món</Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="flex items-center gap-2">
                      🌙 Bữa tối
                    </span>
                    <Badge variant="outline">{shoppingList.summary.dinnerItems} món</Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="flex items-center gap-2">
                      🍪 Ăn vặt
                    </span>
                    <Badge variant="outline">{shoppingList.summary.snackItems} món</Badge>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Thống kê chi tiết</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="flex items-center gap-2">
                      <Package className="h-4 w-4" />
                      Tổng nguyên liệu
                    </span>
                    <span className="font-medium">{shoppingList.totalItems}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="flex items-center gap-2">
                      <TrendingUp className="h-4 w-4" />
                      Loại khác nhau
                    </span>
                    <span className="font-medium">{shoppingList.summary.uniqueIngredients}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="flex items-center gap-2">
                      <Clock className="h-4 w-4" />
                      Thời gian mua
                    </span>
                    <span className="font-medium">{shoppingList.summary.estimatedShoppingTime} phút</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="flex items-center gap-2">
                      <DollarSign className="h-4 w-4" />
                      Chi phí/người
                    </span>
                    <span className="font-medium">
                      {formatPrice(shoppingList.totalEstimatedCost / (options.targetServings || 4))}
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="tips">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Lightbulb className="h-5 w-5 text-yellow-500" />
                Mẹo Mua Sắm Thông Minh
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {shoppingList.shoppingTips.map((tip, index) => (
                  <div key={index} className="flex items-start gap-3 p-3 bg-yellow-50 rounded-lg">
                    <div className="w-6 h-6 bg-yellow-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                      <span className="text-xs font-medium text-yellow-700">{index + 1}</span>
                    </div>
                    <p className="text-sm text-yellow-800">{tip}</p>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

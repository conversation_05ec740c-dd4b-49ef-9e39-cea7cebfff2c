import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  RefreshCw, 
  Database, 
  Cloud, 
  HardDrive, 
  Settings,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Info
} from 'lucide-react';
import {
  debugConnection,
  printDebugReport,
  setupSupabaseDatabase,
  type DebugInfo
} from '@/utils/debug-connection';
import DataMigrationPanel from '@/components/DataMigrationPanel';

const DebugPanel: React.FC = () => {
  const [debugInfo, setDebugInfo] = useState<DebugInfo | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isSettingUp, setIsSettingUp] = useState(false);
  const [showMigration, setShowMigration] = useState(false);

  const runDebug = async () => {
    setIsLoading(true);
    try {
      const info = await debugConnection();
      setDebugInfo(info);
      await printDebugReport(); // Print to console
    } catch (error) {
      console.error('Debug failed:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSetupDatabase = async () => {
    setIsSettingUp(true);
    try {
      await setupSupabaseDatabase();
      // Re-run debug after setup
      await runDebug();
    } catch (error) {
      console.error('Setup failed:', error);
    } finally {
      setIsSettingUp(false);
    }
  };

  useEffect(() => {
    runDebug();
  }, []);

  const getStatusIcon = (success: boolean) => {
    return success ? (
      <CheckCircle className="h-4 w-4 text-green-500" />
    ) : (
      <XCircle className="h-4 w-4 text-red-500" />
    );
  };

  const getStatusBadge = (success: boolean, label: string) => {
    return (
      <Badge variant={success ? "default" : "destructive"} className="ml-2">
        {success ? "✅" : "❌"} {label}
      </Badge>
    );
  };

  if (!debugInfo) {
    return (
      <Card className="w-full max-w-4xl mx-auto">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Connection Debug Panel
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center p-8">
            <RefreshCw className="h-6 w-6 animate-spin mr-2" />
            Loading debug information...
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Settings className="h-5 w-5" />
          Connection Debug Panel
        </CardTitle>
        <div className="flex gap-2">
          <Button 
            onClick={runDebug} 
            disabled={isLoading}
            variant="outline"
            size="sm"
          >
            {isLoading ? (
              <RefreshCw className="h-4 w-4 animate-spin mr-2" />
            ) : (
              <RefreshCw className="h-4 w-4 mr-2" />
            )}
            Refresh Debug
          </Button>
          
          {debugInfo.environment.adapterType === 'supabase' &&
           !debugInfo.supabaseConnection.connectionTest.success && (
            <Button
              onClick={handleSetupDatabase}
              disabled={isSettingUp}
              variant="default"
              size="sm"
            >
              {isSettingUp ? (
                <RefreshCw className="h-4 w-4 animate-spin mr-2" />
              ) : (
                <Database className="h-4 w-4 mr-2" />
              )}
              Setup Database
            </Button>
          )}

          <Button
            onClick={() => setShowMigration(!showMigration)}
            variant="outline"
            size="sm"
          >
            <Database className="h-4 w-4 mr-2" />
            {showMigration ? 'Hide' : 'Show'} Migration
          </Button>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* Environment Configuration */}
        <div>
          <h3 className="text-lg font-semibold mb-3 flex items-center gap-2">
            <Info className="h-5 w-5" />
            Environment Configuration
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="p-3 bg-gray-50 rounded-lg">
              <div className="text-sm font-medium text-gray-600">Database Adapter</div>
              <div className="text-lg font-mono">{debugInfo.environment.adapterType}</div>
            </div>
            <div className="p-3 bg-gray-50 rounded-lg">
              <div className="text-sm font-medium text-gray-600">Supabase URL</div>
              <div className="text-sm font-mono truncate">{debugInfo.environment.supabaseUrl}</div>
            </div>
            <div className="p-3 bg-gray-50 rounded-lg">
              <div className="text-sm font-medium text-gray-600">Has Supabase Key</div>
              <div className="flex items-center">
                {getStatusIcon(debugInfo.environment.hasSupabaseKey)}
                <span className="ml-2">{debugInfo.environment.hasSupabaseKey ? 'Yes' : 'No'}</span>
              </div>
            </div>
          </div>
        </div>

        <Separator />

        {/* Supabase Connection */}
        <div>
          <h3 className="text-lg font-semibold mb-3 flex items-center gap-2">
            <Cloud className="h-5 w-5" />
            Supabase Connection
          </h3>
          <div className="space-y-3">
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <span>Client Initialized</span>
              {getStatusBadge(debugInfo.supabaseConnection.isInitialized, 
                debugInfo.supabaseConnection.isInitialized ? 'Ready' : 'Not Ready')}
            </div>
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <span>Has Client</span>
              {getStatusBadge(debugInfo.supabaseConnection.hasClient, 
                debugInfo.supabaseConnection.hasClient ? 'Available' : 'Missing')}
            </div>
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <span>Connection Test</span>
              {getStatusBadge(debugInfo.supabaseConnection.connectionTest.success, 
                debugInfo.supabaseConnection.connectionTest.success ? 'Success' : 'Failed')}
            </div>
            
            {!debugInfo.supabaseConnection.connectionTest.success && (
              <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
                <div className="text-sm font-medium text-red-800 mb-2">Connection Error Details:</div>
                <pre className="text-xs text-red-700 whitespace-pre-wrap">
                  {JSON.stringify(
                    debugInfo.supabaseConnection.connectionTest.details || 
                    debugInfo.supabaseConnection.connectionTest.error, 
                    null, 2
                  )}
                </pre>
              </div>
            )}
          </div>
        </div>

        <Separator />

        {/* Data Location */}
        <div>
          <h3 className="text-lg font-semibold mb-3 flex items-center gap-2">
            <HardDrive className="h-5 w-5" />
            Data Location Analysis
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="p-4 border rounded-lg">
              <div className="flex items-center justify-between mb-2">
                <span className="font-medium">Local Storage</span>
                {getStatusBadge(debugInfo.dataLocation.hasLocalStorageData, 
                  debugInfo.dataLocation.hasLocalStorageData ? 'Has Data' : 'Empty')}
              </div>
              {debugInfo.dataLocation.localStorageKeys.length > 0 && (
                <div className="text-sm text-gray-600">
                  <div className="font-medium mb-1">Keys found:</div>
                  <ul className="list-disc list-inside space-y-1">
                    {debugInfo.dataLocation.localStorageKeys.map(key => (
                      <li key={key} className="font-mono text-xs">{key}</li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
            
            <div className="p-4 border rounded-lg">
              <div className="flex items-center justify-between mb-2">
                <span className="font-medium">Supabase Database</span>
                {getStatusBadge(debugInfo.dataLocation.hasSupabaseData, 
                  debugInfo.dataLocation.hasSupabaseData ? 'Has Data' : 'Empty')}
              </div>
              <div className="text-sm text-gray-600">
                {debugInfo.supabaseConnection.connectionTest.success 
                  ? 'Connection successful, checked for data'
                  : 'Cannot check - connection failed'
                }
              </div>
            </div>
          </div>
        </div>

        <Separator />

        {/* Current Adapter */}
        <div>
          <h3 className="text-lg font-semibold mb-3 flex items-center gap-2">
            <Database className="h-5 w-5" />
            Current Active Adapter
          </h3>
          <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <div className="text-lg font-mono font-semibold text-blue-800">
              {debugInfo.currentAdapter}
            </div>
            <div className="text-sm text-blue-600 mt-1">
              This is the adapter currently being used to store and retrieve data.
            </div>
          </div>
        </div>

        {/* Recommendations */}
        {(debugInfo.environment.adapterType === 'supabase' && !debugInfo.supabaseConnection.connectionTest.success) ||
         (debugInfo.dataLocation.hasLocalStorageData && !debugInfo.dataLocation.hasSupabaseData) ||
         (debugInfo.currentAdapter === 'LocalStorageAdapter' && debugInfo.environment.adapterType === 'supabase') ? (
          <>
            <Separator />
            <div>
              <h3 className="text-lg font-semibold mb-3 flex items-center gap-2">
                <AlertTriangle className="h-5 w-5 text-yellow-500" />
                Recommendations
              </h3>
              <div className="space-y-2">
                {debugInfo.environment.adapterType === 'supabase' && !debugInfo.supabaseConnection.connectionTest.success && (
                  <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                    <div className="text-sm font-medium text-yellow-800">
                      ⚠️ Supabase is configured but connection failed
                    </div>
                    <div className="text-sm text-yellow-700 mt-1">
                      Check if database tables are created. Try running the "Setup Database" button above.
                    </div>
                  </div>
                )}
                
                {debugInfo.dataLocation.hasLocalStorageData && !debugInfo.dataLocation.hasSupabaseData && (
                  <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
                    <div className="text-sm font-medium text-blue-800">
                      📦 Data is currently stored in localStorage
                    </div>
                    <div className="text-sm text-blue-700 mt-1">
                      Consider migrating to Supabase once connection is fixed for better data persistence.
                    </div>
                  </div>
                )}
                
                {debugInfo.currentAdapter === 'LocalStorageAdapter' && debugInfo.environment.adapterType === 'supabase' && (
                  <div className="p-3 bg-orange-50 border border-orange-200 rounded-lg">
                    <div className="text-sm font-medium text-orange-800">
                      🔄 System has fallen back to localStorage due to Supabase issues
                    </div>
                    <div className="text-sm text-orange-700 mt-1">
                      Fix Supabase connection to use cloud storage instead of local storage.
                    </div>
                  </div>
                )}
              </div>
            </div>
          </>
        ) : null}

        {/* Migration Panel */}
        {showMigration && (
          <>
            <Separator />
            <div>
              <h3 className="text-lg font-semibold mb-3 flex items-center gap-2">
                <Database className="h-5 w-5" />
                Data Migration
              </h3>
              <DataMigrationPanel />
            </div>
          </>
        )}
      </CardContent>
    </Card>
  );
};

export default DebugPanel;

import React, { useState, useRef, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { cva, type VariantProps } from 'class-variance-authority';
import { cn } from '@/lib/utils';
import { 
  Menu, 
  X, 
  Search, 
  ChefHat, 
  Heart, 
  ShoppingCart, 
  User, 
  Bell,
  Settings,
  LogOut,
  Home,
  BookOpen,
  Calendar,
  TrendingUp
} from 'lucide-react';
import { Button, IconButton } from '../atoms/Button';
import { SearchInput } from '../atoms/Input';
import { Typography } from '../atoms/Typography';
import { Container } from '../layout/Container';
import { 
  Modal, 
  ModalContent, 
  ModalHeader 
} from '../molecules/Modal';
import type { BaseComponentProps } from '@/types/design-system';

// Header variants using CVA
const headerVariants = cva(
  'sticky top-0 z-50 w-full border-b transition-all duration-200',
  {
    variants: {
      variant: {
        default: 'bg-white/95 backdrop-blur-sm border-gray-200 dark:bg-gray-900/95 dark:border-gray-800',
        transparent: 'bg-transparent border-transparent',
        elevated: 'bg-white shadow-lg border-gray-200 dark:bg-gray-900 dark:border-gray-800',
        kitchen: 'bg-gradient-to-r from-orange-50 to-red-50 border-orange-200 dark:from-gray-900 dark:to-gray-800 dark:border-gray-700',
      },
      size: {
        sm: 'h-14',
        md: 'h-16',
        lg: 'h-20',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'md',
    },
  }
);

// Enhanced Header Props
export interface EnhancedHeaderProps extends BaseComponentProps {
  variant?: 'default' | 'transparent' | 'elevated' | 'kitchen';
  size?: 'sm' | 'md' | 'lg';
  showSearch?: boolean;
  showNotifications?: boolean;
  showCart?: boolean;
  logo?: React.ReactNode;
  navigation?: NavItem[];
  user?: {
    name: string;
    email: string;
    avatar?: string;
  };
  onSearch?: (query: string) => void;
  onCartClick?: () => void;
  onProfileClick?: () => void;
  onLogout?: () => void;
  cartItemCount?: number;
  notificationCount?: number;
}

export interface NavItem {
  label: string;
  href: string;
  icon?: React.ReactNode;
  badge?: string | number;
  active?: boolean;
}

// Mobile Menu Component
const MobileMenu: React.FC<{
  isOpen: boolean;
  onClose: () => void;
  navigation: NavItem[];
  user?: EnhancedHeaderProps['user'];
  onLogout?: () => void;
}> = ({ isOpen, onClose, navigation, user, onLogout }) => {
  return (
    <Modal
      open={isOpen}
      onClose={onClose}
      size="full"
      animation="slide"
      className="md:hidden"
    >
      <ModalContent className="p-0">
        <div className="flex flex-col h-full">
          {/* Mobile Header */}
          <div className="flex items-center justify-between p-4 border-b border-gray-200">
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 bg-orange-500 rounded-lg flex items-center justify-center">
                <ChefHat className="w-5 h-5 text-white" />
              </div>
              <Typography variant="h6" weight="bold">
                Angiday Kitchen
              </Typography>
            </div>
            <IconButton
              icon={<X className="w-5 h-5" />}
              variant="ghost"
              onClick={onClose}
              aria-label="Close menu"
            />
          </div>

          {/* Navigation */}
          <nav className="flex-1 p-4">
            <div className="space-y-2">
              {navigation.map((item, index) => (
                <Link
                  key={index}
                  to={item.href}
                  onClick={onClose}
                  className={cn(
                    'flex items-center gap-3 px-4 py-3 rounded-lg transition-colors',
                    item.active
                      ? 'bg-orange-100 text-orange-700 dark:bg-orange-900/20 dark:text-orange-300'
                      : 'text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-800'
                  )}
                >
                  {item.icon && <span className="w-5 h-5">{item.icon}</span>}
                  <span className="font-medium">{item.label}</span>
                  {item.badge && (
                    <span className="ml-auto bg-orange-100 text-orange-700 text-xs px-2 py-1 rounded-full">
                      {item.badge}
                    </span>
                  )}
                </Link>
              ))}
            </div>
          </nav>

          {/* User Section */}
          {user && (
            <div className="p-4 border-t border-gray-200">
              <div className="flex items-center gap-3 mb-4">
                <div className="w-10 h-10 bg-orange-500 rounded-full flex items-center justify-center">
                  <User className="w-5 h-5 text-white" />
                </div>
                <div>
                  <Typography variant="subtitle2" weight="medium">
                    {user.name}
                  </Typography>
                  <Typography variant="body2" color="muted">
                    {user.email}
                  </Typography>
                </div>
              </div>
              
              <div className="space-y-2">
                <Button
                  variant="ghost"
                  className="w-full justify-start"
                  leftIcon={<Settings className="w-4 h-4" />}
                >
                  Settings
                </Button>
                <Button
                  variant="ghost"
                  className="w-full justify-start text-red-600 hover:text-red-700 hover:bg-red-50"
                  leftIcon={<LogOut className="w-4 h-4" />}
                  onClick={onLogout}
                >
                  Sign Out
                </Button>
              </div>
            </div>
          )}
        </div>
      </ModalContent>
    </Modal>
  );
};

// User Dropdown Component
const UserDropdown: React.FC<{
  user: NonNullable<EnhancedHeaderProps['user']>;
  onLogout?: () => void;
  onProfileClick?: () => void;
}> = ({ user, onLogout, onProfileClick }) => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  return (
    <div className="relative" ref={dropdownRef}>
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center gap-2 p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
      >
        <div className="w-8 h-8 bg-orange-500 rounded-full flex items-center justify-center">
          <User className="w-4 h-4 text-white" />
        </div>
        <div className="hidden sm:block text-left">
          <Typography variant="body2" weight="medium" className="leading-none">
            {user.name}
          </Typography>
          <Typography variant="caption" color="muted" className="leading-none">
            {user.email}
          </Typography>
        </div>
      </button>

      {isOpen && (
        <div className="absolute right-0 mt-2 w-64 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 py-2 z-50">
          <div className="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
            <Typography variant="subtitle2" weight="medium">
              {user.name}
            </Typography>
            <Typography variant="body2" color="muted">
              {user.email}
            </Typography>
          </div>
          
          <div className="py-2">
            <button
              onClick={() => {
                onProfileClick?.();
                setIsOpen(false);
              }}
              className="flex items-center gap-3 w-full px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
            >
              <User className="w-4 h-4" />
              Profile
            </button>
            <button
              onClick={() => setIsOpen(false)}
              className="flex items-center gap-3 w-full px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
            >
              <Settings className="w-4 h-4" />
              Settings
            </button>
          </div>
          
          <div className="border-t border-gray-200 dark:border-gray-700 py-2">
            <button
              onClick={() => {
                onLogout?.();
                setIsOpen(false);
              }}
              className="flex items-center gap-3 w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50 dark:hover:bg-red-900/20"
            >
              <LogOut className="w-4 h-4" />
              Sign Out
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

// Main Enhanced Header Component
export const EnhancedHeader: React.FC<EnhancedHeaderProps> = ({
  variant = 'default',
  size = 'md',
  showSearch = true,
  showNotifications = true,
  showCart = true,
  logo,
  navigation = [],
  user,
  onSearch,
  onCartClick,
  onProfileClick,
  onLogout,
  cartItemCount = 0,
  notificationCount = 0,
  className,
  'data-testid': testId,
  ...props
}) => {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const navigate = useNavigate();

  const handleSearch = (query: string) => {
    onSearch?.(query);
    // Default search behavior
    if (!onSearch && query.trim()) {
      navigate(`/search?q=${encodeURIComponent(query.trim())}`);
    }
  };

  const defaultNavigation: NavItem[] = [
    { label: 'Home', href: '/', icon: <Home className="w-4 h-4" />, active: true },
    { label: 'Recipes', href: '/recipes', icon: <BookOpen className="w-4 h-4" />, badge: '156' },
    { label: 'Meal Planner', href: '/meal-planner', icon: <Calendar className="w-4 h-4" /> },
    { label: 'Trending', href: '/trending', icon: <TrendingUp className="w-4 h-4" /> },
  ];

  const navItems = navigation.length > 0 ? navigation : defaultNavigation;

  return (
    <header
      className={cn(headerVariants({ variant, size }), className)}
      data-testid={testId}
      {...props}
    >
      <Container size="xl" className="h-full">
        <div className="flex items-center justify-between h-full">
          {/* Logo & Brand */}
          <div className="flex items-center gap-8">
            <Link to="/" className="flex items-center gap-3">
              {logo || (
                <>
                  <div className="w-8 h-8 bg-orange-500 rounded-lg flex items-center justify-center">
                    <ChefHat className="w-5 h-5 text-white" />
                  </div>
                  <Typography variant="h6" weight="bold" className="hidden sm:block">
                    Angiday Kitchen
                  </Typography>
                </>
              )}
            </Link>

            {/* Desktop Navigation */}
            <nav className="hidden lg:flex items-center gap-1">
              {navItems.map((item, index) => (
                <Link
                  key={index}
                  to={item.href}
                  className={cn(
                    'flex items-center gap-2 px-3 py-2 rounded-lg text-sm font-medium transition-colors',
                    item.active
                      ? 'bg-orange-100 text-orange-700 dark:bg-orange-900/20 dark:text-orange-300'
                      : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100 dark:text-gray-400 dark:hover:text-gray-100 dark:hover:bg-gray-800'
                  )}
                >
                  {item.icon}
                  <span>{item.label}</span>
                  {item.badge && (
                    <span className="bg-orange-100 text-orange-700 text-xs px-2 py-0.5 rounded-full">
                      {item.badge}
                    </span>
                  )}
                </Link>
              ))}
            </nav>
          </div>

          {/* Search & Actions */}
          <div className="flex items-center gap-3">
            {/* Search */}
            {showSearch && (
              <div className="hidden md:block w-64 lg:w-80">
                <SearchInput
                  placeholder="Search recipes, ingredients..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  onSearch={handleSearch}
                  size="sm"
                />
              </div>
            )}

            {/* Action Buttons */}
            <div className="flex items-center gap-2">
              {/* Mobile Search */}
              {showSearch && (
                <IconButton
                  icon={<Search className="w-4 h-4" />}
                  variant="ghost"
                  size="sm"
                  className="md:hidden"
                  aria-label="Search"
                />
              )}

              {/* Notifications */}
              {showNotifications && (
                <div className="relative">
                  <IconButton
                    icon={<Bell className="w-4 h-4" />}
                    variant="ghost"
                    size="sm"
                    aria-label="Notifications"
                  />
                  {notificationCount > 0 && (
                    <span className="absolute -top-1 -right-1 w-5 h-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center">
                      {notificationCount > 9 ? '9+' : notificationCount}
                    </span>
                  )}
                </div>
              )}

              {/* Shopping Cart */}
              {showCart && (
                <div className="relative">
                  <IconButton
                    icon={<ShoppingCart className="w-4 h-4" />}
                    variant="ghost"
                    size="sm"
                    onClick={onCartClick}
                    aria-label="Shopping cart"
                  />
                  {cartItemCount > 0 && (
                    <span className="absolute -top-1 -right-1 w-5 h-5 bg-orange-500 text-white text-xs rounded-full flex items-center justify-center">
                      {cartItemCount > 9 ? '9+' : cartItemCount}
                    </span>
                  )}
                </div>
              )}

              {/* User Menu */}
              {user ? (
                <UserDropdown
                  user={user}
                  onLogout={onLogout}
                  onProfileClick={onProfileClick}
                />
              ) : (
                <div className="hidden sm:flex items-center gap-2">
                  <Button variant="ghost" size="sm">
                    Sign In
                  </Button>
                  <Button size="sm">
                    Sign Up
                  </Button>
                </div>
              )}

              {/* Mobile Menu Button */}
              <IconButton
                icon={<Menu className="w-5 h-5" />}
                variant="ghost"
                size="sm"
                onClick={() => setIsMobileMenuOpen(true)}
                className="lg:hidden"
                aria-label="Open menu"
              />
            </div>
          </div>
        </div>
      </Container>

      {/* Mobile Menu */}
      <MobileMenu
        isOpen={isMobileMenuOpen}
        onClose={() => setIsMobileMenuOpen(false)}
        navigation={navItems}
        user={user}
        onLogout={onLogout}
      />
    </header>
  );
};

export default EnhancedHeader;

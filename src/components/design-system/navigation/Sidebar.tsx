import React, { forwardRef, useState } from 'react';
import { cva, type VariantProps } from 'class-variance-authority';
import { cn } from '@/lib/utils';
import { 
  ChevronLeft, 
  ChevronRight, 
  Home, 
  ChefHat, 
  Calendar, 
  ShoppingCart, 
  Heart, 
  Settings,
  User,
  Search,
  BookOpen,
  TrendingUp
} from 'lucide-react';
import { Button, IconButton } from '../atoms/Button';
import { Typography } from '../atoms/Typography';
import type { BaseComponentProps } from '@/types/design-system';

// Sidebar variants using CVA
const sidebarVariants = cva(
  'flex flex-col bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 transition-all duration-300',
  {
    variants: {
      variant: {
        default: '',
        floating: 'rounded-lg shadow-lg border',
        minimal: 'border-none',
      },
      size: {
        sm: 'w-56',
        md: 'w-64',
        lg: 'w-72',
        xl: 'w-80',
      },
      collapsed: {
        true: 'w-16',
        false: '',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'md',
      collapsed: false,
    },
  }
);

// Navigation item variants
const navItemVariants = cva(
  'flex items-center gap-3 px-3 py-2 rounded-lg transition-all duration-150 text-sm font-medium',
  {
    variants: {
      variant: {
        default: 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700',
        active: 'bg-orange-100 dark:bg-orange-900/20 text-orange-700 dark:text-orange-300',
        ghost: 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100',
      },
      size: {
        sm: 'px-2 py-1.5 text-xs',
        md: 'px-3 py-2 text-sm',
        lg: 'px-4 py-3 text-base',
      },
      collapsed: {
        true: 'justify-center px-2',
        false: '',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'md',
      collapsed: false,
    },
  }
);

// Sidebar Props Interface
export interface SidebarProps extends BaseComponentProps {
  variant?: 'default' | 'floating' | 'minimal';
  size?: 'sm' | 'md' | 'lg' | 'xl';
  collapsed?: boolean;
  collapsible?: boolean;
  onToggle?: () => void;
  header?: React.ReactNode;
  footer?: React.ReactNode;
}

// Navigation Item Interface
export interface NavItemProps extends BaseComponentProps {
  icon?: React.ReactNode;
  label: string;
  href?: string;
  active?: boolean;
  badge?: string | number;
  onClick?: () => void;
  collapsed?: boolean;
}

// Navigation Item Component
export const NavItem = forwardRef<HTMLButtonElement, NavItemProps>(
  (
    {
      icon,
      label,
      href,
      active = false,
      badge,
      onClick,
      collapsed = false,
      className,
      'data-testid': testId,
      ...props
    },
    ref
  ) => {
    const Comp = href ? 'a' : 'button';
    const linkProps = href ? { href } : {};

    return (
      <Comp
        ref={ref as any}
        className={cn(
          navItemVariants({ 
            variant: active ? 'active' : 'default', 
            collapsed 
          }),
          className
        )}
        onClick={onClick}
        data-testid={testId}
        {...linkProps}
        {...props}
      >
        {/* Icon */}
        {icon && (
          <span className="flex-shrink-0 w-5 h-5">
            {icon}
          </span>
        )}

        {/* Label */}
        {!collapsed && (
          <span className="flex-1 truncate">
            {label}
          </span>
        )}

        {/* Badge */}
        {!collapsed && badge && (
          <span className="flex-shrink-0 bg-orange-100 dark:bg-orange-900/30 text-orange-700 dark:text-orange-300 text-xs px-2 py-0.5 rounded-full">
            {badge}
          </span>
        )}

        {/* Tooltip for collapsed state */}
        {collapsed && (
          <div className="absolute left-full ml-2 px-2 py-1 bg-gray-900 text-white text-xs rounded opacity-0 pointer-events-none group-hover:opacity-100 transition-opacity whitespace-nowrap z-50">
            {label}
          </div>
        )}
      </Comp>
    );
  }
);

NavItem.displayName = 'NavItem';

// Navigation Group Component
export interface NavGroupProps extends BaseComponentProps {
  title?: string;
  collapsed?: boolean;
}

export const NavGroup = forwardRef<HTMLDivElement, NavGroupProps>(
  (
    {
      title,
      collapsed = false,
      children,
      className,
      'data-testid': testId,
      ...props
    },
    ref
  ) => {
    return (
      <div
        ref={ref}
        className={cn('space-y-1', className)}
        data-testid={testId}
        {...props}
      >
        {/* Group Title */}
        {title && !collapsed && (
          <Typography
            variant="caption"
            color="muted"
            className="px-3 py-2 uppercase tracking-wider"
          >
            {title}
          </Typography>
        )}

        {/* Group Items */}
        <div className="space-y-1">
          {React.Children.map(children, (child) => {
            if (React.isValidElement(child) && child.type === NavItem) {
              return React.cloneElement(child, { collapsed });
            }
            return child;
          })}
        </div>
      </div>
    );
  }
);

NavGroup.displayName = 'NavGroup';

// Main Sidebar Component
export const Sidebar = forwardRef<HTMLDivElement, SidebarProps>(
  (
    {
      variant = 'default',
      size = 'md',
      collapsed = false,
      collapsible = true,
      onToggle,
      header,
      footer,
      children,
      className,
      'data-testid': testId,
      ...props
    },
    ref
  ) => {
    return (
      <div
        ref={ref}
        className={cn(
          sidebarVariants({ variant, size, collapsed }),
          className
        )}
        data-testid={testId}
        {...props}
      >
        {/* Header */}
        {header && (
          <div className="flex-shrink-0 p-4 border-b border-gray-200 dark:border-gray-700">
            {header}
          </div>
        )}

        {/* Navigation Content */}
        <nav className="flex-1 overflow-y-auto p-4 space-y-6">
          {React.Children.map(children, (child) => {
            if (React.isValidElement(child) && (child.type === NavGroup || child.type === NavItem)) {
              return React.cloneElement(child, { collapsed });
            }
            return child;
          })}
        </nav>

        {/* Footer */}
        {footer && (
          <div className="flex-shrink-0 p-4 border-t border-gray-200 dark:border-gray-700">
            {footer}
          </div>
        )}

        {/* Collapse Toggle */}
        {collapsible && onToggle && (
          <div className="flex-shrink-0 p-2 border-t border-gray-200 dark:border-gray-700">
            <IconButton
              icon={collapsed ? <ChevronRight className="w-4 h-4" /> : <ChevronLeft className="w-4 h-4" />}
              variant="ghost"
              size="sm"
              onClick={onToggle}
              aria-label={collapsed ? 'Expand sidebar' : 'Collapse sidebar'}
              className="w-full"
            />
          </div>
        )}
      </div>
    );
  }
);

Sidebar.displayName = 'Sidebar';

// Kitchen Sidebar Component (Preset for Angiday Kitchen)
export interface KitchenSidebarProps extends Omit<SidebarProps, 'children'> {
  activeItem?: string;
  onItemClick?: (item: string) => void;
}

export const KitchenSidebar = forwardRef<HTMLDivElement, KitchenSidebarProps>(
  (
    {
      activeItem = 'dashboard',
      onItemClick,
      collapsed = false,
      ...props
    },
    ref
  ) => {
    const handleItemClick = (item: string) => {
      onItemClick?.(item);
    };

    return (
      <Sidebar
        ref={ref}
        collapsed={collapsed}
        header={
          !collapsed ? (
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 bg-orange-500 rounded-lg flex items-center justify-center">
                <ChefHat className="w-5 h-5 text-white" />
              </div>
              <Typography variant="h6" weight="bold">
                Angiday Kitchen
              </Typography>
            </div>
          ) : (
            <div className="flex justify-center">
              <div className="w-8 h-8 bg-orange-500 rounded-lg flex items-center justify-center">
                <ChefHat className="w-5 h-5 text-white" />
              </div>
            </div>
          )
        }
        footer={
          <NavItem
            icon={<Settings className="w-5 h-5" />}
            label="Settings"
            active={activeItem === 'settings'}
            onClick={() => handleItemClick('settings')}
            collapsed={collapsed}
          />
        }
        {...props}
      >
        {/* Main Navigation */}
        <NavGroup title="Main">
          <NavItem
            icon={<Home className="w-5 h-5" />}
            label="Dashboard"
            active={activeItem === 'dashboard'}
            onClick={() => handleItemClick('dashboard')}
          />
          <NavItem
            icon={<Search className="w-5 h-5" />}
            label="Discover"
            active={activeItem === 'discover'}
            onClick={() => handleItemClick('discover')}
          />
          <NavItem
            icon={<BookOpen className="w-5 h-5" />}
            label="My Recipes"
            active={activeItem === 'recipes'}
            onClick={() => handleItemClick('recipes')}
            badge="24"
          />
        </NavGroup>

        {/* Planning */}
        <NavGroup title="Planning">
          <NavItem
            icon={<Calendar className="w-5 h-5" />}
            label="Meal Planner"
            active={activeItem === 'meal-planner'}
            onClick={() => handleItemClick('meal-planner')}
          />
          <NavItem
            icon={<ShoppingCart className="w-5 h-5" />}
            label="Shopping List"
            active={activeItem === 'shopping'}
            onClick={() => handleItemClick('shopping')}
            badge="12"
          />
        </NavGroup>

        {/* Personal */}
        <NavGroup title="Personal">
          <NavItem
            icon={<Heart className="w-5 h-5" />}
            label="Favorites"
            active={activeItem === 'favorites'}
            onClick={() => handleItemClick('favorites')}
          />
          <NavItem
            icon={<TrendingUp className="w-5 h-5" />}
            label="Analytics"
            active={activeItem === 'analytics'}
            onClick={() => handleItemClick('analytics')}
          />
          <NavItem
            icon={<User className="w-5 h-5" />}
            label="Profile"
            active={activeItem === 'profile'}
            onClick={() => handleItemClick('profile')}
          />
        </NavGroup>
      </Sidebar>
    );
  }
);

KitchenSidebar.displayName = 'KitchenSidebar';

// Compound exports
Sidebar.Item = NavItem;
Sidebar.Group = NavGroup;

export default Sidebar;

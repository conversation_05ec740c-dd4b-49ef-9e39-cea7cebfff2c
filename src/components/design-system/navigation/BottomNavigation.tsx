import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { cva, type VariantProps } from 'class-variance-authority';
import { cn } from '@/lib/utils';
import { 
  Home, 
  Search, 
  BookOpen, 
  Calendar, 
  User,
  ChefHat,
  Heart,
  ShoppingCart,
  TrendingUp
} from 'lucide-react';
import { Typography } from '../atoms/Typography';
import type { BaseComponentProps } from '@/types/design-system';

// Bottom Navigation variants using CVA
const bottomNavVariants = cva(
  'fixed bottom-0 left-0 right-0 z-40 bg-white dark:bg-gray-900 border-t border-gray-200 dark:border-gray-800 safe-area-pb',
  {
    variants: {
      variant: {
        default: '',
        elevated: 'shadow-lg',
        blur: 'bg-white/95 backdrop-blur-sm dark:bg-gray-900/95',
      },
      size: {
        sm: 'h-16',
        md: 'h-20',
        lg: 'h-24',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'md',
    },
  }
);

const navItemVariants = cva(
  'flex flex-col items-center justify-center gap-1 px-2 py-2 transition-all duration-200 rounded-lg mx-1',
  {
    variants: {
      active: {
        true: 'text-orange-600 dark:text-orange-400',
        false: 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300',
      },
      size: {
        sm: 'min-h-[48px]',
        md: 'min-h-[56px]',
        lg: 'min-h-[64px]',
      },
    },
    defaultVariants: {
      active: false,
      size: 'md',
    },
  }
);

// Bottom Navigation Props
export interface BottomNavigationProps extends BaseComponentProps {
  variant?: 'default' | 'elevated' | 'blur';
  size?: 'sm' | 'md' | 'lg';
  items?: BottomNavItem[];
  showLabels?: boolean;
  hapticFeedback?: boolean;
}

export interface BottomNavItem {
  id: string;
  label: string;
  icon: React.ReactNode;
  href: string;
  badge?: string | number;
  disabled?: boolean;
}

// Default navigation items for Angiday Kitchen
const defaultNavItems: BottomNavItem[] = [
  {
    id: 'home',
    label: 'Home',
    icon: <Home className="w-5 h-5" />,
    href: '/',
  },
  {
    id: 'discover',
    label: 'Discover',
    icon: <Search className="w-5 h-5" />,
    href: '/discover',
  },
  {
    id: 'recipes',
    label: 'Recipes',
    icon: <BookOpen className="w-5 h-5" />,
    href: '/recipes',
    badge: '24',
  },
  {
    id: 'planner',
    label: 'Planner',
    icon: <Calendar className="w-5 h-5" />,
    href: '/meal-planner',
  },
  {
    id: 'profile',
    label: 'Profile',
    icon: <User className="w-5 h-5" />,
    href: '/profile',
  },
];

// Navigation Item Component
const BottomNavItem: React.FC<{
  item: BottomNavItem;
  isActive: boolean;
  showLabel: boolean;
  size: 'sm' | 'md' | 'lg';
  onHapticFeedback?: () => void;
}> = ({ item, isActive, showLabel, size, onHapticFeedback }) => {
  const handleClick = () => {
    // Trigger haptic feedback on supported devices
    if (onHapticFeedback && 'vibrate' in navigator) {
      navigator.vibrate(10); // Short vibration
      onHapticFeedback();
    }
  };

  const iconSizes = {
    sm: 'w-4 h-4',
    md: 'w-5 h-5',
    lg: 'w-6 h-6',
  };

  const textSizes = {
    sm: 'text-xs',
    md: 'text-xs',
    lg: 'text-sm',
  };

  if (item.disabled) {
    return (
      <div className={cn(navItemVariants({ size }), 'opacity-50 cursor-not-allowed')}>
        <div className={cn('relative', iconSizes[size])}>
          {React.cloneElement(item.icon as React.ReactElement, {
            className: iconSizes[size],
          })}
        </div>
        {showLabel && (
          <Typography
            variant="caption"
            className={cn(textSizes[size], 'leading-none')}
          >
            {item.label}
          </Typography>
        )}
      </div>
    );
  }

  return (
    <Link
      to={item.href}
      onClick={handleClick}
      className={cn(navItemVariants({ active: isActive, size }))}
    >
      <div className="relative">
        {React.cloneElement(item.icon as React.ReactElement, {
          className: iconSizes[size],
        })}
        
        {/* Badge */}
        {item.badge && (
          <span className="absolute -top-2 -right-2 min-w-[16px] h-4 bg-orange-500 text-white text-xs rounded-full flex items-center justify-center px-1">
            {item.badge}
          </span>
        )}
        
        {/* Active Indicator */}
        {isActive && (
          <div className="absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-1 h-1 bg-orange-500 rounded-full" />
        )}
      </div>
      
      {showLabel && (
        <Typography
          variant="caption"
          className={cn(
            textSizes[size],
            'leading-none font-medium',
            isActive ? 'text-orange-600 dark:text-orange-400' : ''
          )}
        >
          {item.label}
        </Typography>
      )}
    </Link>
  );
};

// Main Bottom Navigation Component
export const BottomNavigation: React.FC<BottomNavigationProps> = ({
  variant = 'default',
  size = 'md',
  items = defaultNavItems,
  showLabels = true,
  hapticFeedback = true,
  className,
  'data-testid': testId,
  ...props
}) => {
  const location = useLocation();

  const handleHapticFeedback = () => {
    if (hapticFeedback && 'vibrate' in navigator) {
      navigator.vibrate(10);
    }
  };

  // Only show on mobile devices
  const isMobile = typeof window !== 'undefined' && window.innerWidth < 768;
  
  if (!isMobile) {
    return null;
  }

  return (
    <nav
      className={cn(bottomNavVariants({ variant, size }), className)}
      data-testid={testId}
      {...props}
    >
      <div className="flex items-center justify-around h-full px-2">
        {items.map((item) => {
          const isActive = location.pathname === item.href || 
            (item.href !== '/' && location.pathname.startsWith(item.href));
          
          return (
            <BottomNavItem
              key={item.id}
              item={item}
              isActive={isActive}
              showLabel={showLabels}
              size={size}
              onHapticFeedback={handleHapticFeedback}
            />
          );
        })}
      </div>
    </nav>
  );
};

// Kitchen Bottom Navigation (Preset)
export const KitchenBottomNav: React.FC<Omit<BottomNavigationProps, 'items'>> = (props) => {
  const kitchenNavItems: BottomNavItem[] = [
    {
      id: 'home',
      label: 'Home',
      icon: <Home className="w-5 h-5" />,
      href: '/',
    },
    {
      id: 'discover',
      label: 'Discover',
      icon: <Search className="w-5 h-5" />,
      href: '/discover',
    },
    {
      id: 'cook',
      label: 'Cook',
      icon: <ChefHat className="w-5 h-5" />,
      href: '/cook',
    },
    {
      id: 'favorites',
      label: 'Favorites',
      icon: <Heart className="w-5 h-5" />,
      href: '/favorites',
    },
    {
      id: 'profile',
      label: 'Profile',
      icon: <User className="w-5 h-5" />,
      href: '/profile',
    },
  ];

  return <BottomNavigation items={kitchenNavItems} {...props} />;
};

// Shopping Bottom Navigation (Alternative preset)
export const ShoppingBottomNav: React.FC<Omit<BottomNavigationProps, 'items'>> = (props) => {
  const shoppingNavItems: BottomNavItem[] = [
    {
      id: 'home',
      label: 'Home',
      icon: <Home className="w-5 h-5" />,
      href: '/',
    },
    {
      id: 'browse',
      label: 'Browse',
      icon: <Search className="w-5 h-5" />,
      href: '/browse',
    },
    {
      id: 'trending',
      label: 'Trending',
      icon: <TrendingUp className="w-5 h-5" />,
      href: '/trending',
    },
    {
      id: 'cart',
      label: 'Cart',
      icon: <ShoppingCart className="w-5 h-5" />,
      href: '/cart',
      badge: '3',
    },
    {
      id: 'account',
      label: 'Account',
      icon: <User className="w-5 h-5" />,
      href: '/account',
    },
  ];

  return <BottomNavigation items={shoppingNavItems} {...props} />;
};

export default BottomNavigation;

import React, { forwardRef } from 'react';
import { Link } from 'react-router-dom';
import { cva, type VariantProps } from 'class-variance-authority';
import { cn } from '@/lib/utils';
import { ChevronRight, Home, MoreHorizontal } from 'lucide-react';
import { Typography } from '../atoms/Typography';
import type { BaseComponentProps } from '@/types/design-system';

// Breadcrumb variants using CVA
const breadcrumbVariants = cva(
  'flex items-center space-x-1 text-sm',
  {
    variants: {
      variant: {
        default: '',
        subtle: 'text-gray-500 dark:text-gray-400',
        prominent: 'text-gray-700 dark:text-gray-300',
      },
      size: {
        sm: 'text-xs',
        md: 'text-sm',
        lg: 'text-base',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'md',
    },
  }
);

const breadcrumbItemVariants = cva(
  'transition-colors duration-150',
  {
    variants: {
      active: {
        true: 'text-gray-900 dark:text-gray-100 font-medium',
        false: 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300',
      },
      clickable: {
        true: 'hover:underline cursor-pointer',
        false: 'cursor-default',
      },
    },
    defaultVariants: {
      active: false,
      clickable: true,
    },
  }
);

// Breadcrumb Props
export interface BreadcrumbProps extends BaseComponentProps {
  variant?: 'default' | 'subtle' | 'prominent';
  size?: 'sm' | 'md' | 'lg';
  items: BreadcrumbItem[];
  separator?: React.ReactNode;
  maxItems?: number;
  showHome?: boolean;
  homeHref?: string;
}

export interface BreadcrumbItem {
  label: string;
  href?: string;
  icon?: React.ReactNode;
  active?: boolean;
  disabled?: boolean;
}

// Breadcrumb Separator Component
const BreadcrumbSeparator: React.FC<{ children?: React.ReactNode }> = ({ 
  children = <ChevronRight className="w-4 h-4" /> 
}) => (
  <span className="text-gray-400 dark:text-gray-600 flex-shrink-0">
    {children}
  </span>
);

// Breadcrumb Item Component
const BreadcrumbItemComponent: React.FC<{
  item: BreadcrumbItem;
  isLast: boolean;
  size: 'sm' | 'md' | 'lg';
}> = ({ item, isLast, size }) => {
  const isClickable = !isLast && !item.disabled && item.href;
  const isActive = isLast || item.active;

  const iconSizes = {
    sm: 'w-3 h-3',
    md: 'w-4 h-4',
    lg: 'w-5 h-5',
  };

  const content = (
    <span className="flex items-center gap-1.5">
      {item.icon && (
        <span className={iconSizes[size]}>
          {item.icon}
        </span>
      )}
      <Typography
        variant={size === 'sm' ? 'caption' : size === 'lg' ? 'body1' : 'body2'}
        className={cn(
          breadcrumbItemVariants({ active: isActive, clickable: isClickable }),
          item.disabled && 'opacity-50 cursor-not-allowed'
        )}
      >
        {item.label}
      </Typography>
    </span>
  );

  if (isClickable && item.href) {
    return (
      <Link
        to={item.href}
        className="flex items-center"
        aria-current={isLast ? 'page' : undefined}
      >
        {content}
      </Link>
    );
  }

  return (
    <span 
      className="flex items-center"
      aria-current={isLast ? 'page' : undefined}
    >
      {content}
    </span>
  );
};

// Collapsed Items Component
const CollapsedItems: React.FC<{
  items: BreadcrumbItem[];
  onExpand?: () => void;
}> = ({ items, onExpand }) => {
  const [isExpanded, setIsExpanded] = React.useState(false);

  const handleClick = () => {
    setIsExpanded(!isExpanded);
    onExpand?.();
  };

  if (isExpanded) {
    return (
      <>
        {items.map((item, index) => (
          <React.Fragment key={index}>
            <BreadcrumbItemComponent item={item} isLast={false} size="md" />
            <BreadcrumbSeparator />
          </React.Fragment>
        ))}
      </>
    );
  }

  return (
    <>
      <button
        onClick={handleClick}
        className="flex items-center gap-1.5 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 transition-colors"
        aria-label={`Show ${items.length} hidden items`}
      >
        <MoreHorizontal className="w-4 h-4" />
      </button>
      <BreadcrumbSeparator />
    </>
  );
};

// Main Breadcrumb Component
export const Breadcrumb = forwardRef<HTMLNavElement, BreadcrumbProps>(
  (
    {
      variant = 'default',
      size = 'md',
      items,
      separator,
      maxItems = 5,
      showHome = true,
      homeHref = '/',
      className,
      'data-testid': testId,
      ...props
    },
    ref
  ) => {
    // Add home item if requested and not already present
    const allItems = React.useMemo(() => {
      if (showHome && items[0]?.href !== homeHref) {
        return [
          {
            label: 'Home',
            href: homeHref,
            icon: <Home className="w-4 h-4" />,
          },
          ...items,
        ];
      }
      return items;
    }, [items, showHome, homeHref]);

    // Handle item collapsing when there are too many items
    const { visibleItems, collapsedItems } = React.useMemo(() => {
      if (allItems.length <= maxItems) {
        return { visibleItems: allItems, collapsedItems: [] };
      }

      // Always show first item (home), last item (current), and some in between
      const first = allItems[0];
      const last = allItems[allItems.length - 1];
      const middle = allItems.slice(1, -1);
      
      if (middle.length <= maxItems - 2) {
        return { visibleItems: allItems, collapsedItems: [] };
      }

      // Show first, some middle items, and last
      const visibleMiddleCount = maxItems - 3; // -3 for first, collapsed indicator, and last
      const visibleMiddle = middle.slice(-visibleMiddleCount);
      const collapsed = middle.slice(0, -visibleMiddleCount);

      return {
        visibleItems: [first, ...visibleMiddle, last],
        collapsedItems: collapsed,
      };
    }, [allItems, maxItems]);

    return (
      <nav
        ref={ref}
        aria-label="Breadcrumb"
        className={cn(breadcrumbVariants({ variant, size }), className)}
        data-testid={testId}
        {...props}
      >
        <ol className="flex items-center space-x-1">
          {/* First item */}
          {visibleItems.length > 0 && (
            <li>
              <BreadcrumbItemComponent 
                item={visibleItems[0]} 
                isLast={visibleItems.length === 1}
                size={size}
              />
            </li>
          )}

          {/* Collapsed items indicator */}
          {collapsedItems.length > 0 && (
            <li className="flex items-center space-x-1">
              <BreadcrumbSeparator>{separator}</BreadcrumbSeparator>
              <CollapsedItems items={collapsedItems} />
            </li>
          )}

          {/* Remaining visible items */}
          {visibleItems.slice(1).map((item, index) => (
            <li key={index + 1} className="flex items-center space-x-1">
              <BreadcrumbSeparator>{separator}</BreadcrumbSeparator>
              <BreadcrumbItemComponent 
                item={item} 
                isLast={index === visibleItems.length - 2}
                size={size}
              />
            </li>
          ))}
        </ol>
      </nav>
    );
  }
);

Breadcrumb.displayName = 'Breadcrumb';

// Kitchen Breadcrumb (Preset for recipe navigation)
export interface KitchenBreadcrumbProps extends Omit<BreadcrumbProps, 'items'> {
  recipeName?: string;
  categoryName?: string;
  subcategoryName?: string;
}

export const KitchenBreadcrumb: React.FC<KitchenBreadcrumbProps> = ({
  recipeName,
  categoryName,
  subcategoryName,
  ...props
}) => {
  const items: BreadcrumbItem[] = [];

  if (categoryName) {
    items.push({
      label: categoryName,
      href: `/recipes/category/${categoryName.toLowerCase()}`,
    });
  }

  if (subcategoryName) {
    items.push({
      label: subcategoryName,
      href: `/recipes/category/${categoryName?.toLowerCase()}/${subcategoryName.toLowerCase()}`,
    });
  }

  if (recipeName) {
    items.push({
      label: recipeName,
      active: true,
    });
  }

  return <Breadcrumb items={items} {...props} />;
};

// Auto Breadcrumb (Generates breadcrumb from current path)
export const AutoBreadcrumb: React.FC<Omit<BreadcrumbProps, 'items'>> = (props) => {
  const pathname = typeof window !== 'undefined' ? window.location.pathname : '';
  
  const items: BreadcrumbItem[] = React.useMemo(() => {
    const segments = pathname.split('/').filter(Boolean);
    
    return segments.map((segment, index) => {
      const href = '/' + segments.slice(0, index + 1).join('/');
      const isLast = index === segments.length - 1;
      
      // Convert segment to readable label
      const label = segment
        .split('-')
        .map(word => word.charAt(0).toUpperCase() + word.slice(1))
        .join(' ');

      return {
        label,
        href: isLast ? undefined : href,
        active: isLast,
      };
    });
  }, [pathname]);

  return <Breadcrumb items={items} {...props} />;
};

export default Breadcrumb;

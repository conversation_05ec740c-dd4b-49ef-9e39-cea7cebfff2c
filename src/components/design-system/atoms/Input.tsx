import React, { forwardRef, useState } from 'react';
import { cva, type VariantProps } from 'class-variance-authority';
import { cn } from '@/lib/utils';
import { Eye, EyeOff, Search, X, AlertCircle, CheckCircle } from 'lucide-react';
import type { 
  InputVariant, 
  BaseComponentProps 
} from '@/types/design-system';

// Input variants using CVA
const inputVariants = cva(
  // Base styles
  [
    'flex w-full rounded-lg border transition-all duration-150',
    'focus:outline-none focus:ring-2 focus:ring-offset-0',
    'disabled:cursor-not-allowed disabled:opacity-50',
    'placeholder:text-gray-400',
  ],
  {
    variants: {
      variant: {
        default: [
          'bg-white border-gray-300 text-gray-900',
          'hover:border-gray-400',
          'focus:border-orange-500 focus:ring-orange-500/20',
          'dark:bg-gray-800 dark:border-gray-600 dark:text-gray-100',
          'dark:hover:border-gray-500 dark:focus:border-orange-400',
        ],
        filled: [
          'bg-gray-100 border-transparent text-gray-900',
          'hover:bg-gray-200',
          'focus:bg-white focus:border-orange-500 focus:ring-orange-500/20',
          'dark:bg-gray-700 dark:text-gray-100',
          'dark:hover:bg-gray-600 dark:focus:bg-gray-800',
        ],
        ghost: [
          'bg-transparent border-transparent text-gray-900',
          'hover:bg-gray-50',
          'focus:bg-white focus:border-orange-500 focus:ring-orange-500/20',
          'dark:text-gray-100 dark:hover:bg-gray-800/50',
        ],
      },
      size: {
        sm: 'h-8 px-3 text-sm',
        md: 'h-10 px-4 text-sm',
        lg: 'h-12 px-4 text-base',
      },
      state: {
        default: '',
        error: [
          'border-red-500 focus:border-red-500 focus:ring-red-500/20',
          'dark:border-red-400 dark:focus:border-red-400',
        ],
        success: [
          'border-green-500 focus:border-green-500 focus:ring-green-500/20',
          'dark:border-green-400 dark:focus:border-green-400',
        ],
        warning: [
          'border-yellow-500 focus:border-yellow-500 focus:ring-yellow-500/20',
          'dark:border-yellow-400 dark:focus:border-yellow-400',
        ],
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'md',
      state: 'default',
    },
  }
);

// Input Props Interface
export interface InputProps extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'size'>, BaseComponentProps {
  variant?: InputVariant | 'ghost';
  size?: 'sm' | 'md' | 'lg';
  state?: 'default' | 'error' | 'success' | 'warning';
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  clearable?: boolean;
  onClear?: () => void;
}

// Base Input Component
export const Input = forwardRef<HTMLInputElement, InputProps>(
  (
    {
      className,
      variant = 'default',
      size = 'md',
      state = 'default',
      leftIcon,
      rightIcon,
      clearable = false,
      onClear,
      value,
      onChange,
      'data-testid': testId,
      ...props
    },
    ref
  ) => {
    const hasLeftIcon = !!leftIcon;
    const hasRightIcon = !!rightIcon || clearable;
    const showClearButton = clearable && value && !props.disabled;

    const handleClear = () => {
      if (onClear) {
        onClear();
      } else if (onChange) {
        onChange({ target: { value: '' } } as React.ChangeEvent<HTMLInputElement>);
      }
    };

    return (
      <div className="relative">
        {/* Left Icon */}
        {hasLeftIcon && (
          <div className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400 pointer-events-none">
            {leftIcon}
          </div>
        )}

        {/* Input */}
        <input
          ref={ref}
          className={cn(
            inputVariants({ variant: variant as any, size, state }),
            hasLeftIcon && 'pl-10',
            hasRightIcon && 'pr-10',
            className
          )}
          value={value}
          onChange={onChange}
          data-testid={testId}
          {...props}
        />

        {/* Right Icon / Clear Button */}
        {hasRightIcon && (
          <div className="absolute right-3 top-1/2 -translate-y-1/2 flex items-center gap-1">
            {showClearButton && (
              <button
                type="button"
                onClick={handleClear}
                className="text-gray-400 hover:text-gray-600 transition-colors p-0.5 rounded"
                tabIndex={-1}
              >
                <X className="w-4 h-4" />
              </button>
            )}
            {rightIcon && !showClearButton && (
              <div className="text-gray-400 pointer-events-none">
                {rightIcon}
              </div>
            )}
          </div>
        )}
      </div>
    );
  }
);

Input.displayName = 'Input';

// Password Input Component
export interface PasswordInputProps extends Omit<InputProps, 'type' | 'rightIcon'> {
  showPasswordToggle?: boolean;
}

export const PasswordInput = forwardRef<HTMLInputElement, PasswordInputProps>(
  ({ showPasswordToggle = true, ...props }, ref) => {
    const [showPassword, setShowPassword] = useState(false);

    const togglePasswordVisibility = () => {
      setShowPassword(!showPassword);
    };

    return (
      <Input
        ref={ref}
        type={showPassword ? 'text' : 'password'}
        rightIcon={
          showPasswordToggle ? (
            <button
              type="button"
              onClick={togglePasswordVisibility}
              className="text-gray-400 hover:text-gray-600 transition-colors p-0.5 rounded focus:outline-none focus:ring-2 focus:ring-orange-500/20"
              tabIndex={-1}
            >
              {showPassword ? (
                <EyeOff className="w-4 h-4" />
              ) : (
                <Eye className="w-4 h-4" />
              )}
            </button>
          ) : undefined
        }
        {...props}
      />
    );
  }
);

PasswordInput.displayName = 'PasswordInput';

// Search Input Component
export interface SearchInputProps extends Omit<InputProps, 'leftIcon' | 'type'> {
  onSearch?: (value: string) => void;
  searchOnEnter?: boolean;
}

export const SearchInput = forwardRef<HTMLInputElement, SearchInputProps>(
  ({ onSearch, searchOnEnter = true, onKeyDown, ...props }, ref) => {
    const handleKeyDown = (event: React.KeyboardEvent<HTMLInputElement>) => {
      if (searchOnEnter && event.key === 'Enter' && onSearch) {
        onSearch(event.currentTarget.value);
      }
      onKeyDown?.(event);
    };

    return (
      <Input
        ref={ref}
        type="search"
        leftIcon={<Search className="w-4 h-4" />}
        onKeyDown={handleKeyDown}
        clearable
        {...props}
      />
    );
  }
);

SearchInput.displayName = 'SearchInput';

// Textarea Component
export interface TextareaProps extends Omit<React.TextareaHTMLAttributes<HTMLTextAreaElement>, 'size'>, BaseComponentProps {
  variant?: InputVariant | 'ghost';
  size?: 'sm' | 'md' | 'lg';
  state?: 'default' | 'error' | 'success' | 'warning';
  resize?: 'none' | 'vertical' | 'horizontal' | 'both';
}

export const Textarea = forwardRef<HTMLTextAreaElement, TextareaProps>(
  (
    {
      className,
      variant = 'default',
      size = 'md',
      state = 'default',
      resize = 'vertical',
      'data-testid': testId,
      ...props
    },
    ref
  ) => {
    const resizeClasses = {
      none: 'resize-none',
      vertical: 'resize-y',
      horizontal: 'resize-x',
      both: 'resize',
    };

    const sizeClasses = {
      sm: 'min-h-[80px] px-3 py-2 text-sm',
      md: 'min-h-[100px] px-4 py-3 text-sm',
      lg: 'min-h-[120px] px-4 py-3 text-base',
    };

    return (
      <textarea
        ref={ref}
        className={cn(
          inputVariants({ variant: variant as any, size: 'md', state }),
          sizeClasses[size],
          resizeClasses[resize],
          'h-auto',
          className
        )}
        data-testid={testId}
        {...props}
      />
    );
  }
);

Textarea.displayName = 'Textarea';

// Form Field Component (Wrapper with Label and Error)
export interface FormFieldProps extends BaseComponentProps {
  label?: string;
  description?: string;
  error?: string;
  success?: string;
  warning?: string;
  required?: boolean;
  htmlFor?: string;
}

export const FormField: React.FC<FormFieldProps> = ({
  label,
  description,
  error,
  success,
  warning,
  required = false,
  htmlFor,
  children,
  className,
  'data-testid': testId,
}) => {
  const hasError = !!error;
  const hasSuccess = !!success;
  const hasWarning = !!warning;

  // Clone children to pass state prop
  const childrenWithState = React.Children.map(children, (child) => {
    if (React.isValidElement(child) && (child.type === Input || child.type === PasswordInput || child.type === SearchInput || child.type === Textarea)) {
      return React.cloneElement(child, {
        state: hasError ? 'error' : hasSuccess ? 'success' : hasWarning ? 'warning' : 'default',
      });
    }
    return child;
  });

  return (
    <div className={cn('space-y-2', className)} data-testid={testId}>
      {/* Label */}
      {label && (
        <label
          htmlFor={htmlFor}
          className="block text-sm font-medium text-gray-700 dark:text-gray-300"
        >
          {label}
          {required && <span className="text-red-500 ml-1">*</span>}
        </label>
      )}

      {/* Description */}
      {description && (
        <p className="text-sm text-gray-600 dark:text-gray-400">
          {description}
        </p>
      )}

      {/* Input */}
      {childrenWithState}

      {/* Error Message */}
      {error && (
        <div className="flex items-center gap-2 text-sm text-red-600 dark:text-red-400">
          <AlertCircle className="w-4 h-4 flex-shrink-0" />
          <span>{error}</span>
        </div>
      )}

      {/* Success Message */}
      {success && (
        <div className="flex items-center gap-2 text-sm text-green-600 dark:text-green-400">
          <CheckCircle className="w-4 h-4 flex-shrink-0" />
          <span>{success}</span>
        </div>
      )}

      {/* Warning Message */}
      {warning && (
        <div className="flex items-center gap-2 text-sm text-yellow-600 dark:text-yellow-400">
          <AlertCircle className="w-4 h-4 flex-shrink-0" />
          <span>{warning}</span>
        </div>
      )}
    </div>
  );
};

FormField.displayName = 'FormField';

export default Input;

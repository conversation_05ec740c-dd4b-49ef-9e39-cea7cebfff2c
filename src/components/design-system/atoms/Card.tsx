import React, { forwardRef } from 'react';
import { Slot } from '@radix-ui/react-slot';
import { cva, type VariantProps } from 'class-variance-authority';
import { cn } from '@/lib/utils';
import { OptimizedImage } from '@/components/ui/optimized-image';
import type {
  CardVariant,
  PolymorphicComponentPropWithRef,
  BaseComponentProps
} from '@/types/design-system';

// Card variants using CVA
const cardVariants = cva(
  // Base styles
  [
    'rounded-xl',
    'transition-all duration-200 ease-out',
    'overflow-hidden',
  ],
  {
    variants: {
      variant: {
        default: [
          'bg-white border border-gray-200 shadow-sm',
          'hover:shadow-md hover:-translate-y-1',
          'dark:bg-gray-800 dark:border-gray-700',
        ],
        elevated: [
          'bg-white shadow-lg',
          'hover:shadow-xl hover:-translate-y-2',
          'dark:bg-gray-800',
        ],
        outlined: [
          'bg-white border-2 border-gray-200',
          'hover:border-orange-300 hover:shadow-sm',
          'dark:bg-gray-800 dark:border-gray-600 dark:hover:border-orange-400',
        ],
        ghost: [
          'bg-transparent',
          'hover:bg-gray-50',
          'dark:hover:bg-gray-800/50',
        ],
        gradient: [
          'bg-gradient-to-br from-orange-50 to-red-50 border border-orange-100',
          'hover:from-orange-100 hover:to-red-100 hover:shadow-md',
          'dark:from-gray-800 dark:to-gray-900 dark:border-gray-700',
        ],
      },
      padding: {
        none: 'p-0',
        sm: 'p-3',
        md: 'p-4',
        lg: 'p-6',
        xl: 'p-8',
      },
      interactive: {
        true: 'cursor-pointer focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-orange-500/20',
        false: '',
      },
    },
    defaultVariants: {
      variant: 'default',
      padding: 'md',
      interactive: false,
    },
  }
);

// Card Props Interface
export interface CardProps extends BaseComponentProps {
  variant?: CardVariant | 'gradient';
  padding?: 'none' | 'sm' | 'md' | 'lg' | 'xl';
  interactive?: boolean;
  asChild?: boolean;
  onClick?: (event: React.MouseEvent<HTMLDivElement>) => void;
  onKeyDown?: (event: React.KeyboardEvent<HTMLDivElement>) => void;
}

// Main Card Component
export const Card = forwardRef<HTMLDivElement, CardProps>(
  (
    {
      className,
      variant = 'default',
      padding = 'md',
      interactive = false,
      asChild = false,
      children,
      onClick,
      onKeyDown,
      'data-testid': testId,
      ...props
    },
    ref
  ) => {
    const Comp = asChild ? Slot : 'div';

    const handleKeyDown = (event: React.KeyboardEvent<HTMLDivElement>) => {
      if (interactive && (event.key === 'Enter' || event.key === ' ')) {
        event.preventDefault();
        onClick?.(event as any);
      }
      onKeyDown?.(event);
    };

    return (
      <Comp
        ref={ref}
        className={cn(
          cardVariants({ variant: variant as any, padding, interactive }),
          className
        )}
        onClick={onClick}
        onKeyDown={handleKeyDown}
        tabIndex={interactive ? 0 : undefined}
        role={interactive ? 'button' : undefined}
        data-testid={testId}
        {...props}
      >
        {children}
      </Comp>
    );
  }
);

Card.displayName = 'Card';

// Card Header Component
export interface CardHeaderProps extends BaseComponentProps {
  padding?: 'none' | 'sm' | 'md' | 'lg';
}

export const CardHeader = forwardRef<HTMLDivElement, CardHeaderProps>(
  ({ className, padding = 'md', children, 'data-testid': testId, ...props }, ref) => {
    const paddingClasses = {
      none: 'p-0',
      sm: 'p-3 pb-2',
      md: 'p-4 pb-3',
      lg: 'p-6 pb-4',
    };

    return (
      <div
        ref={ref}
        className={cn(
          'flex flex-col space-y-1.5',
          paddingClasses[padding],
          className
        )}
        data-testid={testId}
        {...props}
      >
        {children}
      </div>
    );
  }
);

CardHeader.displayName = 'CardHeader';

// Card Title Component
export interface CardTitleProps extends BaseComponentProps {
  as?: 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6';
  size?: 'sm' | 'md' | 'lg' | 'xl';
}

export const CardTitle = forwardRef<HTMLHeadingElement, CardTitleProps>(
  ({ 
    className, 
    as: Comp = 'h3', 
    size = 'md',
    children, 
    'data-testid': testId, 
    ...props 
  }, ref) => {
    const sizeClasses = {
      sm: 'text-lg font-semibold',
      md: 'text-xl font-semibold',
      lg: 'text-2xl font-bold',
      xl: 'text-3xl font-bold',
    };

    return (
      <Comp
        ref={ref}
        className={cn(
          'leading-none tracking-tight text-gray-900 dark:text-gray-100',
          sizeClasses[size],
          className
        )}
        data-testid={testId}
        {...props}
      >
        {children}
      </Comp>
    );
  }
);

CardTitle.displayName = 'CardTitle';

// Card Description Component
export interface CardDescriptionProps extends BaseComponentProps {
  size?: 'sm' | 'md' | 'lg';
}

export const CardDescription = forwardRef<HTMLParagraphElement, CardDescriptionProps>(
  ({ className, size = 'md', children, 'data-testid': testId, ...props }, ref) => {
    const sizeClasses = {
      sm: 'text-sm',
      md: 'text-base',
      lg: 'text-lg',
    };

    return (
      <p
        ref={ref}
        className={cn(
          'text-gray-600 dark:text-gray-400 leading-relaxed',
          sizeClasses[size],
          className
        )}
        data-testid={testId}
        {...props}
      >
        {children}
      </p>
    );
  }
);

CardDescription.displayName = 'CardDescription';

// Card Content Component
export interface CardContentProps extends BaseComponentProps {
  padding?: 'none' | 'sm' | 'md' | 'lg';
}

export const CardContent = forwardRef<HTMLDivElement, CardContentProps>(
  ({ className, padding = 'md', children, 'data-testid': testId, ...props }, ref) => {
    const paddingClasses = {
      none: 'p-0',
      sm: 'p-3 pt-0',
      md: 'p-4 pt-0',
      lg: 'p-6 pt-0',
    };

    return (
      <div
        ref={ref}
        className={cn(paddingClasses[padding], className)}
        data-testid={testId}
        {...props}
      >
        {children}
      </div>
    );
  }
);

CardContent.displayName = 'CardContent';

// Card Footer Component
export interface CardFooterProps extends BaseComponentProps {
  padding?: 'none' | 'sm' | 'md' | 'lg';
  justify?: 'start' | 'center' | 'end' | 'between';
}

export const CardFooter = forwardRef<HTMLDivElement, CardFooterProps>(
  ({ 
    className, 
    padding = 'md', 
    justify = 'start',
    children, 
    'data-testid': testId, 
    ...props 
  }, ref) => {
    const paddingClasses = {
      none: 'p-0',
      sm: 'p-3 pt-0',
      md: 'p-4 pt-0',
      lg: 'p-6 pt-0',
    };

    const justifyClasses = {
      start: 'justify-start',
      center: 'justify-center',
      end: 'justify-end',
      between: 'justify-between',
    };

    return (
      <div
        ref={ref}
        className={cn(
          'flex items-center',
          paddingClasses[padding],
          justifyClasses[justify],
          className
        )}
        data-testid={testId}
        {...props}
      >
        {children}
      </div>
    );
  }
);

CardFooter.displayName = 'CardFooter';

// Card Image Component
export interface CardImageProps extends BaseComponentProps {
  src: string;
  alt: string;
  aspectRatio?: 'square' | 'video' | 'wide' | 'auto';
  objectFit?: 'cover' | 'contain' | 'fill';
  loading?: 'lazy' | 'eager';
  priority?: boolean;
  placeholder?: 'blur' | 'empty';
  fallbackSrc?: string;
  onLoadingComplete?: () => void;
  onError?: () => void;
}

export const CardImage = forwardRef<HTMLImageElement, CardImageProps>(
  ({
    className,
    src,
    alt,
    aspectRatio = 'auto',
    objectFit = 'cover',
    loading = 'lazy',
    priority = false,
    placeholder = 'empty',
    fallbackSrc,
    onLoadingComplete,
    onError,
    'data-testid': testId,
    ...props
  }, ref) => {
    return (
      <OptimizedImage
        ref={ref}
        src={src}
        alt={alt}
        aspectRatio={aspectRatio}
        objectFit={objectFit}
        priority={priority || loading === 'eager'}
        placeholder={placeholder}
        fallbackSrc={fallbackSrc}
        onLoadingComplete={onLoadingComplete}
        onError={onError}
        className={className}
        data-testid={testId}
        {...props}
      />
    );
  }
);

CardImage.displayName = 'CardImage';

// Export all components
export {
  Card as default,
  cardVariants,
};

// Compound component pattern
Card.Header = CardHeader;
Card.Title = CardTitle;
Card.Description = CardDescription;
Card.Content = CardContent;
Card.Footer = CardFooter;
Card.Image = CardImage;

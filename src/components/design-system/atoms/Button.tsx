import React, { forwardRef } from 'react';
import { Slot } from '@radix-ui/react-slot';
import { cva, type VariantProps } from 'class-variance-authority';
import { cn } from '@/lib/utils';
import { designTokens } from '@/lib/design-system';
import type { 
  ButtonVariant, 
  ButtonSize, 
  PolymorphicComponentPropWithRef,
  BaseComponentProps 
} from '@/types/design-system';

// Button variants using CVA (Class Variance Authority)
const buttonVariants = cva(
  // Base styles
  [
    'inline-flex items-center justify-center gap-2',
    'font-medium text-sm',
    'transition-all duration-150 ease-out',
    'focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2',
    'disabled:pointer-events-none disabled:opacity-50',
    'relative overflow-hidden',
    'select-none',
  ],
  {
    variants: {
      variant: {
        primary: [
          'bg-orange-500 text-white',
          'hover:bg-orange-600 hover:-translate-y-0.5 hover:shadow-lg',
          'active:bg-orange-700 active:translate-y-0',
          'focus-visible:ring-orange-500/20',
          'disabled:bg-gray-300 disabled:text-gray-500',
        ],
        secondary: [
          'bg-gray-100 text-gray-900 border border-gray-300',
          'hover:bg-gray-200 hover:border-gray-400',
          'active:bg-gray-300',
          'focus-visible:ring-gray-500/20',
          'disabled:bg-gray-100 disabled:text-gray-400 disabled:border-gray-200',
        ],
        ghost: [
          'bg-transparent text-gray-700',
          'hover:bg-gray-100',
          'active:bg-gray-200',
          'focus-visible:ring-gray-500/20',
          'disabled:text-gray-400',
        ],
        danger: [
          'bg-red-500 text-white',
          'hover:bg-red-600 hover:-translate-y-0.5 hover:shadow-lg',
          'active:bg-red-700 active:translate-y-0',
          'focus-visible:ring-red-500/20',
          'disabled:bg-gray-300 disabled:text-gray-500',
        ],
        outline: [
          'bg-transparent border-2 border-orange-500 text-orange-600',
          'hover:bg-orange-50 hover:border-orange-600',
          'active:bg-orange-100',
          'focus-visible:ring-orange-500/20',
          'disabled:border-gray-300 disabled:text-gray-400',
        ],
        link: [
          'bg-transparent text-orange-600 underline-offset-4',
          'hover:underline',
          'focus-visible:ring-orange-500/20',
          'disabled:text-gray-400',
        ],
      },
      size: {
        xs: 'h-7 px-2 text-xs rounded-md',
        sm: 'h-8 px-3 text-sm rounded-md',
        md: 'h-10 px-4 text-sm rounded-lg',
        lg: 'h-11 px-6 text-base rounded-lg',
        xl: 'h-12 px-8 text-lg rounded-xl',
      },
      fullWidth: {
        true: 'w-full',
        false: 'w-auto',
      },
      loading: {
        true: 'cursor-wait',
        false: '',
      },
    },
    defaultVariants: {
      variant: 'primary',
      size: 'md',
      fullWidth: false,
      loading: false,
    },
  }
);

// Loading spinner component
const LoadingSpinner: React.FC<{ size: ButtonSize }> = ({ size }) => {
  const spinnerSizes = {
    xs: 'w-3 h-3',
    sm: 'w-3 h-3',
    md: 'w-4 h-4',
    lg: 'w-5 h-5',
    xl: 'w-6 h-6',
  };

  return (
    <svg
      className={cn('animate-spin', spinnerSizes[size])}
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
    >
      <circle
        className="opacity-25"
        cx="12"
        cy="12"
        r="10"
        stroke="currentColor"
        strokeWidth="4"
      />
      <path
        className="opacity-75"
        fill="currentColor"
        d="m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
      />
    </svg>
  );
};

// Button Props Interface
export interface ButtonProps extends BaseComponentProps {
  variant?: ButtonVariant | 'outline' | 'link';
  size?: ButtonSize;
  fullWidth?: boolean;
  loading?: boolean;
  disabled?: boolean;
  asChild?: boolean;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  loadingText?: string;
  onClick?: (event: React.MouseEvent<HTMLButtonElement>) => void;
  type?: 'button' | 'submit' | 'reset';
}

// Polymorphic Button Component
type ButtonComponent = <C extends React.ElementType = 'button'>(
  props: PolymorphicComponentPropWithRef<C, ButtonProps>
) => React.ReactElement | null;

export const Button: ButtonComponent = forwardRef<
  HTMLButtonElement,
  ButtonProps
>(
  (
    {
      className,
      variant = 'primary',
      size = 'md',
      fullWidth = false,
      loading = false,
      disabled = false,
      asChild = false,
      leftIcon,
      rightIcon,
      loadingText,
      children,
      type = 'button',
      onClick,
      'data-testid': testId,
      ...props
    },
    ref
  ) => {
    const Comp = asChild ? Slot : 'button';
    const isDisabled = disabled || loading;

    const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
      if (loading || disabled) {
        event.preventDefault();
        return;
      }
      onClick?.(event);
    };

    return (
      <Comp
        className={cn(
          buttonVariants({ 
            variant: variant as any, 
            size, 
            fullWidth, 
            loading 
          }),
          className
        )}
        ref={ref}
        disabled={isDisabled}
        type={type}
        onClick={handleClick}
        data-testid={testId}
        aria-disabled={isDisabled}
        {...props}
      >
        {/* Left Icon */}
        {leftIcon && !loading && (
          <span className="flex-shrink-0" aria-hidden="true">
            {leftIcon}
          </span>
        )}

        {/* Loading Spinner */}
        {loading && (
          <LoadingSpinner size={size} />
        )}

        {/* Button Content */}
        <span className={cn(
          'flex items-center gap-2',
          loading && 'opacity-70'
        )}>
          {loading && loadingText ? loadingText : children}
        </span>

        {/* Right Icon */}
        {rightIcon && !loading && (
          <span className="flex-shrink-0" aria-hidden="true">
            {rightIcon}
          </span>
        )}

        {/* Ripple Effect (Optional Enhancement) */}
        <span 
          className="absolute inset-0 overflow-hidden rounded-[inherit]"
          aria-hidden="true"
        >
          <span className="absolute inset-0 rounded-[inherit] opacity-0 transition-opacity duration-300 hover:opacity-10 bg-white" />
        </span>
      </Comp>
    );
  }
);

Button.displayName = 'Button';

// Button Group Component
export interface ButtonGroupProps extends BaseComponentProps {
  variant?: ButtonVariant;
  size?: ButtonSize;
  orientation?: 'horizontal' | 'vertical';
  attached?: boolean;
}

export const ButtonGroup: React.FC<ButtonGroupProps> = ({
  children,
  className,
  variant = 'primary',
  size = 'md',
  orientation = 'horizontal',
  attached = false,
  'data-testid': testId,
}) => {
  return (
    <div
      className={cn(
        'flex',
        orientation === 'horizontal' ? 'flex-row' : 'flex-col',
        attached && orientation === 'horizontal' && [
          '[&>*:not(:first-child)]:rounded-l-none',
          '[&>*:not(:last-child)]:rounded-r-none',
          '[&>*:not(:first-child)]:border-l-0',
        ],
        attached && orientation === 'vertical' && [
          '[&>*:not(:first-child)]:rounded-t-none',
          '[&>*:not(:last-child)]:rounded-b-none',
          '[&>*:not(:first-child)]:border-t-0',
        ],
        !attached && 'gap-2',
        className
      )}
      role="group"
      data-testid={testId}
    >
      {React.Children.map(children, (child) => {
        if (React.isValidElement(child) && child.type === Button) {
          return React.cloneElement(child, {
            variant: child.props.variant || variant,
            size: child.props.size || size,
          });
        }
        return child;
      })}
    </div>
  );
};

ButtonGroup.displayName = 'ButtonGroup';

// Icon Button Component
export interface IconButtonProps extends Omit<ButtonProps, 'leftIcon' | 'rightIcon'> {
  icon: React.ReactNode;
  'aria-label': string;
}

export const IconButton: React.FC<IconButtonProps> = forwardRef<
  HTMLButtonElement,
  IconButtonProps
>(
  (
    {
      icon,
      size = 'md',
      variant = 'ghost',
      className,
      'aria-label': ariaLabel,
      ...props
    },
    ref
  ) => {
    const iconSizes = {
      xs: 'w-3 h-3',
      sm: 'w-4 h-4',
      md: 'w-5 h-5',
      lg: 'w-6 h-6',
      xl: 'w-7 h-7',
    };

    return (
      <Button
        ref={ref}
        variant={variant}
        size={size}
        className={cn(
          'aspect-square p-0',
          size === 'xs' && 'w-7 h-7',
          size === 'sm' && 'w-8 h-8',
          size === 'md' && 'w-10 h-10',
          size === 'lg' && 'w-11 h-11',
          size === 'xl' && 'w-12 h-12',
          className
        )}
        aria-label={ariaLabel}
        {...props}
      >
        <span className={iconSizes[size]} aria-hidden="true">
          {icon}
        </span>
      </Button>
    );
  }
);

IconButton.displayName = 'IconButton';

export default Button;

import React, { forwardRef } from 'react';
import { Slot } from '@radix-ui/react-slot';
import { cva, type VariantProps } from 'class-variance-authority';
import { cn } from '@/lib/utils';
import type { 
  PolymorphicComponentPropWithRef,
  BaseComponentProps 
} from '@/types/design-system';

// Typography variants using CVA
const typographyVariants = cva(
  // Base styles
  'text-gray-900 dark:text-gray-100',
  {
    variants: {
      variant: {
        h1: 'text-4xl md:text-5xl lg:text-6xl font-bold tracking-tight',
        h2: 'text-3xl md:text-4xl lg:text-5xl font-bold tracking-tight',
        h3: 'text-2xl md:text-3xl lg:text-4xl font-bold tracking-tight',
        h4: 'text-xl md:text-2xl lg:text-3xl font-semibold tracking-tight',
        h5: 'text-lg md:text-xl lg:text-2xl font-semibold tracking-tight',
        h6: 'text-base md:text-lg lg:text-xl font-semibold tracking-tight',
        subtitle1: 'text-lg font-medium',
        subtitle2: 'text-base font-medium',
        body1: 'text-base leading-relaxed',
        body2: 'text-sm leading-relaxed',
        caption: 'text-xs font-medium uppercase tracking-wide',
        overline: 'text-xs font-bold uppercase tracking-widest',
        code: 'text-sm font-mono bg-gray-100 dark:bg-gray-800 px-1.5 py-0.5 rounded',
        kbd: 'text-xs font-mono bg-gray-100 dark:bg-gray-800 border border-gray-300 dark:border-gray-600 px-1.5 py-0.5 rounded shadow-sm',
      },
      color: {
        default: 'text-gray-900 dark:text-gray-100',
        muted: 'text-gray-600 dark:text-gray-400',
        subtle: 'text-gray-500 dark:text-gray-500',
        primary: 'text-orange-600 dark:text-orange-400',
        secondary: 'text-gray-700 dark:text-gray-300',
        success: 'text-green-600 dark:text-green-400',
        warning: 'text-yellow-600 dark:text-yellow-400',
        danger: 'text-red-600 dark:text-red-400',
        white: 'text-white',
        black: 'text-black',
      },
      align: {
        left: 'text-left',
        center: 'text-center',
        right: 'text-right',
        justify: 'text-justify',
      },
      weight: {
        thin: 'font-thin',
        extralight: 'font-extralight',
        light: 'font-light',
        normal: 'font-normal',
        medium: 'font-medium',
        semibold: 'font-semibold',
        bold: 'font-bold',
        extrabold: 'font-extrabold',
        black: 'font-black',
      },
      transform: {
        none: 'normal-case',
        uppercase: 'uppercase',
        lowercase: 'lowercase',
        capitalize: 'capitalize',
      },
      decoration: {
        none: 'no-underline',
        underline: 'underline',
        overline: 'overline',
        'line-through': 'line-through',
      },
      truncate: {
        none: '',
        truncate: 'truncate',
        'line-clamp-1': 'line-clamp-1',
        'line-clamp-2': 'line-clamp-2',
        'line-clamp-3': 'line-clamp-3',
        'line-clamp-4': 'line-clamp-4',
      },
    },
    defaultVariants: {
      variant: 'body1',
      color: 'default',
      align: 'left',
      weight: 'normal',
      transform: 'none',
      decoration: 'none',
      truncate: 'none',
    },
  }
);

// Typography Props Interface
export interface TypographyProps extends BaseComponentProps {
  variant?: 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6' | 'subtitle1' | 'subtitle2' | 'body1' | 'body2' | 'caption' | 'overline' | 'code' | 'kbd';
  color?: 'default' | 'muted' | 'subtle' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger' | 'white' | 'black';
  align?: 'left' | 'center' | 'right' | 'justify';
  weight?: 'thin' | 'extralight' | 'light' | 'normal' | 'medium' | 'semibold' | 'bold' | 'extrabold' | 'black';
  transform?: 'none' | 'uppercase' | 'lowercase' | 'capitalize';
  decoration?: 'none' | 'underline' | 'overline' | 'line-through';
  truncate?: 'none' | 'truncate' | 'line-clamp-1' | 'line-clamp-2' | 'line-clamp-3' | 'line-clamp-4';
  asChild?: boolean;
  as?: React.ElementType;
}

// Default element mapping for variants
const defaultElements = {
  h1: 'h1',
  h2: 'h2',
  h3: 'h3',
  h4: 'h4',
  h5: 'h5',
  h6: 'h6',
  subtitle1: 'p',
  subtitle2: 'p',
  body1: 'p',
  body2: 'p',
  caption: 'span',
  overline: 'span',
  code: 'code',
  kbd: 'kbd',
} as const;

// Polymorphic Typography Component
type TypographyComponent = <C extends React.ElementType = 'p'>(
  props: PolymorphicComponentPropWithRef<C, TypographyProps>
) => React.ReactElement | null;

export const Typography: TypographyComponent = forwardRef<
  HTMLElement,
  TypographyProps
>(
  (
    {
      className,
      variant = 'body1',
      color = 'default',
      align = 'left',
      weight = 'normal',
      transform = 'none',
      decoration = 'none',
      truncate = 'none',
      asChild = false,
      as,
      children,
      'data-testid': testId,
      ...props
    },
    ref
  ) => {
    const Comp = asChild ? Slot : (as || defaultElements[variant] || 'p');

    return (
      <Comp
        className={cn(
          typographyVariants({ 
            variant, 
            color, 
            align, 
            weight, 
            transform, 
            decoration, 
            truncate 
          }),
          className
        )}
        ref={ref}
        data-testid={testId}
        {...props}
      >
        {children}
      </Comp>
    );
  }
);

Typography.displayName = 'Typography';

// Heading Components (Convenience components)
export const Heading1 = forwardRef<HTMLHeadingElement, Omit<TypographyProps, 'variant'>>(
  (props, ref) => <Typography ref={ref} variant="h1" {...props} />
);
Heading1.displayName = 'Heading1';

export const Heading2 = forwardRef<HTMLHeadingElement, Omit<TypographyProps, 'variant'>>(
  (props, ref) => <Typography ref={ref} variant="h2" {...props} />
);
Heading2.displayName = 'Heading2';

export const Heading3 = forwardRef<HTMLHeadingElement, Omit<TypographyProps, 'variant'>>(
  (props, ref) => <Typography ref={ref} variant="h3" {...props} />
);
Heading3.displayName = 'Heading3';

export const Heading4 = forwardRef<HTMLHeadingElement, Omit<TypographyProps, 'variant'>>(
  (props, ref) => <Typography ref={ref} variant="h4" {...props} />
);
Heading4.displayName = 'Heading4';

export const Heading5 = forwardRef<HTMLHeadingElement, Omit<TypographyProps, 'variant'>>(
  (props, ref) => <Typography ref={ref} variant="h5" {...props} />
);
Heading5.displayName = 'Heading5';

export const Heading6 = forwardRef<HTMLHeadingElement, Omit<TypographyProps, 'variant'>>(
  (props, ref) => <Typography ref={ref} variant="h6" {...props} />
);
Heading6.displayName = 'Heading6';

// Text Components
export const Text = forwardRef<HTMLParagraphElement, Omit<TypographyProps, 'variant'>>(
  (props, ref) => <Typography ref={ref} variant="body1" {...props} />
);
Text.displayName = 'Text';

export const SmallText = forwardRef<HTMLParagraphElement, Omit<TypographyProps, 'variant'>>(
  (props, ref) => <Typography ref={ref} variant="body2" {...props} />
);
SmallText.displayName = 'SmallText';

export const Caption = forwardRef<HTMLSpanElement, Omit<TypographyProps, 'variant'>>(
  (props, ref) => <Typography ref={ref} variant="caption" {...props} />
);
Caption.displayName = 'Caption';

export const Code = forwardRef<HTMLElement, Omit<TypographyProps, 'variant'>>(
  (props, ref) => <Typography ref={ref} variant="code" {...props} />
);
Code.displayName = 'Code';

export const Kbd = forwardRef<HTMLElement, Omit<TypographyProps, 'variant'>>(
  (props, ref) => <Typography ref={ref} variant="kbd" {...props} />
);
Kbd.displayName = 'Kbd';

// Link Component
export interface LinkProps extends Omit<TypographyProps, 'variant' | 'as'> {
  href?: string;
  external?: boolean;
  underline?: 'none' | 'hover' | 'always';
}

export const Link = forwardRef<HTMLAnchorElement, LinkProps>(
  (
    {
      href,
      external = false,
      underline = 'hover',
      color = 'primary',
      className,
      children,
      ...props
    },
    ref
  ) => {
    const underlineClasses = {
      none: 'no-underline',
      hover: 'no-underline hover:underline',
      always: 'underline',
    };

    return (
      <Typography
        ref={ref}
        as="a"
        href={href}
        target={external ? '_blank' : undefined}
        rel={external ? 'noopener noreferrer' : undefined}
        color={color}
        className={cn(
          'transition-colors hover:opacity-80 focus:outline-none focus:ring-2 focus:ring-orange-500/20 rounded',
          underlineClasses[underline],
          className
        )}
        {...props}
      >
        {children}
      </Typography>
    );
  }
);

Link.displayName = 'Link';

// Blockquote Component
export interface BlockquoteProps extends Omit<TypographyProps, 'variant' | 'as'> {
  cite?: string;
  author?: string;
}

export const Blockquote = forwardRef<HTMLQuoteElement, BlockquoteProps>(
  ({ cite, author, className, children, ...props }, ref) => {
    return (
      <blockquote
        ref={ref}
        cite={cite}
        className={cn(
          'border-l-4 border-orange-500 pl-4 italic text-gray-700 dark:text-gray-300',
          className
        )}
        {...props}
      >
        <Typography variant="body1" className="mb-2">
          {children}
        </Typography>
        {author && (
          <Typography variant="body2" color="muted" className="not-italic">
            — {author}
          </Typography>
        )}
      </blockquote>
    );
  }
);

Blockquote.displayName = 'Blockquote';

export default Typography;

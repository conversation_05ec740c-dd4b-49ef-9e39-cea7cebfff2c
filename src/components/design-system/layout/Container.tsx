import React, { forwardRef } from 'react';
import { Slot } from '@radix-ui/react-slot';
import { cva, type VariantProps } from 'class-variance-authority';
import { cn } from '@/lib/utils';
import type { 
  PolymorphicComponentPropWithRef,
  BaseComponentProps 
} from '@/types/design-system';

// Container variants using CVA
const containerVariants = cva(
  // Base styles
  'w-full mx-auto px-4 sm:px-6 lg:px-8',
  {
    variants: {
      size: {
        sm: 'max-w-2xl',
        md: 'max-w-4xl',
        lg: 'max-w-6xl',
        xl: 'max-w-7xl',
        '2xl': 'max-w-screen-2xl',
        full: 'max-w-none',
        prose: 'max-w-prose',
      },
      padding: {
        none: 'px-0',
        sm: 'px-4 sm:px-6',
        md: 'px-4 sm:px-6 lg:px-8',
        lg: 'px-6 sm:px-8 lg:px-12',
        xl: 'px-8 sm:px-12 lg:px-16',
      },
      center: {
        true: 'mx-auto',
        false: '',
      },
    },
    defaultVariants: {
      size: 'xl',
      padding: 'md',
      center: true,
    },
  }
);

// Container Props Interface
export interface ContainerProps extends BaseComponentProps {
  size?: 'sm' | 'md' | 'lg' | 'xl' | '2xl' | 'full' | 'prose';
  padding?: 'none' | 'sm' | 'md' | 'lg' | 'xl';
  center?: boolean;
  asChild?: boolean;
  as?: React.ElementType;
}

// Polymorphic Container Component
type ContainerComponent = <C extends React.ElementType = 'div'>(
  props: PolymorphicComponentPropWithRef<C, ContainerProps>
) => React.ReactElement | null;

export const Container: ContainerComponent = forwardRef<
  HTMLDivElement,
  ContainerProps
>(
  (
    {
      className,
      size = 'xl',
      padding = 'md',
      center = true,
      asChild = false,
      as,
      children,
      'data-testid': testId,
      ...props
    },
    ref
  ) => {
    const Comp = asChild ? Slot : (as || 'div');

    return (
      <Comp
        className={cn(
          containerVariants({ size, padding, center }),
          className
        )}
        ref={ref}
        data-testid={testId}
        {...props}
      >
        {children}
      </Comp>
    );
  }
);

Container.displayName = 'Container';

// Section Component (Container with semantic meaning)
export interface SectionProps extends ContainerProps {
  variant?: 'default' | 'hero' | 'feature' | 'content';
  spacing?: 'none' | 'sm' | 'md' | 'lg' | 'xl';
}

const sectionVariants = cva(
  '',
  {
    variants: {
      variant: {
        default: '',
        hero: 'relative overflow-hidden',
        feature: 'relative',
        content: '',
      },
      spacing: {
        none: 'py-0',
        sm: 'py-8 sm:py-12',
        md: 'py-12 sm:py-16',
        lg: 'py-16 sm:py-20',
        xl: 'py-20 sm:py-24',
      },
    },
    defaultVariants: {
      variant: 'default',
      spacing: 'md',
    },
  }
);

export const Section = forwardRef<HTMLElement, SectionProps>(
  (
    {
      className,
      variant = 'default',
      spacing = 'md',
      as = 'section',
      ...props
    },
    ref
  ) => {
    return (
      <Container
        ref={ref}
        as={as}
        className={cn(
          sectionVariants({ variant, spacing }),
          className
        )}
        {...props}
      />
    );
  }
);

Section.displayName = 'Section';

// Grid Container Component
export interface GridProps extends BaseComponentProps {
  cols?: 1 | 2 | 3 | 4 | 5 | 6 | 12;
  gap?: 'none' | 'sm' | 'md' | 'lg' | 'xl';
  responsive?: boolean;
  asChild?: boolean;
}

const gridVariants = cva(
  'grid',
  {
    variants: {
      cols: {
        1: 'grid-cols-1',
        2: 'grid-cols-1 md:grid-cols-2',
        3: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',
        4: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4',
        5: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5',
        6: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6',
        12: 'grid-cols-12',
      },
      gap: {
        none: 'gap-0',
        sm: 'gap-2 sm:gap-3',
        md: 'gap-4 sm:gap-6',
        lg: 'gap-6 sm:gap-8',
        xl: 'gap-8 sm:gap-10',
      },
      responsive: {
        true: '',
        false: '',
      },
    },
    defaultVariants: {
      cols: 1,
      gap: 'md',
      responsive: true,
    },
  }
);

export const Grid = forwardRef<HTMLDivElement, GridProps>(
  (
    {
      className,
      cols = 1,
      gap = 'md',
      responsive = true,
      asChild = false,
      children,
      'data-testid': testId,
      ...props
    },
    ref
  ) => {
    const Comp = asChild ? Slot : 'div';

    return (
      <Comp
        className={cn(
          gridVariants({ cols, gap, responsive }),
          className
        )}
        ref={ref}
        data-testid={testId}
        {...props}
      >
        {children}
      </Comp>
    );
  }
);

Grid.displayName = 'Grid';

// Flex Container Component
export interface FlexProps extends BaseComponentProps {
  direction?: 'row' | 'col' | 'row-reverse' | 'col-reverse';
  align?: 'start' | 'center' | 'end' | 'stretch' | 'baseline';
  justify?: 'start' | 'center' | 'end' | 'between' | 'around' | 'evenly';
  wrap?: 'wrap' | 'nowrap' | 'wrap-reverse';
  gap?: 'none' | 'sm' | 'md' | 'lg' | 'xl';
  asChild?: boolean;
}

const flexVariants = cva(
  'flex',
  {
    variants: {
      direction: {
        row: 'flex-row',
        col: 'flex-col',
        'row-reverse': 'flex-row-reverse',
        'col-reverse': 'flex-col-reverse',
      },
      align: {
        start: 'items-start',
        center: 'items-center',
        end: 'items-end',
        stretch: 'items-stretch',
        baseline: 'items-baseline',
      },
      justify: {
        start: 'justify-start',
        center: 'justify-center',
        end: 'justify-end',
        between: 'justify-between',
        around: 'justify-around',
        evenly: 'justify-evenly',
      },
      wrap: {
        wrap: 'flex-wrap',
        nowrap: 'flex-nowrap',
        'wrap-reverse': 'flex-wrap-reverse',
      },
      gap: {
        none: 'gap-0',
        sm: 'gap-2',
        md: 'gap-4',
        lg: 'gap-6',
        xl: 'gap-8',
      },
    },
    defaultVariants: {
      direction: 'row',
      align: 'start',
      justify: 'start',
      wrap: 'nowrap',
      gap: 'none',
    },
  }
);

export const Flex = forwardRef<HTMLDivElement, FlexProps>(
  (
    {
      className,
      direction = 'row',
      align = 'start',
      justify = 'start',
      wrap = 'nowrap',
      gap = 'none',
      asChild = false,
      children,
      'data-testid': testId,
      ...props
    },
    ref
  ) => {
    const Comp = asChild ? Slot : 'div';

    return (
      <Comp
        className={cn(
          flexVariants({ direction, align, justify, wrap, gap }),
          className
        )}
        ref={ref}
        data-testid={testId}
        {...props}
      >
        {children}
      </Comp>
    );
  }
);

Flex.displayName = 'Flex';

// Stack Component (Vertical Flex with gap)
export interface StackProps extends Omit<FlexProps, 'direction'> {
  spacing?: 'none' | 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl';
}

export const Stack = forwardRef<HTMLDivElement, StackProps>(
  ({ spacing = 'md', gap, ...props }, ref) => {
    const spacingMap = {
      none: 'none',
      xs: 'sm',
      sm: 'sm',
      md: 'md',
      lg: 'lg',
      xl: 'xl',
      '2xl': 'xl',
    };

    return (
      <Flex
        ref={ref}
        direction="col"
        gap={gap || spacingMap[spacing]}
        {...props}
      />
    );
  }
);

Stack.displayName = 'Stack';

// HStack Component (Horizontal Flex with gap)
export interface HStackProps extends Omit<FlexProps, 'direction'> {
  spacing?: 'none' | 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl';
}

export const HStack = forwardRef<HTMLDivElement, HStackProps>(
  ({ spacing = 'md', gap, ...props }, ref) => {
    const spacingMap = {
      none: 'none',
      xs: 'sm',
      sm: 'sm',
      md: 'md',
      lg: 'lg',
      xl: 'xl',
      '2xl': 'xl',
    };

    return (
      <Flex
        ref={ref}
        direction="row"
        gap={gap || spacingMap[spacing]}
        {...props}
      />
    );
  }
);

HStack.displayName = 'HStack';

export default Container;

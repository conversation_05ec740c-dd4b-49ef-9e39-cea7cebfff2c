import React, { forwardRef } from 'react';
import { cva, type VariantProps } from 'class-variance-authority';
import { cn } from '@/lib/utils';
import Header from '../../Header';
import Footer from '../../Footer';
import { Container } from './Container';
import type { BaseComponentProps } from '@/types/design-system';

// Layout variants using CVA
const layoutVariants = cva(
  'min-h-screen flex flex-col',
  {
    variants: {
      variant: {
        default: 'bg-white dark:bg-gray-900',
        app: 'bg-gray-50 dark:bg-gray-900',
        landing: 'bg-gradient-to-br from-orange-50 to-red-50 dark:from-gray-900 dark:to-gray-800',
        dashboard: 'bg-gray-50 dark:bg-gray-900',
        kitchen: 'bg-gradient-to-br from-orange-25 via-white to-red-25 dark:from-gray-900 dark:to-gray-800',
      },
      spacing: {
        none: '',
        comfortable: '',
        compact: '',
      },
    },
    defaultVariants: {
      variant: 'default',
      spacing: 'comfortable',
    },
  }
);

const mainVariants = cva(
  'flex-1',
  {
    variants: {
      spacing: {
        none: '',
        comfortable: 'py-6 sm:py-8',
        compact: 'py-4',
      },
      container: {
        true: '',
        false: '',
      },
    },
    defaultVariants: {
      spacing: 'comfortable',
      container: true,
    },
  }
);

// Enhanced AppLayout Props
export interface AppLayoutProps extends BaseComponentProps {
  variant?: 'default' | 'app' | 'landing' | 'dashboard' | 'kitchen';
  spacing?: 'none' | 'comfortable' | 'compact';
  showHeader?: boolean;
  showFooter?: boolean;
  headerProps?: any;
  footerProps?: any;
  containerized?: boolean;
  containerSize?: 'sm' | 'md' | 'lg' | 'xl' | '2xl' | 'full';
  stickyHeader?: boolean;
  stickyFooter?: boolean;
}

export const AppLayout = forwardRef<HTMLDivElement, AppLayoutProps>(
  (
    {
      children,
      className,
      variant = 'default',
      spacing = 'comfortable',
      showHeader = true,
      showFooter = true,
      headerProps = {},
      footerProps = {},
      containerized = true,
      containerSize = 'xl',
      stickyHeader = false,
      stickyFooter = false,
      'data-testid': testId,
      ...props
    },
    ref
  ) => {
    const MainContent = containerized ? Container : 'div';
    const mainContainerProps = containerized 
      ? { size: containerSize, className: mainVariants({ spacing, container: containerized }) }
      : { className: mainVariants({ spacing, container: containerized }) };

    return (
      <div
        ref={ref}
        className={cn(layoutVariants({ variant, spacing }), className)}
        data-testid={testId}
        {...props}
      >
        {/* Header */}
        {showHeader && (
          <header className={cn(stickyHeader && 'sticky top-0 z-40 backdrop-blur-sm bg-white/80 dark:bg-gray-900/80')}>
            <Header {...headerProps} />
          </header>
        )}

        {/* Main Content */}
        <main className="flex-1 relative">
          <MainContent {...mainContainerProps}>
            {children}
          </MainContent>
        </main>

        {/* Footer */}
        {showFooter && (
          <footer className={cn(stickyFooter && 'sticky bottom-0 z-40')}>
            <Footer {...footerProps} />
          </footer>
        )}
      </div>
    );
  }
);

AppLayout.displayName = 'AppLayout';

// Page Layout Component (Semantic wrapper)
export interface PageLayoutProps extends AppLayoutProps {
  title?: string;
  description?: string;
  breadcrumbs?: React.ReactNode;
  actions?: React.ReactNode;
  sidebar?: React.ReactNode;
  sidebarPosition?: 'left' | 'right';
  sidebarWidth?: 'sm' | 'md' | 'lg';
}

export const PageLayout = forwardRef<HTMLDivElement, PageLayoutProps>(
  (
    {
      title,
      description,
      breadcrumbs,
      actions,
      sidebar,
      sidebarPosition = 'left',
      sidebarWidth = 'md',
      children,
      ...layoutProps
    },
    ref
  ) => {
    const sidebarWidthClasses = {
      sm: 'w-64',
      md: 'w-72',
      lg: 'w-80',
    };

    const hasSidebar = !!sidebar;

    return (
      <AppLayout ref={ref} containerized={false} {...layoutProps}>
        <Container size={layoutProps.containerSize || 'xl'}>
          {/* Page Header */}
          {(title || description || breadcrumbs || actions) && (
            <div className="mb-6 sm:mb-8">
              {breadcrumbs && (
                <div className="mb-4">
                  {breadcrumbs}
                </div>
              )}
              
              <div className="flex items-start justify-between">
                <div className="min-w-0 flex-1">
                  {title && (
                    <h1 className="text-2xl sm:text-3xl font-bold text-gray-900 dark:text-gray-100 mb-2">
                      {title}
                    </h1>
                  )}
                  {description && (
                    <p className="text-gray-600 dark:text-gray-400">
                      {description}
                    </p>
                  )}
                </div>
                
                {actions && (
                  <div className="ml-4 flex-shrink-0">
                    {actions}
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Page Content */}
          {hasSidebar ? (
            <div className="flex gap-6 lg:gap-8">
              {/* Sidebar Left */}
              {sidebar && sidebarPosition === 'left' && (
                <aside className={cn(
                  'hidden lg:block flex-shrink-0',
                  sidebarWidthClasses[sidebarWidth]
                )}>
                  {sidebar}
                </aside>
              )}

              {/* Main Content */}
              <div className="flex-1 min-w-0">
                {children}
              </div>

              {/* Sidebar Right */}
              {sidebar && sidebarPosition === 'right' && (
                <aside className={cn(
                  'hidden lg:block flex-shrink-0',
                  sidebarWidthClasses[sidebarWidth]
                )}>
                  {sidebar}
                </aside>
              )}
            </div>
          ) : (
            children
          )}
        </Container>
      </AppLayout>
    );
  }
);

PageLayout.displayName = 'PageLayout';

// Dashboard Layout Component
export interface DashboardLayoutProps extends AppLayoutProps {
  sidebar: React.ReactNode;
  topbar?: React.ReactNode;
  sidebarCollapsed?: boolean;
  onSidebarToggle?: () => void;
}

export const DashboardLayout = forwardRef<HTMLDivElement, DashboardLayoutProps>(
  (
    {
      children,
      sidebar,
      topbar,
      sidebarCollapsed = false,
      onSidebarToggle,
      ...layoutProps
    },
    ref
  ) => {
    return (
      <AppLayout 
        ref={ref} 
        variant="dashboard" 
        containerized={false} 
        showHeader={false}
        showFooter={false}
        {...layoutProps}
      >
        <div className="flex h-screen">
          {/* Sidebar */}
          <aside className={cn(
            'bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 transition-all duration-300',
            sidebarCollapsed ? 'w-16' : 'w-64'
          )}>
            {sidebar}
          </aside>

          {/* Main Content Area */}
          <div className="flex-1 flex flex-col overflow-hidden">
            {/* Topbar */}
            {topbar && (
              <header className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 px-6 py-4">
                {topbar}
              </header>
            )}

            {/* Page Content */}
            <main className="flex-1 overflow-auto p-6">
              {children}
            </main>
          </div>
        </div>
      </AppLayout>
    );
  }
);

DashboardLayout.displayName = 'DashboardLayout';

export default AppLayout;

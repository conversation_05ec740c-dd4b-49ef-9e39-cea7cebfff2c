import React, { useEffect, useRef } from 'react';
import { createPortal } from 'react-dom';
import { cva, type VariantProps } from 'class-variance-authority';
import { cn } from '@/lib/utils';
import { X } from 'lucide-react';
import { Button } from '@/components/design-system/atoms/Button';
import type { 
  ModalVariant, 
  BaseComponentProps 
} from '@/types/design-system';

// Modal variants using CVA
const modalVariants = cva(
  // Base styles
  [
    'relative bg-white rounded-xl shadow-2xl',
    'max-h-[90vh] overflow-hidden',
    'dark:bg-gray-800',
  ],
  {
    variants: {
      size: {
        sm: 'w-full max-w-md',
        md: 'w-full max-w-lg',
        lg: 'w-full max-w-2xl',
        xl: 'w-full max-w-4xl',
        full: 'w-full h-full max-w-none max-h-none rounded-none',
      },
      variant: {
        default: '',
        centered: 'mx-auto',
      },
    },
    defaultVariants: {
      size: 'md',
      variant: 'default',
    },
  }
);

// Overlay variants
const overlayVariants = cva(
  [
    'fixed inset-0 z-50',
    'bg-black/50 backdrop-blur-sm',
    'flex items-center justify-center p-4',
    'transition-all duration-300',
  ],
  {
    variants: {
      animation: {
        fade: 'animate-in fade-in-0',
        scale: 'animate-in fade-in-0 zoom-in-95',
        slide: 'animate-in fade-in-0 slide-in-from-bottom-4',
      },
    },
    defaultVariants: {
      animation: 'scale',
    },
  }
);

// Modal Props Interface
export interface ModalProps extends BaseComponentProps {
  open: boolean;
  onClose: () => void;
  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full';
  variant?: ModalVariant;
  animation?: 'fade' | 'scale' | 'slide';
  closeOnOverlayClick?: boolean;
  closeOnEscape?: boolean;
  showCloseButton?: boolean;
  preventScroll?: boolean;
  portal?: boolean;
  portalContainer?: HTMLElement;
}

// Focus trap hook
const useFocusTrap = (isActive: boolean, containerRef: React.RefObject<HTMLElement>) => {
  useEffect(() => {
    if (!isActive || !containerRef.current) return;

    const container = containerRef.current;
    const focusableElements = container.querySelectorAll(
      'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
    );
    const firstElement = focusableElements[0] as HTMLElement;
    const lastElement = focusableElements[focusableElements.length - 1] as HTMLElement;

    const handleTabKey = (e: KeyboardEvent) => {
      if (e.key !== 'Tab') return;

      if (e.shiftKey) {
        if (document.activeElement === firstElement) {
          lastElement?.focus();
          e.preventDefault();
        }
      } else {
        if (document.activeElement === lastElement) {
          firstElement?.focus();
          e.preventDefault();
        }
      }
    };

    container.addEventListener('keydown', handleTabKey);
    firstElement?.focus();

    return () => {
      container.removeEventListener('keydown', handleTabKey);
    };
  }, [isActive, containerRef]);
};

// Main Modal Component
export const Modal: React.FC<ModalProps> = ({
  open,
  onClose,
  size = 'md',
  variant = 'default',
  animation = 'scale',
  closeOnOverlayClick = true,
  closeOnEscape = true,
  showCloseButton = true,
  preventScroll = true,
  portal = true,
  portalContainer,
  children,
  className,
  'data-testid': testId,
}) => {
  const modalRef = useRef<HTMLDivElement>(null);
  const previousActiveElement = useRef<HTMLElement | null>(null);

  // Focus trap
  useFocusTrap(open, modalRef);

  // Handle escape key
  useEffect(() => {
    if (!open || !closeOnEscape) return;

    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose();
      }
    };

    document.addEventListener('keydown', handleEscape);
    return () => document.removeEventListener('keydown', handleEscape);
  }, [open, closeOnEscape, onClose]);

  // Prevent scroll
  useEffect(() => {
    if (!open || !preventScroll) return;

    const originalStyle = window.getComputedStyle(document.body).overflow;
    document.body.style.overflow = 'hidden';

    return () => {
      document.body.style.overflow = originalStyle;
    };
  }, [open, preventScroll]);

  // Focus management
  useEffect(() => {
    if (open) {
      previousActiveElement.current = document.activeElement as HTMLElement;
    } else if (previousActiveElement.current) {
      previousActiveElement.current.focus();
    }
  }, [open]);

  if (!open) return null;

  const handleOverlayClick = (e: React.MouseEvent) => {
    if (closeOnOverlayClick && e.target === e.currentTarget) {
      onClose();
    }
  };

  const modalContent = (
    <div
      className={cn(overlayVariants({ animation }))}
      onClick={handleOverlayClick}
      data-testid={testId}
    >
      <div
        ref={modalRef}
        className={cn(
          modalVariants({ size, variant }),
          className
        )}
        role="dialog"
        aria-modal="true"
        aria-labelledby="modal-title"
      >
        {/* Close Button */}
        {showCloseButton && (
          <button
            onClick={onClose}
            className="absolute top-4 right-4 z-10 p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors dark:hover:bg-gray-700 dark:hover:text-gray-300"
            aria-label="Close modal"
          >
            <X className="w-5 h-5" />
          </button>
        )}

        {children}
      </div>
    </div>
  );

  if (portal) {
    const container = portalContainer || document.body;
    return createPortal(modalContent, container);
  }

  return modalContent;
};

Modal.displayName = 'Modal';

// Modal Header Component
export interface ModalHeaderProps extends BaseComponentProps {
  title?: string;
  description?: string;
  showCloseButton?: boolean;
  onClose?: () => void;
}

export const ModalHeader: React.FC<ModalHeaderProps> = ({
  title,
  description,
  showCloseButton = false,
  onClose,
  children,
  className,
  'data-testid': testId,
}) => {
  return (
    <div
      className={cn(
        'flex items-start justify-between p-6 border-b border-gray-200 dark:border-gray-700',
        className
      )}
      data-testid={testId}
    >
      <div className="flex-1 min-w-0">
        {title && (
          <h2
            id="modal-title"
            className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-1"
          >
            {title}
          </h2>
        )}
        {description && (
          <p className="text-sm text-gray-600 dark:text-gray-400">
            {description}
          </p>
        )}
        {children}
      </div>
      
      {showCloseButton && onClose && (
        <button
          onClick={onClose}
          className="ml-4 p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors dark:hover:bg-gray-700 dark:hover:text-gray-300"
          aria-label="Close modal"
        >
          <X className="w-5 h-5" />
        </button>
      )}
    </div>
  );
};

ModalHeader.displayName = 'ModalHeader';

// Modal Content Component
export interface ModalContentProps extends BaseComponentProps {
  scrollable?: boolean;
}

export const ModalContent: React.FC<ModalContentProps> = ({
  scrollable = true,
  children,
  className,
  'data-testid': testId,
}) => {
  return (
    <div
      className={cn(
        'p-6',
        scrollable && 'overflow-y-auto max-h-[60vh]',
        className
      )}
      data-testid={testId}
    >
      {children}
    </div>
  );
};

ModalContent.displayName = 'ModalContent';

// Modal Footer Component
export interface ModalFooterProps extends BaseComponentProps {
  justify?: 'start' | 'center' | 'end' | 'between';
}

export const ModalFooter: React.FC<ModalFooterProps> = ({
  justify = 'end',
  children,
  className,
  'data-testid': testId,
}) => {
  const justifyClasses = {
    start: 'justify-start',
    center: 'justify-center',
    end: 'justify-end',
    between: 'justify-between',
  };

  return (
    <div
      className={cn(
        'flex items-center gap-3 p-6 border-t border-gray-200 dark:border-gray-700',
        justifyClasses[justify],
        className
      )}
      data-testid={testId}
    >
      {children}
    </div>
  );
};

ModalFooter.displayName = 'ModalFooter';

// Confirmation Modal Component
export interface ConfirmationModalProps extends Omit<ModalProps, 'children'> {
  title: string;
  description?: string;
  confirmText?: string;
  cancelText?: string;
  confirmVariant?: 'primary' | 'danger';
  onConfirm: () => void;
  loading?: boolean;
}

export const ConfirmationModal: React.FC<ConfirmationModalProps> = ({
  title,
  description,
  confirmText = 'Confirm',
  cancelText = 'Cancel',
  confirmVariant = 'primary',
  onConfirm,
  onClose,
  loading = false,
  ...modalProps
}) => {
  return (
    <Modal onClose={onClose} size="sm" {...modalProps}>
      <ModalHeader title={title} description={description} />
      
      <ModalFooter>
        <Button
          variant="ghost"
          onClick={onClose}
          disabled={loading}
        >
          {cancelText}
        </Button>
        <Button
          variant={confirmVariant}
          onClick={onConfirm}
          loading={loading}
        >
          {confirmText}
        </Button>
      </ModalFooter>
    </Modal>
  );
};

ConfirmationModal.displayName = 'ConfirmationModal';

// Export compound components
Modal.Header = ModalHeader;
Modal.Content = ModalContent;
Modal.Footer = ModalFooter;

export default Modal;

import React, { useState } from 'react';
import { Heart, Clock, Users, Star, MoreVertical, Share2, Bookmark } from 'lucide-react';
import { cn } from '@/lib/utils';
import { TouchButton, SwipeableCard } from './mobile-touch';
import { OptimizedImage } from './optimized-image';

// Mobile-optimized recipe card
interface MobileRecipeCardProps {
  id: string;
  title: string;
  description?: string;
  image: string;
  cookTime?: number;
  servings?: number;
  rating?: number;
  difficulty?: 'easy' | 'medium' | 'hard';
  isFavorite?: boolean;
  onFavoriteToggle?: (id: string) => void;
  onShare?: (id: string) => void;
  onBookmark?: (id: string) => void;
  onClick?: (id: string) => void;
  className?: string;
}

export const MobileRecipeCard: React.FC<MobileRecipeCardProps> = ({
  id,
  title,
  description,
  image,
  cookTime,
  servings,
  rating,
  difficulty,
  isFavorite = false,
  onFavoriteToggle,
  onShare,
  onBookmark,
  onClick,
  className,
}) => {
  const [showActions, setShowActions] = useState(false);

  const difficultyColors = {
    easy: 'bg-green-100 text-green-700',
    medium: 'bg-yellow-100 text-yellow-700',
    hard: 'bg-red-100 text-red-700',
  };

  const difficultyLabels = {
    easy: 'Dễ',
    medium: 'Trung bình',
    hard: 'Khó',
  };

  const handleSwipeLeft = () => {
    onBookmark?.(id);
  };

  const handleSwipeRight = () => {
    onFavoriteToggle?.(id);
  };

  return (
    <SwipeableCard
      onSwipeLeft={handleSwipeLeft}
      onSwipeRight={handleSwipeRight}
      className={cn('bg-white rounded-xl shadow-sm border border-gray-200', className)}
    >
      <div className="relative">
        {/* Image */}
        <div className="relative aspect-video overflow-hidden rounded-t-xl">
          <OptimizedImage
            src={image}
            alt={title}
            aspectRatio="video"
            className="w-full h-full object-cover"
            priority={false}
          />
          
          {/* Overlay actions */}
          <div className="absolute top-3 right-3 flex gap-2">
            <TouchButton
              variant="ghost"
              size="sm"
              onClick={() => onFavoriteToggle?.(id)}
              className={cn(
                'w-8 h-8 rounded-full backdrop-blur-sm',
                isFavorite 
                  ? 'bg-red-500/90 text-white' 
                  : 'bg-white/90 text-gray-700'
              )}
            >
              <Heart className={cn('w-4 h-4', isFavorite && 'fill-current')} />
            </TouchButton>
            
            <TouchButton
              variant="ghost"
              size="sm"
              onClick={() => setShowActions(!showActions)}
              className="w-8 h-8 rounded-full bg-white/90 text-gray-700 backdrop-blur-sm"
            >
              <MoreVertical className="w-4 h-4" />
            </TouchButton>
          </div>

          {/* Difficulty badge */}
          {difficulty && (
            <div className="absolute top-3 left-3">
              <span className={cn(
                'px-2 py-1 rounded-full text-xs font-medium',
                difficultyColors[difficulty]
              )}>
                {difficultyLabels[difficulty]}
              </span>
            </div>
          )}

          {/* Rating */}
          {rating && (
            <div className="absolute bottom-3 left-3 flex items-center gap-1 bg-black/50 backdrop-blur-sm rounded-full px-2 py-1">
              <Star className="w-3 h-3 text-yellow-400 fill-current" />
              <span className="text-white text-xs font-medium">{rating.toFixed(1)}</span>
            </div>
          )}
        </div>

        {/* Action menu */}
        {showActions && (
          <div className="absolute top-14 right-3 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-10">
            <TouchButton
              variant="ghost"
              size="sm"
              onClick={() => {
                onShare?.(id);
                setShowActions(false);
              }}
              className="w-full justify-start px-4 py-2 text-left"
            >
              <Share2 className="w-4 h-4 mr-2" />
              Chia sẻ
            </TouchButton>
            <TouchButton
              variant="ghost"
              size="sm"
              onClick={() => {
                onBookmark?.(id);
                setShowActions(false);
              }}
              className="w-full justify-start px-4 py-2 text-left"
            >
              <Bookmark className="w-4 h-4 mr-2" />
              Lưu lại
            </TouchButton>
          </div>
        )}

        {/* Content */}
        <div 
          className="p-4 cursor-pointer"
          onClick={() => onClick?.(id)}
        >
          <h3 className="font-semibold text-gray-900 text-lg mb-2 line-clamp-2">
            {title}
          </h3>
          
          {description && (
            <p className="text-gray-600 text-sm mb-3 line-clamp-2">
              {description}
            </p>
          )}

          {/* Meta info */}
          <div className="flex items-center justify-between text-sm text-gray-500">
            <div className="flex items-center gap-4">
              {cookTime && (
                <div className="flex items-center gap-1">
                  <Clock className="w-4 h-4" />
                  <span>{cookTime} phút</span>
                </div>
              )}
              
              {servings && (
                <div className="flex items-center gap-1">
                  <Users className="w-4 h-4" />
                  <span>{servings} người</span>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </SwipeableCard>
  );
};

// Mobile meal plan card
interface MobileMealPlanCardProps {
  id: string;
  title: string;
  description?: string;
  meals: Array<{
    type: 'breakfast' | 'lunch' | 'dinner';
    name: string;
    image?: string;
  }>;
  duration?: string;
  totalCost?: number;
  onClick?: (id: string) => void;
  className?: string;
}

export const MobileMealPlanCard: React.FC<MobileMealPlanCardProps> = ({
  id,
  title,
  description,
  meals,
  duration,
  totalCost,
  onClick,
  className,
}) => {
  const mealTypeLabels = {
    breakfast: 'Sáng',
    lunch: 'Trưa',
    dinner: 'Tối',
  };

  return (
    <TouchButton
      variant="ghost"
      onClick={() => onClick?.(id)}
      className={cn(
        'w-full p-0 h-auto bg-white rounded-xl shadow-sm border border-gray-200',
        'hover:shadow-md transition-shadow',
        className
      )}
    >
      <div className="p-4">
        <div className="flex items-start justify-between mb-3">
          <div className="flex-1">
            <h3 className="font-semibold text-gray-900 text-lg mb-1">
              {title}
            </h3>
            {description && (
              <p className="text-gray-600 text-sm line-clamp-2">
                {description}
              </p>
            )}
          </div>
          
          {totalCost && (
            <div className="ml-3 text-right">
              <div className="text-lg font-bold text-orange-600">
                {totalCost.toLocaleString('vi-VN')}đ
              </div>
              {duration && (
                <div className="text-xs text-gray-500">{duration}</div>
              )}
            </div>
          )}
        </div>

        {/* Meals preview */}
        <div className="space-y-2">
          {meals.slice(0, 3).map((meal, index) => (
            <div key={index} className="flex items-center gap-3">
              <div className="w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center">
                <span className="text-orange-600 text-xs font-medium">
                  {mealTypeLabels[meal.type][0]}
                </span>
              </div>
              <span className="text-sm text-gray-700 flex-1 truncate">
                {meal.name}
              </span>
            </div>
          ))}
          
          {meals.length > 3 && (
            <div className="text-xs text-gray-500 text-center pt-2">
              +{meals.length - 3} món khác
            </div>
          )}
        </div>
      </div>
    </TouchButton>
  );
};

// Mobile shopping item card
interface MobileShoppingItemProps {
  id: string;
  name: string;
  quantity: string;
  unit: string;
  category?: string;
  isChecked?: boolean;
  onToggle?: (id: string) => void;
  onRemove?: (id: string) => void;
  className?: string;
}

export const MobileShoppingItem: React.FC<MobileShoppingItemProps> = ({
  id,
  name,
  quantity,
  unit,
  category,
  isChecked = false,
  onToggle,
  onRemove,
  className,
}) => {
  return (
    <SwipeableCard
      onSwipeLeft={() => onRemove?.(id)}
      className={cn(
        'bg-white border border-gray-200 rounded-lg',
        isChecked && 'opacity-60',
        className
      )}
    >
      <div className="flex items-center gap-3 p-4">
        <TouchButton
          variant="ghost"
          size="sm"
          onClick={() => onToggle?.(id)}
          className={cn(
            'w-6 h-6 rounded-full border-2 flex items-center justify-center',
            isChecked 
              ? 'bg-green-500 border-green-500 text-white' 
              : 'border-gray-300'
          )}
        >
          {isChecked && <span className="text-xs">✓</span>}
        </TouchButton>
        
        <div className="flex-1 min-w-0">
          <div className={cn(
            'font-medium text-gray-900',
            isChecked && 'line-through'
          )}>
            {name}
          </div>
          <div className="text-sm text-gray-500">
            {quantity} {unit}
            {category && ` • ${category}`}
          </div>
        </div>
      </div>
    </SwipeableCard>
  );
};

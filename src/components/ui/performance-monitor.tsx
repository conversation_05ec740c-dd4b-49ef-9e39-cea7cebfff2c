import React, { useEffect, useState, useCallback } from 'react';
import { cn } from '@/lib/utils';

interface PerformanceMetrics {
  renderTime: number;
  renderCount: number;
  memoryUsage?: number;
  bundleSize?: number;
  loadTime?: number;
}

interface PerformanceMonitorProps {
  enabled?: boolean;
  componentName?: string;
  showInProduction?: boolean;
  className?: string;
}

export const PerformanceMonitor: React.FC<PerformanceMonitorProps> = ({
  enabled = process.env.NODE_ENV === 'development',
  componentName = 'App',
  showInProduction = false,
  className
}) => {
  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    renderTime: 0,
    renderCount: 0,
  });
  const [isVisible, setIsVisible] = useState(false);

  // Only show in development or when explicitly enabled for production
  const shouldShow = enabled && (process.env.NODE_ENV === 'development' || showInProduction);

  const updateMetrics = useCallback(() => {
    if (!shouldShow) return;

    const now = performance.now();
    
    // Get memory usage if available
    const memoryInfo = (performance as any).memory;
    const memoryUsage = memoryInfo ? memoryInfo.usedJSHeapSize / 1024 / 1024 : undefined;

    // Get navigation timing for load time
    const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
    const loadTime = navigation ? navigation.loadEventEnd - navigation.fetchStart : undefined;

    setMetrics(prev => ({
      renderTime: now,
      renderCount: prev.renderCount + 1,
      memoryUsage,
      loadTime,
    }));
  }, [shouldShow]);

  useEffect(() => {
    updateMetrics();
  }, [updateMetrics]);

  // Monitor performance entries
  useEffect(() => {
    if (!shouldShow) return;

    const observer = new PerformanceObserver((list) => {
      const entries = list.getEntries();
      entries.forEach((entry) => {
        if (entry.entryType === 'measure' || entry.entryType === 'navigation') {
          console.log(`📊 Performance: ${entry.name} - ${entry.duration.toFixed(2)}ms`);
        }
      });
    });

    observer.observe({ entryTypes: ['measure', 'navigation'] });

    return () => observer.disconnect();
  }, [shouldShow]);

  if (!shouldShow) return null;

  return (
    <div className={cn('fixed bottom-4 right-4 z-50', className)}>
      <button
        onClick={() => setIsVisible(!isVisible)}
        className="bg-blue-500 hover:bg-blue-600 text-white text-xs px-2 py-1 rounded-full shadow-lg transition-colors"
        title="Performance Monitor"
      >
        📊
      </button>

      {isVisible && (
        <div className="absolute bottom-full right-0 mb-2 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg p-3 min-w-[250px] text-xs">
          <div className="font-semibold text-gray-900 dark:text-gray-100 mb-2">
            Performance Metrics - {componentName}
          </div>
          
          <div className="space-y-1 text-gray-600 dark:text-gray-400">
            <div className="flex justify-between">
              <span>Render Count:</span>
              <span className="font-mono">{metrics.renderCount}</span>
            </div>
            
            <div className="flex justify-between">
              <span>Last Render:</span>
              <span className="font-mono">{metrics.renderTime.toFixed(2)}ms</span>
            </div>

            {metrics.memoryUsage && (
              <div className="flex justify-between">
                <span>Memory Usage:</span>
                <span className="font-mono">{metrics.memoryUsage.toFixed(1)}MB</span>
              </div>
            )}

            {metrics.loadTime && (
              <div className="flex justify-between">
                <span>Load Time:</span>
                <span className="font-mono">{metrics.loadTime.toFixed(0)}ms</span>
              </div>
            )}
          </div>

          <div className="mt-2 pt-2 border-t border-gray-200 dark:border-gray-600">
            <button
              onClick={() => {
                // Clear performance entries
                performance.clearMarks();
                performance.clearMeasures();
                setMetrics(prev => ({ ...prev, renderCount: 0 }));
              }}
              className="text-blue-500 hover:text-blue-600 text-xs"
            >
              Clear Metrics
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

// HOC to wrap components with performance monitoring
export function withPerformanceMonitoring<P extends object>(
  Component: React.ComponentType<P>,
  componentName?: string
) {
  const WrappedComponent = React.forwardRef<any, P>((props, ref) => {
    const [renderStart] = useState(() => performance.now());
    
    useEffect(() => {
      const renderEnd = performance.now();
      const renderTime = renderEnd - renderStart;
      
      if (process.env.NODE_ENV === 'development') {
        console.log(`🚀 ${componentName || Component.displayName || Component.name} rendered in ${renderTime.toFixed(2)}ms`);
      }
    });

    return <Component ref={ref} {...props} />;
  });

  WrappedComponent.displayName = `withPerformanceMonitoring(${componentName || Component.displayName || Component.name})`;
  
  return WrappedComponent;
}

// Hook to measure component performance
export function usePerformanceMeasure(componentName: string) {
  const [renderCount, setRenderCount] = useState(0);
  const renderStart = React.useRef<number>();

  useEffect(() => {
    renderStart.current = performance.now();
    setRenderCount(prev => prev + 1);
  });

  useEffect(() => {
    if (renderStart.current) {
      const renderTime = performance.now() - renderStart.current;
      
      if (process.env.NODE_ENV === 'development') {
        performance.mark(`${componentName}-render-start`);
        performance.mark(`${componentName}-render-end`);
        performance.measure(
          `${componentName}-render`,
          `${componentName}-render-start`,
          `${componentName}-render-end`
        );
      }
    }
  });

  return { renderCount };
}

export default PerformanceMonitor;

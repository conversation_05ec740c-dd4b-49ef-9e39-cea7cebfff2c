import React, { useState, useCallback, forwardRef } from 'react';
import { cn } from '@/lib/utils';

interface OptimizedImageProps extends React.ImgHTMLAttributes<HTMLImageElement> {
  src: string;
  alt: string;
  fallbackSrc?: string;
  aspectRatio?: 'square' | 'video' | 'wide' | 'auto';
  objectFit?: 'cover' | 'contain' | 'fill';
  priority?: boolean;
  placeholder?: 'blur' | 'empty';
  blurDataURL?: string;
  onLoadingComplete?: () => void;
  onError?: () => void;
}

const OptimizedImage = forwardRef<HTMLImageElement, OptimizedImageProps>(
  ({
    src,
    alt,
    fallbackSrc = '/placeholder.svg',
    aspectRatio = 'auto',
    objectFit = 'cover',
    priority = false,
    placeholder = 'empty',
    blurDataURL,
    className,
    onLoadingComplete,
    onError,
    ...props
  }, ref) => {
    const [isLoading, setIsLoading] = useState(true);
    const [hasError, setHasError] = useState(false);
    const [currentSrc, setCurrentSrc] = useState(src);

    const aspectRatioClasses = {
      square: 'aspect-square',
      video: 'aspect-video',
      wide: 'aspect-[21/9]',
      auto: '',
    };

    const objectFitClasses = {
      cover: 'object-cover',
      contain: 'object-contain',
      fill: 'object-fill',
    };

    const handleLoad = useCallback(() => {
      setIsLoading(false);
      onLoadingComplete?.();
    }, [onLoadingComplete]);

    const handleError = useCallback(() => {
      setHasError(true);
      setIsLoading(false);
      if (currentSrc !== fallbackSrc) {
        setCurrentSrc(fallbackSrc);
      }
      onError?.();
    }, [currentSrc, fallbackSrc, onError]);

    // Generate optimized src URLs for different sizes
    const generateOptimizedSrc = (originalSrc: string, width?: number) => {
      // If it's already an optimized URL or external URL, return as is
      if (originalSrc.includes('unsplash.com') || originalSrc.includes('w=')) {
        return originalSrc;
      }
      
      // For local images, you could implement your own optimization logic here
      return originalSrc;
    };

    const optimizedSrc = generateOptimizedSrc(currentSrc);

    return (
      <div className={cn('relative overflow-hidden', aspectRatioClasses[aspectRatio])}>
        {/* Placeholder/Blur background */}
        {isLoading && placeholder === 'blur' && blurDataURL && (
          <img
            src={blurDataURL}
            alt=""
            className={cn(
              'absolute inset-0 w-full h-full',
              objectFitClasses[objectFit],
              'filter blur-sm scale-110'
            )}
            aria-hidden="true"
          />
        )}

        {/* Loading skeleton */}
        {isLoading && placeholder === 'empty' && (
          <div className="absolute inset-0 bg-gray-200 animate-pulse flex items-center justify-center">
            <div className="text-gray-400 text-sm">Đang tải...</div>
          </div>
        )}

        {/* Main image */}
        <img
          ref={ref}
          src={optimizedSrc}
          alt={alt}
          loading={priority ? 'eager' : 'lazy'}
          decoding="async"
          className={cn(
            'w-full h-full transition-opacity duration-300',
            objectFitClasses[objectFit],
            isLoading ? 'opacity-0' : 'opacity-100',
            className
          )}
          onLoad={handleLoad}
          onError={handleError}
          {...props}
        />

        {/* Error state */}
        {hasError && currentSrc === fallbackSrc && (
          <div className="absolute inset-0 bg-gray-100 flex items-center justify-center">
            <div className="text-center text-gray-500">
              <div className="text-2xl mb-2">📷</div>
              <div className="text-sm">Không thể tải ảnh</div>
            </div>
          </div>
        )}
      </div>
    );
  }
);

OptimizedImage.displayName = 'OptimizedImage';

export { OptimizedImage };
export type { OptimizedImageProps };

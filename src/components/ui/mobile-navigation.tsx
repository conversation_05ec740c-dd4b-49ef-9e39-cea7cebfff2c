import React, { useState, useEffect } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { 
  Home, 
  Search, 
  BookOpen, 
  Calendar, 
  User, 
  ChefHat,
  ShoppingCart,
  Heart,
  Settings,
  Menu,
  X
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { TouchButton } from './mobile-touch';

interface MobileNavItem {
  icon: React.ReactNode;
  label: string;
  href: string;
  badge?: number;
}

interface MobileBottomNavProps {
  className?: string;
}

export const MobileBottomNav: React.FC<MobileBottomNavProps> = ({ className }) => {
  const location = useLocation();
  const [cartCount, setCartCount] = useState(0);

  // Update cart count
  useEffect(() => {
    const updateCartCount = () => {
      const savedCart = localStorage.getItem('simple_shopping_cart');
      if (savedCart) {
        try {
          const cart = JSON.parse(savedCart);
          setCartCount(cart.length);
        } catch {
          setCartCount(0);
        }
      }
    };

    updateCartCount();
    window.addEventListener('storage', updateCartCount);
    return () => window.removeEventListener('storage', updateCartCount);
  }, []);

  const navItems: MobileNavItem[] = [
    {
      icon: <Home className="w-5 h-5" />,
      label: 'Trang chủ',
      href: '/',
    },
    {
      icon: <Search className="w-5 h-5" />,
      label: 'Tìm kiếm',
      href: '/recipes',
    },
    {
      icon: <Calendar className="w-5 h-5" />,
      label: 'Kế hoạch',
      href: '/meal-planner',
    },
    {
      icon: <ShoppingCart className="w-5 h-5" />,
      label: 'Giỏ hàng',
      href: '/shopping-cart',
      badge: cartCount,
    },
    {
      icon: <User className="w-5 h-5" />,
      label: 'Cá nhân',
      href: '/profile',
    },
  ];

  return (
    <nav className={cn(
      'fixed bottom-0 left-0 right-0 z-50',
      'bg-white border-t border-gray-200',
      'safe-area-inset-bottom', // Handle iPhone notch
      className
    )}>
      <div className="flex items-center justify-around px-2 py-2">
        {navItems.map((item) => {
          const isActive = location.pathname === item.href;
          
          return (
            <Link
              key={item.href}
              to={item.href}
              className={cn(
                'flex flex-col items-center justify-center',
                'min-w-[60px] py-2 px-1',
                'rounded-lg transition-colors duration-200',
                'touch-manipulation',
                isActive 
                  ? 'text-orange-600 bg-orange-50' 
                  : 'text-gray-600 hover:text-orange-600 hover:bg-orange-50'
              )}
            >
              <div className="relative">
                {item.icon}
                {item.badge && item.badge > 0 && (
                  <span className="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full min-w-[18px] h-[18px] flex items-center justify-center">
                    {item.badge > 99 ? '99+' : item.badge}
                  </span>
                )}
              </div>
              <span className="text-xs mt-1 font-medium">{item.label}</span>
            </Link>
          );
        })}
      </div>
    </nav>
  );
};

// Mobile slide-out menu
interface MobileSideMenuProps {
  isOpen: boolean;
  onClose: () => void;
  children: React.ReactNode;
}

export const MobileSideMenu: React.FC<MobileSideMenuProps> = ({
  isOpen,
  onClose,
  children,
}) => {
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isOpen]);

  return (
    <>
      {/* Backdrop */}
      {isOpen && (
        <div
          className="fixed inset-0 bg-black/50 z-40 lg:hidden"
          onClick={onClose}
        />
      )}

      {/* Side menu */}
      <div
        className={cn(
          'fixed top-0 left-0 h-full w-80 max-w-[85vw]',
          'bg-white shadow-xl z-50 lg:hidden',
          'transform transition-transform duration-300 ease-out',
          isOpen ? 'translate-x-0' : '-translate-x-full'
        )}
      >
        <div className="flex items-center justify-between p-4 border-b border-gray-200">
          <h2 className="text-lg font-semibold text-gray-900">Menu</h2>
          <TouchButton
            variant="ghost"
            size="sm"
            onClick={onClose}
            className="p-2"
          >
            <X className="w-5 h-5" />
          </TouchButton>
        </div>
        
        <div className="overflow-y-auto h-full pb-20">
          {children}
        </div>
      </div>
    </>
  );
};

// Mobile-optimized header
interface MobileHeaderProps {
  title: string;
  leftAction?: React.ReactNode;
  rightAction?: React.ReactNode;
  className?: string;
  variant?: 'default' | 'transparent' | 'elevated';
}

export const MobileHeader: React.FC<MobileHeaderProps> = ({
  title,
  leftAction,
  rightAction,
  className,
  variant = 'default',
}) => {
  const variants = {
    default: 'bg-white border-b border-gray-200',
    transparent: 'bg-transparent',
    elevated: 'bg-white shadow-md',
  };

  return (
    <header className={cn(
      'sticky top-0 z-40',
      'flex items-center justify-between',
      'h-14 px-4',
      'safe-area-inset-top', // Handle iPhone notch
      variants[variant],
      className
    )}>
      <div className="flex items-center min-w-0">
        {leftAction}
      </div>
      
      <h1 className="text-lg font-semibold text-gray-900 truncate mx-4">
        {title}
      </h1>
      
      <div className="flex items-center min-w-0">
        {rightAction}
      </div>
    </header>
  );
};

// Floating Action Button
interface FloatingActionButtonProps {
  icon: React.ReactNode;
  onClick: () => void;
  className?: string;
  position?: 'bottom-right' | 'bottom-left' | 'bottom-center';
}

export const FloatingActionButton: React.FC<FloatingActionButtonProps> = ({
  icon,
  onClick,
  className,
  position = 'bottom-right',
}) => {
  const positions = {
    'bottom-right': 'bottom-20 right-4',
    'bottom-left': 'bottom-20 left-4',
    'bottom-center': 'bottom-20 left-1/2 transform -translate-x-1/2',
  };

  return (
    <TouchButton
      onClick={onClick}
      variant="primary"
      className={cn(
        'fixed z-40',
        'w-14 h-14 rounded-full',
        'shadow-lg hover:shadow-xl',
        'flex items-center justify-center',
        positions[position],
        className
      )}
      haptic
    >
      {icon}
    </TouchButton>
  );
};

// Mobile-optimized tabs
interface MobileTabsProps {
  tabs: Array<{
    id: string;
    label: string;
    icon?: React.ReactNode;
  }>;
  activeTab: string;
  onTabChange: (tabId: string) => void;
  className?: string;
}

export const MobileTabs: React.FC<MobileTabsProps> = ({
  tabs,
  activeTab,
  onTabChange,
  className,
}) => {
  return (
    <div className={cn(
      'flex bg-gray-100 rounded-lg p-1',
      'overflow-x-auto scrollbar-hide',
      className
    )}>
      {tabs.map((tab) => (
        <TouchButton
          key={tab.id}
          variant={activeTab === tab.id ? 'primary' : 'ghost'}
          size="sm"
          onClick={() => onTabChange(tab.id)}
          className={cn(
            'flex items-center gap-2 whitespace-nowrap',
            'min-w-0 flex-1',
            activeTab === tab.id 
              ? 'bg-white shadow-sm' 
              : 'bg-transparent hover:bg-white/50'
          )}
        >
          {tab.icon}
          <span className="truncate">{tab.label}</span>
        </TouchButton>
      ))}
    </div>
  );
};

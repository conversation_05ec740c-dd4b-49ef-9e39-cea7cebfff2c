import React, { useState } from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import {
  Clock,
  Users,
  DollarSign,
  ChefHat,
  Star,
  ShoppingCart,
  Calendar,
  Utensils,
  Coffee,
  Soup,
  Cookie
} from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { vietnameseMealTemplates, MealTemplate, getMealTemplatesByCategory, getMealTemplatesByDifficulty, getMealTemplatesByBudget } from '@/data/vietnameseMealTemplates';

interface MealTemplateExplorerProps {
  onSelectTemplate?: (template: MealTemplate) => void;
  className?: string;
}

export const MealTemplateExplorer: React.FC<MealTemplateExplorerProps> = ({
  onSelectTemplate,
  className
}) => {
  const navigate = useNavigate();
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [selectedDifficulty, setSelectedDifficulty] = useState<string>('all');
  const [maxBudget, setMaxBudget] = useState<string>('all');

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND'
    }).format(price);
  };

  const getCategoryIcon = (category: string) => {
    const icons = {
      quick: '⚡',
      budget: '💰',
      healthy: '🥗',
      family: '👨‍👩‍👧‍👦',
      special: '⭐',
      vegetarian: '🌱'
    };
    return icons[category as keyof typeof icons] || '🍽️';
  };

  const getCategoryColor = (category: string) => {
    const colors = {
      quick: 'bg-blue-100 text-blue-800',
      budget: 'bg-green-100 text-green-800',
      healthy: 'bg-emerald-100 text-emerald-800',
      family: 'bg-purple-100 text-purple-800',
      special: 'bg-orange-100 text-orange-800',
      vegetarian: 'bg-lime-100 text-lime-800'
    };
    return colors[category as keyof typeof colors] || 'bg-gray-100 text-gray-800';
  };

  const getDifficultyColor = (difficulty: string) => {
    const colors = {
      easy: 'bg-green-100 text-green-800',
      medium: 'bg-yellow-100 text-yellow-800',
      hard: 'bg-red-100 text-red-800'
    };
    return colors[difficulty as keyof typeof colors] || 'bg-gray-100 text-gray-800';
  };

  const getMealIcon = (mealType: string) => {
    const icons = {
      breakfast: <Coffee className="h-4 w-4" />,
      lunch: <Utensils className="h-4 w-4" />,
      dinner: <Soup className="h-4 w-4" />,
      snack: <Cookie className="h-4 w-4" />
    };
    return icons[mealType as keyof typeof icons] || <Utensils className="h-4 w-4" />;
  };

  // Filter templates
  const filteredTemplates = vietnameseMealTemplates.filter(template => {
    if (selectedCategory !== 'all' && template.category !== selectedCategory) return false;
    if (selectedDifficulty !== 'all' && template.difficulty !== selectedDifficulty) return false;
    if (maxBudget !== 'all') {
      const budget = parseInt(maxBudget);
      if (template.estimatedCost > budget) return false;
    }
    return true;
  });

  return (
    <div className={className}>
      {/* Header */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <ChefHat className="h-6 w-6 text-orange-500" />
            Thực Đơn Mẫu "Mâm Cơm Việt"
          </CardTitle>
          <p className="text-gray-600">
            Chọn thực đơn phù hợp với mục tiêu và hoàn cảnh của bạn
          </p>
        </CardHeader>
        
        <CardContent>
          {/* Filters */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <Select value={selectedCategory} onValueChange={setSelectedCategory}>
              <SelectTrigger>
                <SelectValue placeholder="Loại thực đơn" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Tất cả loại</SelectItem>
                <SelectItem value="quick">⚡ Nhanh gọn</SelectItem>
                <SelectItem value="budget">💰 Tiết kiệm</SelectItem>
                <SelectItem value="healthy">🥗 Healthy</SelectItem>
                <SelectItem value="family">👨‍👩‍👧‍👦 Gia đình</SelectItem>
                <SelectItem value="special">⭐ Đặc biệt</SelectItem>
                <SelectItem value="vegetarian">🌱 Chay</SelectItem>
              </SelectContent>
            </Select>

            <Select value={selectedDifficulty} onValueChange={setSelectedDifficulty}>
              <SelectTrigger>
                <SelectValue placeholder="Độ khó" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Tất cả độ khó</SelectItem>
                <SelectItem value="easy">Dễ</SelectItem>
                <SelectItem value="medium">Trung bình</SelectItem>
                <SelectItem value="hard">Khó</SelectItem>
              </SelectContent>
            </Select>

            <Select value={maxBudget} onValueChange={setMaxBudget}>
              <SelectTrigger>
                <SelectValue placeholder="Ngân sách" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Tất cả ngân sách</SelectItem>
                <SelectItem value="150000">Dưới 150k</SelectItem>
                <SelectItem value="200000">Dưới 200k</SelectItem>
                <SelectItem value="300000">Dưới 300k</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Results count */}
          <p className="text-sm text-gray-600 mb-4">
            Hiển thị {filteredTemplates.length} thực đơn
          </p>
        </CardContent>
      </Card>

      {/* Templates Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {filteredTemplates.map((template) => (
          <Card key={template.id} className="hover:shadow-lg transition-shadow duration-200">
            <CardHeader className="pb-3">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <h3 className="font-semibold text-gray-900 mb-2">{template.name}</h3>
                  <p className="text-sm text-gray-600 mb-3">{template.description}</p>
                  
                  <div className="flex flex-wrap gap-2">
                    <Badge className={getCategoryColor(template.category)}>
                      {getCategoryIcon(template.category)} {template.category}
                    </Badge>
                    <Badge className={getDifficultyColor(template.difficulty)}>
                      {template.difficulty}
                    </Badge>
                    {template.region && (
                      <Badge variant="outline">
                        {template.region === 'north' ? '🏔️ Bắc' :
                         template.region === 'central' ? '🏛️ Trung' :
                         template.region === 'south' ? '🌴 Nam' : '🇻🇳 Toàn quốc'}
                      </Badge>
                    )}
                  </div>
                </div>
              </div>
            </CardHeader>
            
            <CardContent className="pt-0">
              {/* Quick Info */}
              <div className="grid grid-cols-3 gap-4 mb-4 text-sm">
                <div className="flex items-center gap-1">
                  <Clock className="h-4 w-4 text-gray-400" />
                  <span className="text-gray-600">{template.totalTime}</span>
                </div>
                <div className="flex items-center gap-1">
                  <Users className="h-4 w-4 text-gray-400" />
                  <span className="text-gray-600">{template.servings} người</span>
                </div>
                <div className="flex items-center gap-1">
                  <DollarSign className="h-4 w-4 text-gray-400" />
                  <span className="text-gray-600 font-medium text-green-600">
                    {formatPrice(template.estimatedCost)}
                  </span>
                </div>
              </div>

              {/* Meals Preview */}
              <div className="space-y-2 mb-4">
                {Object.entries(template.meals).map(([mealType, dishes]) => (
                  dishes && dishes.length > 0 && (
                    <div key={mealType} className="flex items-center gap-2 text-sm">
                      {getMealIcon(mealType)}
                      <span className="font-medium capitalize">
                        {mealType === 'breakfast' ? 'Sáng' :
                         mealType === 'lunch' ? 'Trưa' :
                         mealType === 'dinner' ? 'Tối' : 'Ăn vặt'}:
                      </span>
                      <span className="text-gray-600">
                        {dishes.length} món
                      </span>
                    </div>
                  )
                ))}
              </div>

              {/* Nutrition Summary */}
              <div className="bg-gray-50 rounded-lg p-3 mb-4">
                <h4 className="text-sm font-medium text-gray-900 mb-2">Dinh dưỡng/ngày:</h4>
                <div className="grid grid-cols-2 gap-2 text-xs">
                  <div>Calories: <span className="font-medium">{template.nutritionSummary.calories} kcal</span></div>
                  <div>Protein: <span className="font-medium">{template.nutritionSummary.protein}g</span></div>
                  <div>Carbs: <span className="font-medium">{template.nutritionSummary.carbs}g</span></div>
                  <div>Fat: <span className="font-medium">{template.nutritionSummary.fat}g</span></div>
                </div>
              </div>

              {/* Tags */}
              <div className="flex flex-wrap gap-1 mb-4">
                {template.tags.slice(0, 3).map((tag, index) => (
                  <Badge key={index} variant="secondary" className="text-xs">
                    {tag}
                  </Badge>
                ))}
              </div>

              {/* Actions */}
              <div className="flex gap-2">
                <Button
                  className="flex-1"
                  onClick={() => {
                    if (onSelectTemplate) {
                      onSelectTemplate(template);
                    } else {
                      navigate(`/meal-planner?template=${template.id}`);
                    }
                  }}
                >
                  <Calendar className="h-4 w-4 mr-2" />
                  Áp dụng thực đơn
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => navigate(`/meal-planner?template=${template.id}&tab=shopping`)}
                >
                  <ShoppingCart className="h-4 w-4 mr-2" />
                  Xem list mua
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {filteredTemplates.length === 0 && (
        <Card>
          <CardContent className="p-8 text-center">
            <ChefHat className="h-16 w-16 text-gray-300 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Không tìm thấy thực đơn phù hợp
            </h3>
            <p className="text-gray-600">
              Thử điều chỉnh bộ lọc để tìm thực đơn khác
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

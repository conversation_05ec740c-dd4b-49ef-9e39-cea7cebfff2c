# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

node_modules
dist
dist-ssr
*.local

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Debug and test files
debug-*.html
debug-*.tsx
debug-*.js
test-*.js
*-debug.js
*-test.html
REMOVE_MEAL_FIX_SUMMARY.md

# Scripts directory (development only)
scripts/

# Public debug files
public/cors-test-result.html
public/debug-cors.html
public/test-cors-fix.html

# Development components (keep essential ones)
src/components/TestAddMeal.tsx
src/components/QuickConnectionTest.tsx
src/components/ErrorBoundary.tsx

# Debug pages
src/pages/CookingAndShoppingDemo.tsx

# Config files for development
src/config/browser-supabase.js
src/config/supabase-node.ts

# Adapter files (keep only essential ones)
src/services/adapters/SupabaseAdapterNode.ts

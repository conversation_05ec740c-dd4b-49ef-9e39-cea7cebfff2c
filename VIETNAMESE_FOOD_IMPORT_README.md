# 🍜 Hệ Thống Import Món Ăn Việt Nam

Hệ thống tự động cào dữ liệu và import các món ăn Việt Nam vào database, với khả năng phân biệt rõ ràng giữa **món ăn sáng/ngoài hàng** (phở, bún, hủ tiếu...) và **món ăn cơm hàng ngày**.

## 🎯 Tính Năng Chính

### 📊 Phân Loại Món Ăn Thông Minh
- **Món ăn sáng/ngoài hàng**: Phở, bún, hủ tiếu, mì, bánh mì, cháo, xôi
- **Món ăn cơm hàng ngày**: <PERSON><PERSON> ch<PERSON> thị<PERSON>, cá, tôm cua, canh, rau củ, món kho/rim
- **Món ăn vặt/tráng miệng**: <PERSON><PERSON><PERSON> x<PERSON>, chè, b<PERSON>h ngọt

### 🌍 Phân Chia Theo Vùng <PERSON>
- **Miền <PERSON>**: Phở b<PERSON>, b<PERSON> chả, chả cá Lã V<PERSON>ng
- **Miền Trung**: <PERSON><PERSON> b<PERSON>, <PERSON><PERSON>, ca<PERSON> l<PERSON>i An
- **Miền Nam**: Hủ tiếu Nam Vang, bánh xèo, cơm tấm
- **Toàn quốc**: Các món phổ biến khắp cả nước

### 🕷️ Cào Dữ Liệu Từ Nhiều Nguồn
- **Cooky.vn**: Công thức nấu ăn phong phú
- **Foody.vn**: Đánh giá và review món ăn
- **MonNgon.vn**: Hướng dẫn nấu ăn chi tiết

## 🚀 Cài Đặt và Sử Dụng

### 1. Cài Đặt Dependencies
```bash
npm install
```

### 2. Cấu Hình Database
Đảm bảo Supabase đã được cấu hình đúng trong `src/config/supabase.ts`

### 3. Chạy Import

#### Import Dữ Liệu Local (Nhanh, Ổn Định)
```bash
npm run import-vietnamese-food:local
```

#### Cào Dữ Liệu Từ Web (Chậm, Có Thể Lỗi)
```bash
npm run import-vietnamese-food:scraping
```

#### Import Kết Hợp (Khuyến Nghị)
```bash
npm run import-vietnamese-food:mixed
```

#### Xem Tùy Chọn Khác
```bash
npm run import-vietnamese-food:help
```

### 4. Tùy Chọn Nâng Cao

#### Xóa Dữ Liệu Cũ Trước Khi Import
```bash
npm run import-vietnamese-food -- --clear --source local
```

#### Import Từ Nguồn Cụ Thể
```bash
npm run import-vietnamese-food -- --source scraping --sources "Cooky.vn,Foody.vn"
```

#### Chế Độ Dry Run (Xem Trước)
```bash
npm run import-vietnamese-food -- --dry-run --verbose
```

#### Cập Nhật Món Đã Có
```bash
npm run import-vietnamese-food -- --update-existing --verbose
```

## 📁 Cấu Trúc File

```
src/
├── data/
│   ├── vietnameseFoodCategories.ts    # Định nghĩa phân loại món ăn
│   └── vietnameseFoodData.ts          # Dữ liệu mẫu món ăn Việt Nam
├── services/
│   ├── VietnameseFoodScrapingService.ts    # Service cào dữ liệu
│   └── VietnameseFoodImportService.ts      # Service import vào DB
├── components/
│   └── VietnameseFoodImporter.tsx     # UI component quản lý import
└── tests/
    └── vietnameseFoodImport.test.ts   # Test suite validation

scripts/
└── importVietnameseFood.ts            # Script CLI import
```

## 🧪 Chạy Tests

### Test Validation Dữ Liệu
```bash
npm run test
```

### Test UI Interactive
```bash
npm run test:ui
```

### Test Cụ Thể
```bash
npm run test -- vietnameseFoodImport.test.ts
```

## 📊 Dữ Liệu Mẫu

### Món Ăn Sáng/Ngoài Hàng (5 món)
- **Phở bò Hà Nội**: Phở truyền thống với nước dùng trong vắt
- **Bún chả Hà Nội**: Bún chả nướng than hồng với nước mắm chua ngọt
- **Bún bò Huế**: Bún bò cay nồng đặc trưng miền Trung
- **Bún riêu cua**: Bún riêu chua ngọt với cua đồng
- **Hủ tiếu Nam Vang**: Hủ tiếu trong vắt với tôm thịt

### Món Ăn Cơm Hàng Ngày (6 món)
- **Gà rang gừng**: Món gà thơm ngon đậm đà
- **Thịt kho trứng cút**: Món kho truyền thống thấm vị
- **Đậu phụ nhồi thịt**: Món chay bổ dưỡng tiết kiệm
- **Cá thu nướng lá chuối**: Món cá nướng thơm lừng
- **Canh sườn non hầm rau củ**: Canh thanh mát bổ dưỡng
- **Rau muống xào tỏi**: Món rau nhanh gọn

### Món Ăn Vặt/Tráng Miệng (2 món)
- **Bánh xèo**: Bánh giòn rụm với nhân tôm thịt
- **Chè ba màu**: Chè mát lạnh đa sắc màu

## 🔧 API Reference

### VietnameseFoodImportService

```typescript
// Import dữ liệu local
const result = await vietnameseFoodImportService.importLocalData({
  batchSize: 10,
  validateData: true,
  skipDuplicates: true,
  updateExisting: false
});

// Import dữ liệu scraped
const result = await vietnameseFoodImportService.importScrapedData(
  ['Cooky.vn', 'Foody.vn'],
  options
);

// Lấy thống kê
const stats = await vietnameseFoodImportService.getImportStats();

// Xóa dữ liệu
const clearResult = await vietnameseFoodImportService.clearImportedData();
```

### VietnameseFoodScrapingService

```typescript
// Cào từ một nguồn
const result = await vietnameseFoodScrapingService.scrapeFromSource('Cooky.vn', {
  maxPages: 2,
  delay: 1000
});

// Cào từ tất cả nguồn
const results = await vietnameseFoodScrapingService.scrapeFromAllSources();

// Lấy danh sách nguồn hỗ trợ
const sources = vietnameseFoodScrapingService.getSupportedSources();
```

## 🎨 Sử Dụng UI Component

```tsx
import { VietnameseFoodImporter } from './components/VietnameseFoodImporter';

function AdminPage() {
  return (
    <div>
      <h1>Quản Lý Dữ Liệu</h1>
      <VietnameseFoodImporter />
    </div>
  );
}
```

## 📈 Monitoring và Logs

### Xem Tiến Trình Import
```bash
npm run import-vietnamese-food:mixed -- --verbose
```

### Kiểm Tra Thống Kê Database
```bash
npm run import-vietnamese-food -- --dry-run --verbose
```

## ⚠️ Lưu Ý Quan Trọng

### 🔒 Bảo Mật
- Không commit API keys vào git
- Sử dụng environment variables cho sensitive data
- Rate limiting khi cào dữ liệu để tránh bị block

### 🚀 Performance
- Import theo batch để tránh timeout
- Sử dụng dữ liệu local cho development
- Cache kết quả scraping khi có thể

### 🧪 Testing
- Luôn test trên dữ liệu mẫu trước
- Sử dụng dry-run để xem trước kết quả
- Backup database trước khi import lớn

## 🐛 Troubleshooting

### Lỗi Kết Nối Database
```bash
# Kiểm tra cấu hình Supabase
npm run test -- --grep "connection"
```

### Lỗi Validation Dữ Liệu
```bash
# Chạy validation test
npm run test -- --grep "validation"
```

### Lỗi Scraping
```bash
# Test scraping service
npm run test -- --grep "scraping"
```

## 🤝 Đóng Góp

1. Fork repository
2. Tạo feature branch
3. Thêm tests cho tính năng mới
4. Chạy test suite đầy đủ
5. Tạo Pull Request

## 📄 License

MIT License - Xem file LICENSE để biết thêm chi tiết.
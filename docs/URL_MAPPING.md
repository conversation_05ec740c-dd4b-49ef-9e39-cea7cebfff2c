# URL Mapping - Chuẩn Hóa Toàn Bộ Thành Tiếng Anh

## 🎯 Mục Tiêu
Đồng bộ tất cả URL thành tiếng Anh để nhất quán và dễ maintain.

## 📋 URL Mapping Table

### ✅ Đã Chuẩn Hóa

| Chức Năng | URL Cũ (Tiếng Việt) | URL Mới (Tiếng Anh) | Status |
|-----------|---------------------|---------------------|---------|
| Trang chủ | `/` | `/` | ✅ |
| Kế hoạch nấu ăn | `/ke-hoach-nau-an` | `/meal-planner` | ✅ Redirect |
| Thực đơn mẫu | `/thuc-don-mau` | `/meal-templates` | ✅ Redirect |
| Món ăn Việt Nam | `/mon-an-viet-nam` | `/vietnamese-food` | ✅ Redirect |
| Thực đơn | `/thuc-don` | `/meal-plans` | ✅ Redirect |
| Bếp | `/bep` | `/kitchen` | ✅ Redirect |
| Thư viện công thức | `/recipes-library` | `/recipes` | ✅ |
| Thực đơn hàng ngày | `/daily-menu` | `/meal-plans` | ✅ Redirect |
| Giỏ hàng | `/shopping-cart` | `/shopping-cart` | ✅ |
| Admin | `/admin` | `/admin` | ✅ |
| Quản lý hàng loạt | - | `/admin/bulk-management` | ✅ |

### 🔄 Redirects Được Thiết Lập

```typescript
// Trong App.tsx
<Route path="/ke-hoach-nau-an" element={<Navigate to="/meal-planner" replace />} />
<Route path="/thuc-don-mau" element={<Navigate to="/meal-templates" replace />} />
<Route path="/mon-an-viet-nam" element={<Navigate to="/vietnamese-food" replace />} />
<Route path="/thuc-don" element={<Navigate to="/meal-plans" replace />} />
<Route path="/bep" element={<Navigate to="/kitchen" replace />} />
<Route path="/daily-menu" element={<Navigate to="/meal-plans" replace />} />
```

## 🎨 Navigation Links Đã Cập Nhật

### Header.tsx
- ✅ `/ke-hoach-nau-an` → `/meal-planner`
- ✅ `/meal-planner` → `/meal-templates` (sửa logic)

### HeroSection.tsx
- ✅ `/meal-planning` → `/meal-templates`

### QuickAccessButtons.tsx
- ✅ `/recipes-library` → `/recipes`
- ✅ `/daily-menu` → `/meal-plans`

## 📱 URL Structure Mới

```
angiday.com/
├── /                          # Trang chủ
├── /meal-planner             # Lập kế hoạch bữa ăn
├── /meal-templates           # Thực đơn mẫu
├── /meal-plans              # Thực đơn hàng ngày
├── /vietnamese-food         # Khám phá món ăn Việt Nam
├── /recipes                 # Thư viện công thức
├── /kitchen                 # Kitchen Command Center
├── /shopping-cart           # Giỏ hàng thông minh
├── /admin/                  # Admin area
│   └── /bulk-management     # Quản lý hàng loạt
└── /cooking/                # Cooking modes
    ├── /step-by-step        # Nấu ăn từng bước
    └── /all-in-one         # Nấu ăn tổng hợp
```

## 🔍 SEO Benefits

### Trước (Lẫn Lộn)
```
/ke-hoach-nau-an          # Tiếng Việt
/meal-planner             # Tiếng Anh
/thuc-don                 # Tiếng Việt
/recipes-library          # Tiếng Anh
```

### Sau (Nhất Quán)
```
/meal-planner             # Tiếng Anh
/meal-templates           # Tiếng Anh
/meal-plans              # Tiếng Anh
/recipes                 # Tiếng Anh
```

## 🚀 Lợi Ích

1. **Consistency:** Tất cả URL đều tiếng Anh
2. **SEO Friendly:** Dễ index và remember
3. **International:** Sẵn sàng cho thị trường quốc tế
4. **Developer Friendly:** Dễ maintain và debug
5. **User Experience:** URL dễ nhớ và share

## 🔧 Implementation Details

### Redirects
- Sử dụng `<Navigate to="..." replace />` để redirect
- `replace` flag để không tạo history entry
- Giữ backward compatibility

### Link Updates
- Cập nhật tất cả `<Link to="...">` components
- Kiểm tra navigation menus
- Cập nhật breadcrumbs

## ✅ Checklist

- [x] Cập nhật App.tsx routes
- [x] Thêm redirects cho URL cũ
- [x] Cập nhật Header navigation
- [x] Cập nhật HeroSection links
- [x] Cập nhật QuickAccessButtons
- [x] Import Navigate component
- [ ] Kiểm tra các component khác
- [ ] Test tất cả redirects
- [ ] Cập nhật documentation

## 🧪 Testing

### Manual Testing
```bash
# Test redirects
http://localhost:8080/ke-hoach-nau-an → /meal-planner
http://localhost:8080/thuc-don-mau → /meal-templates
http://localhost:8080/mon-an-viet-nam → /vietnamese-food

# Test direct URLs
http://localhost:8080/meal-planner ✅
http://localhost:8080/meal-templates ✅
http://localhost:8080/vietnamese-food ✅
```

### Automated Testing
```typescript
// Có thể thêm tests cho routing
describe('URL Redirects', () => {
  test('Vietnamese URLs redirect to English', () => {
    // Test redirect logic
  });
});
```

## 📝 Notes

- Tất cả URL cũ vẫn hoạt động nhờ redirects
- Không breaking changes cho users
- SEO-friendly với proper redirects
- Sẵn sàng cho internationalization

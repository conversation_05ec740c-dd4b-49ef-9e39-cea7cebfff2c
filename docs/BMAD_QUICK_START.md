# BMAD-METHOD Quick Start Guide

## 🚀 Bắt Đ<PERSON> với BMAD-METHOD

### 1. <PERSON><PERSON><PERSON>ra Cà<PERSON> Đặt

Đảm bảo BMAD-METHOD đã được cài đặt thành công:

```bash
# Kiểm tra các thư mục đã được tạo
ls -la .bmad-core/
ls -la web-bundles/
ls -la .windsurf/rules/
```

### 2. Chọn Workflow

#### Cho Tính Năng Mới (Greenfield)
```bash
# Sử dụng greenfield workflow
cat .bmad-core/workflows/greenfield-fullstack.yaml
```

#### Cho Cải Tiến Hiện Tại (Brownfield)
```bash
# Sử dụng brownfield workflow  
cat .bmad-core/workflows/brownfield-fullstack.yaml
```

## 🎯 Ví Dụ Thực Tế: Thêm AI Recipe Suggestions

### Phase 1: Planning (Web UI)

#### Step 1: Analyst Research
```
1. Copy content từ: web-bundles/agents/analyst.txt
2. <PERSON>e vào <PERSON>tGPT/Claude
3. Prompt: "I want to add AI-powered recipe suggestions to Angiday Kitchen app. 
   Please research current trends in AI recipe recommendation systems."
```

#### Step 2: PM Create PRD
```
1. Copy content từ: web-bundles/agents/pm.txt
2. Prompt: "Based on the analyst research, create a PRD for AI Recipe Suggestions feature.
   Current app: React/TypeScript kitchen management app with Supabase backend."
```

#### Step 3: Architect Design
```
1. Copy content từ: web-bundles/agents/architect.txt
2. Prompt: "Create architecture for AI Recipe Suggestions feature.
   Current stack: React, TypeScript, Supabase, existing recipe database."
```

#### Step 4: PO Validation
```
1. Copy content từ: web-bundles/agents/po.txt
2. Prompt: "Validate the PRD and Architecture documents for alignment.
   Run the master checklist."
```

### Phase 2: Development (IDE)

#### Step 5: Document Sharding
```bash
# Trong Windsurf IDE
@po Please shard the PRD document into smaller files for development
@po Please shard the Architecture document into implementation-ready sections
```

#### Step 6: Story Creation
```bash
# Trong Windsurf IDE
@sm Create the first development story from the AI Recipe Suggestions epic
```

#### Step 7: Implementation
```bash
# Trong Windsurf IDE
@dev Implement the first story: "Setup AI recommendation service infrastructure"
```

#### Step 8: QA Review
```bash
# Trong Windsurf IDE
@qa Review the implemented AI recommendation infrastructure code
```

## 🛠️ Templates Sẵn Có

### PRD Template
```yaml
# .bmad-core/templates/prd-tmpl.yaml
product_name: "Angiday Kitchen - AI Recipe Suggestions"
version: "1.0"
sections:
  - overview
  - functional_requirements
  - non_functional_requirements
  - epics
  - stories
```

### Architecture Template
```yaml
# .bmad-core/templates/architecture-tmpl.yaml
system_name: "AI Recipe Recommendation System"
components:
  - frontend
  - backend_api
  - ai_service
  - database
```

### Story Template
```yaml
# .bmad-core/templates/story-tmpl.yaml
story_id: "ARS-001"
title: "Setup AI Recommendation Service"
description: "As a developer, I want to setup the AI recommendation service infrastructure"
acceptance_criteria:
  - Service endpoint created
  - Database schema updated
  - API integration tested
```

## 📋 Checklists Quan Trọng

### PM Checklist
```bash
# Kiểm tra PRD quality
cat .bmad-core/checklists/pm-checklist.md
```

### Architect Checklist
```bash
# Kiểm tra Architecture quality
cat .bmad-core/checklists/architect-checklist.md
```

### Story Definition of Done
```bash
# Kiểm tra story completion
cat .bmad-core/checklists/story-dod-checklist.md
```

## 🎮 Hands-on Exercise

### Bài Tập 1: Cải Tiến Supabase Connection

1. **Analyst Task**:
   ```
   Research current issues with Supabase connection in Angiday.
   Analyze fallback mechanisms and suggest improvements.
   ```

2. **PM Task**:
   ```
   Create PRD for "Enhanced Supabase Connection Reliability" feature.
   Include retry mechanisms, connection monitoring, auto-recovery.
   ```

3. **Architect Task**:
   ```
   Design improved adapter architecture with:
   - Connection pooling
   - Retry strategies  
   - Health monitoring
   - Graceful degradation
   ```

4. **SM Task**:
   ```
   Break down into development stories:
   - Story 1: Enhanced connection retry logic
   - Story 2: Connection health monitoring
   - Story 3: Improved error handling
   ```

### Bài Tập 2: Mobile-First Recipe View

1. **UX Expert Task**:
   ```
   Design mobile-first recipe viewing experience.
   Focus on touch interactions, readability, cooking mode.
   ```

2. **Architect Task**:
   ```
   Design responsive architecture:
   - Progressive Web App features
   - Offline recipe access
   - Touch-optimized interactions
   ```

## 🔧 IDE Integration Commands

### Windsurf Commands
```bash
# Activate specific agent
@analyst    # For research and analysis
@pm         # For product management
@architect  # For system design
@ux-expert  # For UI/UX design
@po         # For product ownership
@sm         # For scrum master tasks
@dev        # For development
@qa         # For quality assurance

# Team commands
@team-fullstack    # Full development team
@team-no-ui        # Backend-focused team
@team-ide-minimal  # Minimal IDE team
```

### Common Workflows
```bash
# Planning workflow
@analyst research [topic]
@pm create prd from [brief]
@architect design [system]
@po validate documents

# Development workflow  
@sm draft next story
@po validate story
@dev implement [story]
@qa review [implementation]
```

## 📊 Success Metrics

### Planning Phase
- [ ] PRD completed with all sections
- [ ] Architecture aligned with PRD
- [ ] PO master checklist passed
- [ ] Documents sharded for development

### Development Phase
- [ ] Stories follow template format
- [ ] Code passes all validations
- [ ] QA review completed
- [ ] Regression tests passing

## 🚨 Common Pitfalls

### 1. Skipping Planning Phase
❌ **Don't**: Jump straight to coding
✅ **Do**: Complete planning phase first

### 2. Poor Document Sharding
❌ **Don't**: Keep large monolithic documents
✅ **Do**: Shard into manageable pieces

### 3. Missing Context
❌ **Don't**: Work in isolation
✅ **Do**: Maintain context between agents

### 4. Skipping Validation
❌ **Don't**: Skip PO validation steps
✅ **Do**: Validate at each phase

## 🎯 Next Actions

1. **Read Full User Guide**: `.bmad-core/user-guide.md`
2. **Try Simple Feature**: Start with small enhancement
3. **Practice Agent Collaboration**: Use multiple agents for one feature
4. **Integrate with Existing Workflow**: Adapt to current Angiday processes

---

**Ready to accelerate your Angiday development with BMAD-METHOD!** 🚀

Bắt đầu với một tính năng nhỏ để làm quen, sau đó scale up cho các features lớn hơn.
